  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search memos..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Created">Created</SelectItem>
              <SelectItem value="Received">Received</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => fetchMemos(userBranchId || undefined)}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="flex items-center justify-center h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading memos...</span>
        </div>
      ) : filteredMemos.length === 0 ? (
        <Card className="p-6">
          <div className="flex flex-col items-center justify-center h-[200px]">
            <p className="text-muted-foreground">No memos found</p>
            {searchQuery && (
              <Button
                variant="link"
                onClick={() => setSearchQuery("")}
                className="mt-2"
              >
                Clear search
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredMemos.map((memo) => (
            <Card key={memo.memo_id} className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <h3 className="font-medium">{memo.memo_number}</h3>
                    <Badge variant={
                      memo.status === 'Completed' ? 'default' :
                      memo.status === 'Received' ? 'secondary' :
                      memo.status === 'Created' ? 'destructive' : 'default'
                    }>
                      {memo.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Vehicle: {memo.vehicle?.registration_number || 'N/A'} •
                      Drivers: {memo.driver_ids && Array.isArray(memo.driver_ids)
                        ? memo.driver_ids.length
                        : 0}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    From: {memo.from_branch?.name || 'N/A'} ({memo.from_branch?.code || 'N/A'}) →
                    To: {memo.to_branch?.name || 'N/A'} ({memo.to_branch?.code || 'N/A'})
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Created: {memo.created_at ? format(new Date(memo.created_at), 'dd MMM yyyy, HH:mm') : 'N/A'}
                    {memo.received_at && ` • Received: ${format(new Date(memo.received_at), 'dd MMM yyyy, HH:mm')}`}
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      fetchDriverNumbers(memo)
                      setIsViewDialogOpen(true)
                    }}
                  >
                    View Details
                  </Button>

                  {memo.status === 'Created' && (
                    <>
                      {/* Load button removed as requested */}
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => {
                          fetchDriverNumbers(memo)
                          setIsReceiveDialogOpen(true)
                          setFuelLitres("")
                        }}
                      >
                        <ArrowDownToLine className="mr-2 h-4 w-4" />
                        Receive
                      </Button>
                    </>
                  )}

                  {memo.status === 'Completed' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Format the WhatsApp message
                        const driverCount = memo.driver_ids && Array.isArray(memo.driver_ids)
                          ? memo.driver_ids.length
                          : 1;

                        let message = `Memo ${memo.memo_number} has been completed.\n` +
                          `From: ${memo.from_branch?.name || 'N/A'} → To: ${memo.to_branch?.name || 'N/A'}\n` +
                          `Vehicle: ${memo.vehicle?.registration_number || 'N/A'}\n` +
                          `Drivers: ${driverCount}\n` +
                          `Fuel Consumed: ${memo.fuel_consumed || 0} L\n` +
                          `BATA Amount: ₹${memo.bata_amount || 0}\n` +
                          `Salary Amount: ₹${memo.salary_amount || 0}\n` +
                          `Total Expense: ₹${memo.total_expense || 0}`;

                        // Add driver expense details if available
                        if (memo.driver_expenses && Array.isArray(memo.driver_expenses) && memo.driver_expenses.length > 0) {
                          message += "\n\nDriver Details:";
                          memo.driver_expenses.forEach((driver: any, index: number) => {
                            const driverNumber = driver.driver_number || memo.driver_numbers?.[index];
                            message += `\nDriver ${index + 1}${driverNumber ? ` (ID: ${driverNumber})` : ` (ID: ${driver.driver_id})`}:\n` +
                              `  BATA: ₹${driver.bata_amount || 0}\n` +
                              `  Salary: ₹${driver.salary_amount || 0}`;
                          });
                        }

                        // Encode the message for WhatsApp
                        const encodedMessage = encodeURIComponent(message);

                        // Open WhatsApp with the pre-filled message
                        window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                      }}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Send WhatsApp
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
