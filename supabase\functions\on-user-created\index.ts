// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create a Supabase client with the auth header
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth header of the user that called the function.
      { global: { headers: { Authorization: authHeader } } }
    )

    // Get the request body
    const { record } = await req.json()

    // Extract user data from the record
    const userId = record.id
    const userEmail = record.email
    const userData = record.user_metadata || {}
    const userName = userData.name || userData.full_name || 'User'

    // Create a new user record in the public users table
    const { data, error } = await supabaseClient
      .from('users')
      .insert([
        {
          id: userId,
          email: userEmail,
          name: userName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          role: 'user', // Default role
          is_active: true,
        },
      ])
      .select()

    if (error) {
      console.error('Error creating user record:', error)
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ success: true, data }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error: any) {
    console.error('Error processing request:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

/* To invoke:
  1. Go to your Supabase project dashboard
  2. Navigate to Database -> Webhooks
  3. Create a new webhook with the following settings:
     - Name: on-user-created
     - Table: auth.users
     - Events: INSERT
     - URL: https://your-project-ref.supabase.co/functions/v1/on-user-created
     - HTTP Method: POST
     - Enable: Yes
*/
