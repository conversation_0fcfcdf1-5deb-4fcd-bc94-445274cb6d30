-- Create cities table
CREATE TABLE IF NOT EXISTS public.cities (
  city_id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  state TEXT,
  country TEXT DEFAULT 'India',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add city_id column to branches table
ALTER TABLE public.branches ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES public.cities(city_id);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS branches_city_id_idx ON public.branches (city_id);

-- Insert initial cities
INSERT INTO public.cities (name, state) VALUES
  ('Chennai', 'Tamil Nadu'),
  ('Coimbatore', 'Tamil Nadu'),
  ('Madurai', 'Tamil Nadu'),
  ('Salem', 'Tamil Nadu'),
  ('Trichy', 'Tamil Nadu')
ON CONFLICT (name) DO NOTHING;

-- Update existing branches with city_id based on address
UPDATE public.branches
SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Chennai')
WHERE address LIKE '%Chennai%';

UPDATE public.branches
SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Coimbatore')
WHERE address LIKE '%Coimbatore%';

UPDATE public.branches
SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Madurai')
WHERE address LIKE '%Madurai%';

UPDATE public.branches
SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Salem')
WHERE address LIKE '%Salem%';

UPDATE public.branches
SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Trichy')
WHERE address LIKE '%Trichy%';

-- Comment on table and columns
COMMENT ON TABLE public.cities IS 'Cities where branches are located';
COMMENT ON COLUMN public.cities.city_id IS 'Primary key for the cities table';
COMMENT ON COLUMN public.cities.name IS 'Name of the city';
COMMENT ON COLUMN public.cities.state IS 'State where the city is located';
COMMENT ON COLUMN public.cities.country IS 'Country where the city is located';
COMMENT ON COLUMN public.branches.city_id IS 'Foreign key reference to the cities table';
