"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, AlertTriangle, Send, Phone, Clock, ChevronDown, ChevronUp } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"

// Demo data
const overdueParcels = [
  {
    id: "OVD001",
    lrn: "LRN123456",
    senderName: "Rajesh Kumar",
    senderPhone: "+91 98765 43210",
    recipientName: "Priya Sundaram",
    recipientPhone: "+91 87654 32109",
    daysOverdue: 35,
    lastUpdate: "2024-02-15T10:30:00",
    status: "pending_collection",
    escalationLevel: 1,
    reminders: [
      { date: "2024-03-10T14:30:00", type: "sms", status: "sent" },
      { date: "2024-03-15T11:20:00", type: "whatsapp", status: "delivered" }
    ]
  },
  {
    id: "OVD002",
    lrn: "LRN789012",
    senderName: "Karthik Raman",
    senderPhone: "+91 76543 21098",
    recipientName: "Lakshmi Venkatesh",
    recipientPhone: "+91 65432 10987",
    daysOverdue: 45,
    lastUpdate: "2024-02-05T15:45:00",
    status: "escalated",
    escalationLevel: 2,
    reminders: [
      { date: "2024-02-25T09:15:00", type: "sms", status: "sent" },
      { date: "2024-03-01T16:40:00", type: "whatsapp", status: "delivered" },
      { date: "2024-03-10T11:30:00", type: "call", status: "completed" }
    ]
  },
  {
    id: "OVD003",
    lrn: "LRN345678",
    senderName: "Anand Subramanian",
    senderPhone: "+91 54321 09876",
    recipientName: "Meena Krishnan",
    recipientPhone: "+91 43210 98765",
    daysOverdue: 60,
    lastUpdate: "2024-01-21T12:15:00",
    status: "critical",
    escalationLevel: 3,
    reminders: [
      { date: "2024-02-10T10:00:00", type: "sms", status: "sent" },
      { date: "2024-02-15T14:20:00", type: "whatsapp", status: "delivered" },
      { date: "2024-02-25T11:45:00", type: "call", status: "completed" },
      { date: "2024-03-05T16:30:00", type: "registered_mail", status: "sent" }
    ]
  }
]

const reminderFormSchema = z.object({
  message: z.string().min(10, "Message must be at least 10 characters"),
  sendTo: z.array(z.string()).min(1, "Select at least one recipient"),
  method: z.string().min(1, "Select a reminder method"),
})

const statusColors = {
  pending_collection: "bg-yellow-500",
  escalated: "bg-orange-500",
  critical: "bg-red-500"
}

const statusLabels = {
  pending_collection: "Pending Collection",
  escalated: "Escalated",
  critical: "Critical"
}

const escalationLevelColors = {
  1: "bg-yellow-500",
  2: "bg-orange-500",
  3: "bg-red-500"
}

export function OverdueParcelManagement() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedParcels, setSelectedParcels] = useState<string[]>([])
  const [isReminderDialogOpen, setIsReminderDialogOpen] = useState(false)
  const [expandedParcel, setExpandedParcel] = useState<string | null>(null)

  const form = useForm<z.infer<typeof reminderFormSchema>>({
    resolver: zodResolver(reminderFormSchema),
    defaultValues: {
      message: "",
      sendTo: [],
      method: "",
    },
  })

  const handleSendReminders = (values: z.infer<typeof reminderFormSchema>) => {
    toast({
      title: "Reminders Sent",
      description: `Successfully sent reminders to ${values.sendTo.length} recipients.`,
    })
    setIsReminderDialogOpen(false)
    form.reset()
    setSelectedParcels([])
  }

  const handleEscalate = (parcelId: string) => {
    toast({
      title: "Parcel Escalated",
      description: "The parcel has been escalated for supervisory review.",
    })
  }

  const toggleParcelExpansion = (parcelId: string) => {
    setExpandedParcel(expandedParcel === parcelId ? null : parcelId)
  }

  return (
    <div className="space-y-4">

      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by LRN or customer name..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending_collection">Pending Collection</SelectItem>
              <SelectItem value="escalated">Escalated</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Dialog open={isReminderDialogOpen} onOpenChange={setIsReminderDialogOpen}>
          <DialogTrigger asChild>
            <Button
              disabled={selectedParcels.length === 0}
              className="whitespace-nowrap"
            >
              <Send className="mr-2 h-4 w-4" />
              Send Reminders
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send Reminders</DialogTitle>
              <DialogDescription>
                Send reminders to selected parcel recipients
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSendReminders)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reminder Method</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select reminder method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="sms">SMS</SelectItem>
                          <SelectItem value="whatsapp">WhatsApp</SelectItem>
                          <SelectItem value="call">Phone Call</SelectItem>
                          <SelectItem value="email">Email</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sendTo"
                  render={() => (
                    <FormItem>
                      <FormLabel>Send To</FormLabel>
                      <div className="space-y-2">
                        {selectedParcels.map((parcelId) => {
                          const parcel = overdueParcels.find(p => p.id === parcelId)
                          if (!parcel) return null
                          return (
                            <div key={parcel.id} className="flex items-center space-x-2">
                              <Checkbox
                                checked={form.getValues("sendTo").includes(parcel.recipientPhone)}
                                onCheckedChange={(checked) => {
                                  const current = form.getValues("sendTo")
                                  if (checked) {
                                    form.setValue("sendTo", [...current, parcel.recipientPhone])
                                  } else {
                                    form.setValue("sendTo", current.filter(phone => phone !== parcel.recipientPhone))
                                  }
                                }}
                              />
                              <span className="text-sm">
                                {parcel.recipientName} ({parcel.recipientPhone})
                              </span>
                            </div>
                          )
                        })}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter reminder message..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="submit">Send Reminders</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {overdueParcels.map((parcel) => (
          <Card key={parcel.id} className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-1">
                <CardTitle className="text-base font-medium">
                  LRN: {parcel.lrn}
                </CardTitle>
                <CardDescription className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>{parcel.daysOverdue} days overdue</span>
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={statusColors[parcel.status as keyof typeof statusColors]}>
                  {statusLabels[parcel.status as keyof typeof statusLabels]}
                </Badge>
                <Badge variant="outline">
                  Level {parcel.escalationLevel}
                </Badge>
                <Checkbox
                  checked={selectedParcels.includes(parcel.id)}
                  onCheckedChange={(checked) => {
                    setSelectedParcels(prev =>
                      checked
                        ? [...prev, parcel.id]
                        : prev.filter(id => id !== parcel.id)
                    )
                  }}
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Sender</p>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{parcel.senderName}</span>
                    <span>•</span>
                    <span>{parcel.senderPhone}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Recipient</p>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{parcel.recipientName}</span>
                    <span>•</span>
                    <span>{parcel.recipientPhone}</span>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <Button
                  variant="ghost"
                  className="w-full justify-between p-0"
                  onClick={() => toggleParcelExpansion(parcel.id)}
                >
                  <span className="text-sm font-medium">
                    {expandedParcel === parcel.id ? "Hide Details" : "Show Details"}
                  </span>
                  {expandedParcel === parcel.id ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>

                {expandedParcel === parcel.id && (
                  <div className="mt-4 space-y-4">
                    <Separator />

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Reminder History</h4>
                      <div className="space-y-2">
                        {parcel.reminders.map((reminder, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between rounded-lg border p-2 text-sm"
                          >
                            <div className="flex items-center space-x-2">
                              {reminder.type === "sms" && <Send className="h-4 w-4" />}
                              {reminder.type === "whatsapp" && <Phone className="h-4 w-4" />}
                              {reminder.type === "call" && <Phone className="h-4 w-4" />}
                              <span className="capitalize">{reminder.type}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-muted-foreground">
                              <span>{new Date(reminder.date).toLocaleDateString()}</span>
                              <span>•</span>
                              <span className="capitalize">{reminder.status}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSelectedParcels([parcel.id])
                          setIsReminderDialogOpen(true)
                        }}
                      >
                        <Send className="mr-2 h-4 w-4" />
                        Send Reminder
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleEscalate(parcel.id)}
                      >
                        <AlertTriangle className="mr-2 h-4 w-4" />
                        Escalate
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}