import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/vehicles/validation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.vehicle_id && !body.registration_number) {
      return NextResponse.json(
        { error: "Missing required field: vehicle_id or registration_number" },
        { status: 400 },
      );
    }

    // Query to get vehicle details
    let query = supabase.from("vehicles").select("*");

    if (body.vehicle_id) {
      query = query.eq("vehicle_id", body.vehicle_id);
    } else if (body.registration_number) {
      // Normalize registration number by removing spaces
      const normalizedRegNumber = body.registration_number.replace(/\s+/g, "");

      // Try both the normalized version and the original with flexible matching
      query = query.or(
        `registration_number.ilike.${normalizedRegNumber},registration_number.ilike.%${body.registration_number}%`,
      );
    }

    const { data: vehicle, error } = await query.single();

    if (error || !vehicle) {
      return NextResponse.json({ error: "Vehicle not found" }, { status: 404 });
    }

    // Check if vehicle is active
    if (vehicle.current_status !== "Active") {
      return NextResponse.json({
        valid: false,
        vehicle_id: vehicle.vehicle_id,
        validation: {
          status_valid: false,
          fc_valid: false,
          insurance_valid: false,
          permit_valid: false,
          tax_valid: false,
        },
        message:
          `Vehicle is not active. Current status: ${vehicle.current_status}`,
      });
    }

    // Check if vehicle is already in an active memo
    const { data: activeMemos, error: memosError } = await supabase
      .from("memos")
      .select("memo_id, memo_number, status")
      .eq("vehicle_id", vehicle.vehicle_id)
      .eq("status", "Created")
      .order("created_at", { ascending: false });

    if (memosError) {
      console.error("Error checking active memos:", memosError);
    }

    if (activeMemos && activeMemos.length > 0) {
      return NextResponse.json({
        valid: false,
        message: `Vehicle is already in use in memo ${
          activeMemos[0].memo_number
        } (Status: ${activeMemos[0].status})`,
        vehicle_id: vehicle.vehicle_id,
        active_memos: activeMemos,
        validation: null,
      });
    }

    // Get current date
    const currentDate = new Date();

    // Validate FC, insurance, permit, and tax validity
    const fcValid = vehicle.fitness_expiry_date
      ? new Date(vehicle.fitness_expiry_date) > currentDate
      : false;
    const insuranceValid = vehicle.insurance_expiry
      ? new Date(vehicle.insurance_expiry) > currentDate
      : false;
    const permitValid = vehicle.permit_expiry_date
      ? new Date(vehicle.permit_expiry_date) > currentDate
      : false;
    const taxValid = true; // Assuming tax validity is not tracked in the vehicle table

    const validation = {
      status_valid: true,
      fc_valid: fcValid,
      insurance_valid: insuranceValid,
      permit_valid: permitValid,
      tax_valid: taxValid,
    };

    const allValid = Object.values(validation).every((value) => value === true);

    return NextResponse.json({
      valid: allValid,
      vehicle_id: vehicle.vehicle_id,
      validation,
      vehicle: {
        registration_number: vehicle.registration_number,
        vehicle_type: vehicle.vehicle_type,
        make_model: vehicle.make_model,
      },
      message: allValid
        ? "Vehicle validation successful"
        : "Vehicle validation failed. Please check the validation details.",
    });
  } catch (error: any) {
    console.error("Error in POST /api/vehicles/validation:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
