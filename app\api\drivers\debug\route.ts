import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/drivers/debug
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const phone = url.searchParams.get("phone") || "123123123"; // Default to the phone number you're trying
    const driverId = url.searchParams.get("id");
    const driverNumber = url.searchParams.get("driver_number");

    console.log("Debug search parameters:", { phone, driverId, driverNumber });

    // Get all drivers first
    const { data: allDrivers, error: allError } = await supabase
      .from("drivers")
      .select("*")
      .limit(10);

    console.log("All drivers:", allDrivers);

    // Try different search methods
    let results: Record<string, any> = {};

    // Search by phone_number
    if (phone) {
      const { data: phoneData, error: phoneError } = await supabase
        .from("drivers")
        .select("*")
        .eq("phone_number", phone);

      results.phoneSearch = {
        query: `phone_number = ${phone}`,
        found: phoneData && phoneData.length > 0,
        count: phoneData?.length || 0,
        error: phoneError?.message,
        data: phoneData,
      };
    }

    // Search by driver_number
    if (driverNumber) {
      const { data: driverNumData, error: driverNumError } = await supabase
        .from("drivers")
        .select("*")
        .eq("driver_number", driverNumber);

      results.driverNumberSearch = {
        query: `driver_number = ${driverNumber}`,
        found: driverNumData && driverNumData.length > 0,
        count: driverNumData?.length || 0,
        error: driverNumError?.message,
        data: driverNumData,
      };
    }

    // Search by driver_id
    if (driverId) {
      const { data: idData, error: idError } = await supabase
        .from("drivers")
        .select("*")
        .eq("driver_id", driverId);

      results.idSearch = {
        query: `driver_id = ${driverId}`,
        found: idData && idData.length > 0,
        count: idData?.length || 0,
        error: idError?.message,
        data: idData,
      };
    }

    // Try the OR query
    const searchValue = phone || driverNumber || driverId;
    if (searchValue) {
      const { data: orData, error: orError } = await supabase
        .from("drivers")
        .select("*")
        .or(`driver_number.eq.${searchValue},phone_number.eq.${searchValue}`);

      results.orSearch = {
        query: `driver_number.eq.${searchValue},phone_number.eq.${searchValue}`,
        found: orData && orData.length > 0,
        count: orData?.length || 0,
        error: orError?.message,
        data: orData,
      };
    }

    return NextResponse.json({
      message: "Debug information",
      allDrivers: allDrivers?.map((d) => ({
        driver_id: d.driver_id,
        name: d.name,
        phone_number: d.phone_number,
        driver_number: d.driver_number,
      })),
      results,
    });
  } catch (error: any) {
    console.error("Error in GET /api/drivers/debug:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error.message || String(error),
      },
      { status: 500 },
    );
  }
}
