import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// POST /api/migrations/driver-expenses
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is an admin
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("id", session.user.id)
      .single();

    if (userError || !user || user.role !== 'Admin') {
      return NextResponse.json({ error: "Only admins can run migrations" }, { status: 403 });
    }

    // Add driver_expenses_json column to memos table if it doesn't exist
    const { error: columnError } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_name = 'memos' AND column_name = 'driver_expenses_json'
          ) THEN
            ALTER TABLE public.memos ADD COLUMN driver_expenses_json JSONB;
          END IF;
        END
        $$;
      `
    });

    if (columnError) {
      console.error("Error adding driver_expenses_json column:", columnError);
      return NextResponse.json({ error: "Failed to add driver_expenses_json column" }, { status: 500 });
    }

    // Try to create driver_expenses table
    const { error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.driver_expenses (
          expense_id SERIAL PRIMARY KEY,
          memo_id INT REFERENCES memos(memo_id) ON DELETE CASCADE,
          driver_id INT NOT NULL,
          driver_number VARCHAR(50),
          bata_amount NUMERIC(10, 2) DEFAULT 0,
          salary_amount NUMERIC(10, 2) DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS driver_expenses_memo_id_idx ON driver_expenses(memo_id);
        CREATE INDEX IF NOT EXISTS driver_expenses_driver_id_idx ON driver_expenses(driver_id);
      `
    });

    if (tableError) {
      console.error("Error creating driver_expenses table:", tableError);
      return NextResponse.json({ error: "Failed to create driver_expenses table" }, { status: 500 });
    }

    return NextResponse.json({
      message: "Migration completed successfully",
    });
  } catch (error: any) {
    console.error("Error in POST /api/migrations/driver-expenses:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
