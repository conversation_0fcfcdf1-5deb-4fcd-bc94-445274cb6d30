// Script to create the cities table and update branches
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Cities to add
const cities = [
  { name: "Chennai", state: "Tamil Nadu", country: "India" },
  { name: "Coimbatore", state: "Tamil Nadu", country: "India" },
  { name: "Madurai", state: "Tamil Nadu", country: "India" },
  { name: "Salem", state: "Tamil Nadu", country: "India" },
  { name: "Trichy", state: "Tamil Nadu", country: "India" }
];

// Function to create the cities table
async function createCitiesTable() {
  try {
    console.log("Creating cities table...");

    // Create the cities table
    const { error: createTableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.cities (
          city_id SERIAL PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          state TEXT,
          country TEXT DEFAULT 'India',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );
      `
    });

    if (createTableError) {
      // If the exec_sql function doesn't exist, try direct SQL
      console.log("Error creating cities table with exec_sql, trying direct SQL...");
      
      // Try to create the table directly
      const { error: directError } = await supabase.from('cities').insert([
        { name: "Chennai", state: "Tamil Nadu", country: "India" }
      ]);

      if (directError && directError.code === '42P01') {
        console.error("Cannot create cities table. Please create it manually in the Supabase dashboard.");
        console.log("SQL to run in the Supabase SQL Editor:");
        console.log(`
          CREATE TABLE IF NOT EXISTS public.cities (
            city_id SERIAL PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            state TEXT,
            country TEXT DEFAULT 'India',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
          );
          
          -- Add city_id column to branches table
          ALTER TABLE public.branches ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES public.cities(city_id);
          
          -- Create index for faster lookups
          CREATE INDEX IF NOT EXISTS branches_city_id_idx ON public.branches (city_id);
        `);
        return false;
      }
    }

    // Add city_id column to branches table
    const { error: alterTableError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.branches ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES public.cities(city_id);
        CREATE INDEX IF NOT EXISTS branches_city_id_idx ON public.branches (city_id);
      `
    });

    if (alterTableError) {
      console.error("Error adding city_id column to branches table:", alterTableError);
    }

    return true;
  } catch (error) {
    console.error("Unexpected error creating cities table:", error);
    return false;
  }
}

// Function to add cities
async function addCities() {
  try {
    console.log("Adding cities...");

    for (const city of cities) {
      // Check if city already exists
      const { data: existingCity, error: checkError } = await supabase
        .from("cities")
        .select("city_id, name")
        .eq("name", city.name)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        console.error(`Error checking for city ${city.name}:`, checkError);
        continue;
      }

      if (existingCity) {
        console.log(`City ${city.name} already exists:`, existingCity);
        continue;
      }

      // Create the city
      const { data, error } = await supabase
        .from("cities")
        .insert([city])
        .select();

      if (error) {
        console.error(`Error creating city ${city.name}:`, error);
        continue;
      }

      console.log(`City ${city.name} created successfully:`, data[0]);
    }

    return true;
  } catch (error) {
    console.error("Unexpected error adding cities:", error);
    return false;
  }
}

// Function to update branches with city_id
async function updateBranchesWithCityId() {
  try {
    console.log("Updating branches with city_id...");

    // Get all branches
    const { data: branches, error: branchesError } = await supabase
      .from("branches")
      .select("*");

    if (branchesError) {
      console.error("Error fetching branches:", branchesError);
      return false;
    }

    // Get all cities
    const { data: citiesData, error: citiesError } = await supabase
      .from("cities")
      .select("*");

    if (citiesError) {
      console.error("Error fetching cities:", citiesError);
      return false;
    }

    // Update each branch with the appropriate city_id
    for (const branch of branches) {
      if (branch.city_id) {
        console.log(`Branch ${branch.name} already has city_id ${branch.city_id}`);
        continue;
      }

      // Try to determine city from address
      let cityName = null;
      for (const city of citiesData) {
        if (branch.address && branch.address.includes(city.name)) {
          cityName = city.name;
          break;
        }
      }

      if (!cityName) {
        console.log(`Could not determine city for branch ${branch.name}`);
        continue;
      }

      const city = citiesData.find(c => c.name === cityName);
      if (!city) {
        console.log(`City ${cityName} not found in database`);
        continue;
      }

      // Update branch with city_id
      const { data, error } = await supabase
        .from("branches")
        .update({ city_id: city.city_id })
        .eq("branch_id", branch.branch_id)
        .select();

      if (error) {
        console.error(`Error updating branch ${branch.name} with city_id:`, error);
        continue;
      }

      console.log(`Branch ${branch.name} updated with city_id ${city.city_id}`);
    }

    return true;
  } catch (error) {
    console.error("Unexpected error updating branches:", error);
    return false;
  }
}

async function main() {
  console.log("Starting script...");

  try {
    // Create cities table
    const tableCreated = await createCitiesTable();
    if (!tableCreated) {
      console.log("Skipping remaining steps due to table creation failure");
      return;
    }

    // Add cities
    await addCities();

    // Update branches with city_id
    await updateBranchesWithCityId();

    console.log("Script completed successfully!");
  } catch (error) {
    console.error("Error in main function:", error);
  }
}

main();
