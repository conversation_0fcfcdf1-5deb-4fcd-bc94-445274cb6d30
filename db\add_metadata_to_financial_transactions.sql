-- SQL script to add metadata column to financial_transactions table

-- Check if metadata column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'metadata'
    ) THEN
        -- Add metadata column as JSONB type
        ALTER TABLE financial_transactions ADD COLUMN metadata JSONB;
        
        -- Add an index on the metadata column for better performance
        CREATE INDEX IF NOT EXISTS idx_financial_transactions_metadata ON financial_transactions USING GIN (metadata);
        
        RAISE NOTICE 'Added metadata column to financial_transactions table';
    ELSE
        RAISE NOTICE 'metadata column already exists in financial_transactions table';
    END IF;
END $$;
