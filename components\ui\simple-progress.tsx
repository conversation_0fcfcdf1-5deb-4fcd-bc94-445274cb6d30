'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface SimpleProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number;
}

/**
 * A simpler alternative to the Radix UI Progress component
 * This component doesn't rely on Radix UI and should be more stable for builds
 */
const SimpleProgress = React.forwardRef<HTMLDivElement, SimpleProgressProps>(
  ({ className, value = 0, ...props }, ref) => {
    // Ensure value is between 0 and 100
    const clampedValue = Math.max(0, Math.min(100, value));
    
    return (
      <div
        ref={ref}
        className={cn(
          'relative h-4 w-full overflow-hidden rounded-full bg-secondary',
          className
        )}
        {...props}
      >
        <div
          className="h-full w-full flex-1 bg-primary transition-all"
          style={{ transform: `translateX(-${100 - clampedValue}%)` }}
        />
      </div>
    );
  }
);

SimpleProgress.displayName = 'SimpleProgress';

export { SimpleProgress };
