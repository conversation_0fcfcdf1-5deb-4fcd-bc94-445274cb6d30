"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Minus, Check, AlertCircle, Truck, Search, X, PackageOpen } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { getUserBranchIdAsync } from "@/lib/branch-utils"

interface VehicleMemo {
  memo_id: number
  memo_number: string
  status: string
  from_branch_id: number
  to_branch_id: number
  from_branch: {
    name: string
    code: string
  }
  to_branch: {
    name: string
    code: string
  }
  vehicle_id: number
  vehicle: {
    registration_number: string
    vehicle_type: string
  }
}

interface LREntry {
  lrNumber: string
  quantity: number
  isValid: boolean
  validationMessage?: string
  destination?: string
}

export function VehicleLoadingPanel() {
  const { toast } = useToast()
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [memo, setMemo] = useState<VehicleMemo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [step, setStep] = useState<"search" | "lr" | "confirmation" | "complete">("search")
  const [userBranchId, setUserBranchId] = useState<string | null>(null)
  const [suggestedLRs, setSuggestedLRs] = useState<any[]>([])

  // LR entries
  const [lrEntries, setLrEntries] = useState<LREntry[]>([])
  const [currentLR, setCurrentLR] = useState("")
  const [currentQuantity, setCurrentQuantity] = useState("1")
  const [isValidating, setIsValidating] = useState(false)

  // Loading chart
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loadingChartId, setLoadingChartId] = useState<string | null>(null)
  const [loadingChartNumber, setLoadingChartNumber] = useState<string | null>(null)

  // Fetch user's branch ID on component mount
  useEffect(() => {
    // Get branch ID from API
    const fetchBranchId = async () => {
      try {
        const branchId = await getUserBranchIdAsync();
        if (branchId !== null) {
          setUserBranchId(branchId.toString());
        } else {
          console.error('Failed to get branch ID');
          // Show a toast notification to inform the user
          toast({
            title: "Authentication Issue",
            description: "Could not determine your branch. Please try logging in again.",
            variant: "destructive",
          });
        }
      } catch (error: any) {
        console.error('Error fetching branch ID:', error);
      }
    };

    fetchBranchId();
  }, [])

  // Fetch suggested LRs for the destination branch
  const fetchSuggestedLRs = async (destinationBranchId: number) => {
    if (!userBranchId) return

    try {
      const response = await fetch('/api/parcels/validate-lr/branch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          destination_branch_id: destinationBranchId,
          user_branch_id: userBranchId
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuggestedLRs(data.parcels || [])
      } else {
        console.error('Error fetching suggested LRs:', data.error)
      }
    } catch (error: any) {
      console.error('Error fetching suggested LRs:', error)
    }
  }

  // Search for vehicle memo
  const searchVehicle = async () => {
    if (!vehicleNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setError(null)
    setMemo(null)

    try {
      // Build URL with user's branch ID
      const url = new URL('/api/memos/vehicle', window.location.origin)

      // Normalize vehicle number by removing spaces
      const normalizedVehicleNumber = vehicleNumber.trim().replace(/\s+/g, '')
      url.searchParams.append('registration_number', normalizedVehicleNumber)

      // Use the branch ID (either from API or hardcoded)
      const branchId = userBranchId || "1" // Fallback to hardcoded value
      url.searchParams.append('user_branch_id', branchId)

      console.log('Searching vehicle with URL:', url.toString())
      const response = await fetch(url.toString())
      const data = await response.json()
      console.log('Vehicle search response:', data)

      if (response.ok && data.memo) {
        if (data.memo.status !== "Created") {
          setError(`Vehicle has a memo with status "${data.memo.status}". Only memos with "Created" status can be used for loading.`)
        } else {
          setMemo(data.memo)
          // Fetch suggested LRs in the background
          fetchSuggestedLRs(data.memo.to_branch_id)
          setStep("lr")
        }
      } else if (response.status === 403 && data.wrong_branch) {
        // Special case: memo exists but is from another branch
        setError(data.error || "This vehicle has an active memo, but it's not from your branch")
      } else {
        setError(data.error || "No active memo found for this vehicle")
      }
    } catch (error: any) {
      console.error('Error searching vehicle:', error)
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  // Validate LR number against the database
  const validateLR = async (lrNumber: string): Promise<LREntry> => {
    setIsValidating(true)

    try {
      // Build query parameters
      const params = new URLSearchParams({
        lr_number: lrNumber
      });

      // Always check destination branch ID if memo exists
      // This ensures parcels can only be loaded if their destination matches the memo's destination
      if (memo) {
        params.append('destination_branch_id', memo.to_branch_id.toString());
      } else {
        // If no memo exists (which shouldn't happen in normal flow), show an error
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: false,
          validationMessage: 'No memo selected. Cannot validate destination.'
        };
      }

      // Add user branch ID if available
      if (userBranchId) {
        params.append('user_branch_id', userBranchId);
      }

      const response = await fetch(`/api/parcels/validate-lr?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: true,
          destination: data.destination_branch?.name || 'Unknown'
        }
      } else {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: false,
          validationMessage: data.message || 'Invalid LR number'
        }
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      return {
        lrNumber,
        quantity: parseInt(currentQuantity) || 1,
        isValid: false,
        validationMessage: 'Failed to validate LR number'
      }
    } finally {
      setIsValidating(false)
    }
  }

  // Add LR entry to the list
  const addLREntry = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    // Check if LR already exists in the list
    if (lrEntries.some(entry => entry.lrNumber === currentLR.trim())) {
      toast({
        title: "Duplicate LR",
        description: "This LR number is already in the list",
        variant: "destructive",
      })
      return
    }

    const validatedEntry = await validateLR(currentLR.trim())

    setLrEntries(prev => [...prev, validatedEntry])
    setCurrentLR("")
    setCurrentQuantity("1")
  }

  // Remove LR entry from the list
  const removeLREntry = (index: number) => {
    setLrEntries(prev => prev.filter((_, i) => i !== index))
  }

  // Generate loading chart
  const generateLoadingChart = async () => {
    if (!memo) {
      setError("No memo selected")
      return
    }

    // We'll use the to_branch_id from the memo as the destination

    if (lrEntries.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please add at least one LR number",
        variant: "destructive",
      })
      return
    }

    // Check if all LRs are valid
    const invalidLRs = lrEntries.filter(entry => !entry.isValid)
    if (invalidLRs.length > 0) {
      toast({
        title: "Invalid LR Numbers",
        description: `There are ${invalidLRs.length} invalid LR numbers in the list`,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/loading-charts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memo_id: memo.memo_id,
          vehicle_id: memo.vehicle_id,
          destination_branch_id: memo.to_branch_id,
          lr_entries: lrEntries.map(entry => ({
            lr_number: entry.lrNumber,
            quantity: entry.quantity
          }))
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setLoadingChartId(data.chart_id)
        setLoadingChartNumber(data.chart_number)
        setStep("complete")
        toast({
          title: "Loading Chart Created",
          description: `Successfully created loading chart #${data.chart_number}`,
        })
      } else {
        setError(data.error || 'Failed to create loading chart')
        toast({
          title: "Error",
          description: data.error || "Failed to create loading chart",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error creating loading chart:', error)
      setError('Failed to create loading chart')
      toast({
        title: "Error",
        description: "Failed to create loading chart. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset the form
  const resetForm = () => {
    setVehicleNumber("")
    setMemo(null)
    setError(null)
    setStep("search")
    setLrEntries([])
    setCurrentLR("")
    setCurrentQuantity("1")
    setLoadingChartId(null)
    setLoadingChartNumber(null)
  }

  // Handle key press in vehicle input
  const handleVehicleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      searchVehicle()
    }
  }

  // Handle key press in LR input
  const handleLRKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addLREntry()
    }
  }

  // Proceed to next step
  const proceedToNextStep = () => {
    if (step === "lr") {
      if (lrEntries.length === 0) {
        toast({
          title: "Missing Information",
          description: "Please add at least one LR number",
          variant: "destructive",
        })
        return
      }
      setStep("confirmation")
    }
  }

  // Go back to previous step
  const goBack = () => {
    if (step === "lr") {
      setStep("search")
    } else if (step === "confirmation") {
      setStep("lr")
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {step === "search" && (
        <Card className="w-full max-w-md mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Load Parcels</h2>
              <p className="text-muted-foreground">
                Enter the vehicle number to load parcels
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle-number">Vehicle Number</Label>
                <div className="flex space-x-2">
                  <Input
                    id="vehicle-number"
                    placeholder="Enter vehicle registration number"
                    value={vehicleNumber}
                    onChange={(e) => setVehicleNumber(e.target.value)}
                    onKeyDown={handleVehicleKeyPress}
                    disabled={isSearching}
                  />
                  <Button onClick={searchVehicle} disabled={isSearching || !vehicleNumber.trim()}>
                    {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Destination selection step removed */}

      {step === "lr" && memo && (
        <Card className="w-full max-w-2xl mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <PackageOpen className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Enter LR Numbers</h2>
              <p className="text-muted-foreground">
                Enter the LR numbers to load onto the vehicle
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Loading Details:</h3>
                <p className="text-sm">
                  <span className="font-medium">Vehicle:</span> {memo.vehicle.registration_number} ({memo.vehicle.vehicle_type})
                </p>
                <p className="text-sm">
                  <span className="font-medium">Memo:</span> {memo.memo_number}
                </p>
                <p className="text-sm">
                  <span className="font-medium">From:</span> {memo.from_branch.name} ({memo.from_branch.code})
                </p>
                <p className="text-sm">
                  <span className="font-medium">To:</span> {memo.to_branch.name} ({memo.to_branch.code})
                </p>
              </div>

              <div className="space-y-2">
                <Label>Enter LR Numbers</Label>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter LR number"
                    value={currentLR}
                    onChange={(e) => setCurrentLR(e.target.value)}
                    onKeyDown={handleLRKeyPress}
                    disabled={isValidating}
                  />
                  <Input
                    type="number"
                    placeholder="Qty"
                    value={currentQuantity}
                    onChange={(e) => setCurrentQuantity(e.target.value)}
                    className="w-20"
                    min="1"
                    disabled={isValidating}
                  />
                  <Button onClick={addLREntry} disabled={isValidating || !currentLR.trim()}>
                    {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Suggested LRs */}
              {suggestedLRs.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Suggested LRs for this destination</Label>
                    <Badge variant="outline" className="font-normal">
                      {suggestedLRs.length} available
                    </Badge>
                  </div>
                  <ScrollArea className="h-[120px] rounded-md border">
                    <div className="p-4 space-y-2">
                      {suggestedLRs.map((parcel) => (
                        <div
                          key={parcel.lr_number}
                          className="flex items-center justify-between p-2 border rounded-md cursor-pointer hover:bg-muted"
                          onClick={() => {
                            setCurrentLR(parcel.lr_number);
                            setCurrentQuantity(parcel.number_of_items?.toString() || "1");
                          }}
                        >
                          <div>
                            <p className="font-medium">{parcel.lr_number}</p>
                            <p className="text-xs text-muted-foreground">
                              To: {parcel.delivery_branch?.name || 'Unknown'} •
                              Items: {parcel.number_of_items || 1}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCurrentLR(parcel.lr_number);
                              setCurrentQuantity(parcel.number_of_items?.toString() || "1");
                              addLREntry();
                            }}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              <div className="space-y-2">
                <Label>LR Numbers to Load</Label>
                <ScrollArea className="h-[200px] rounded-md border">
                  <div className="p-4 space-y-2">
                    {lrEntries.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        No LR numbers added yet
                      </div>
                    ) : (
                      lrEntries.map((entry, index) => (
                        <Card key={index} className="p-3 flex items-center justify-between">
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium">{entry.lrNumber}</span>
                              <Badge
                                variant={entry.isValid ? "default" : "destructive"}
                                className="ml-2"
                              >
                                {entry.isValid ? "Valid" : "Invalid"}
                              </Badge>
                              <Badge variant="outline" className="ml-2">
                                Qty: {entry.quantity}
                              </Badge>
                            </div>
                            {entry.isValid ? (
                              <p className="text-xs text-muted-foreground">
                                Destination: {entry.destination}
                              </p>
                            ) : (
                              <p className="text-xs text-destructive">
                                {entry.validationMessage}
                              </p>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeLREntry(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </Card>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium">Total LRs: {lrEntries.length}</p>
                  <p className="text-sm text-muted-foreground">
                    Valid: {lrEntries.filter(entry => entry.isValid).length} |
                    Invalid: {lrEntries.filter(entry => !entry.isValid).length}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" onClick={goBack}>
                    Back
                  </Button>
                  <Button
                    onClick={proceedToNextStep}
                    disabled={lrEntries.length === 0}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {step === "confirmation" && memo && (
        <Card className="w-full max-w-2xl mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Confirm Loading</h2>
              <p className="text-muted-foreground">
                Review and confirm the loading details
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Loading Details:</h3>
                <p className="text-sm">
                  <span className="font-medium">Vehicle:</span> {memo.vehicle.registration_number} ({memo.vehicle.vehicle_type})
                </p>
                <p className="text-sm">
                  <span className="font-medium">Memo:</span> {memo.memo_number}
                </p>
                <p className="text-sm">
                  <span className="font-medium">From:</span> {memo.from_branch.name} ({memo.from_branch.code})
                </p>
                <p className="text-sm">
                  <span className="font-medium">To:</span> {memo.to_branch.name} ({memo.to_branch.code})
                </p>
                <p className="text-sm">
                  <span className="font-medium">Total LRs:</span> {lrEntries.length}
                </p>
              </div>

              <div className="space-y-2">
                <Label>LR Numbers to Load</Label>
                <ScrollArea className="h-[200px] rounded-md border">
                  <div className="p-4 space-y-2">
                    {lrEntries.map((entry, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b last:border-0">
                        <div>
                          <p className="font-medium">{entry.lrNumber}</p>
                          <p className="text-xs text-muted-foreground">
                            Destination: {entry.destination}
                          </p>
                        </div>
                        <Badge variant="outline">
                          Qty: {entry.quantity}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={goBack}>
                  Back
                </Button>
                <Button
                  onClick={generateLoadingChart}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Confirm Loading"
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {step === "complete" && loadingChartNumber && (
        <Card className="w-full max-w-md mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Loading Complete</h2>
              <p className="text-muted-foreground">
                The loading chart has been successfully created
              </p>
            </div>

            <div className="p-4 bg-muted rounded-md text-center">
              <h3 className="font-medium mb-2">Loading Chart #{loadingChartNumber}</h3>
              <p className="text-sm mb-2">
                Vehicle: {memo?.vehicle.registration_number}<br />
                Memo: {memo?.memo_number}<br />
                From: {memo?.from_branch.name} ({memo?.from_branch.code})<br />
                To: {memo?.to_branch.name} ({memo?.to_branch.code})<br />
                Total LRs: {lrEntries.length}
              </p>
            </div>

            <div className="flex justify-center space-x-2">
              <Button variant="outline" onClick={() => {
                // Format the WhatsApp message
                let message = `Loading Chart #${loadingChartNumber}\n` +
                  `Vehicle: ${memo?.vehicle.registration_number || 'N/A'}\n` +
                  `Memo: ${memo?.memo_number || 'N/A'}\n` +
                  `From: ${memo?.from_branch.name || 'N/A'} (${memo?.from_branch.code || 'N/A'})\n` +
                  `To: ${memo?.to_branch.name || 'N/A'} (${memo?.to_branch.code || 'N/A'})\n\n` +
                  `LR Numbers:\n`;

                lrEntries.forEach((entry, index) => {
                  message += `${index + 1}. ${entry.lrNumber} (Qty: ${entry.quantity})\n`;
                });

                // Encode the message for WhatsApp
                const encodedMessage = encodeURIComponent(message);

                // Open WhatsApp with the pre-filled message
                window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
              }}>
                Send via WhatsApp
              </Button>
              <Button onClick={resetForm}>
                Start New Loading
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
