'use client';

import { useEffect } from 'react';
import { useSupabase } from '@/contexts/supabase-provider';
import { checkAuthAndRedirect } from '@/lib/redirect-helper';

export function AuthChecker() {
  const { supabase } = useSupabase();

  // DISABLED to prevent redirection loops
  // useEffect(() => {
  //   // Check auth status on initial load
  //   checkAuthAndRedirect(supabase);
  //
  //   // Set up an interval to periodically check auth status
  //   const intervalId = setInterval(() => {
  //     checkAuthAndRedirect(supabase);
  //   }, 5000);
  //
  //   return () => clearInterval(intervalId);
  // }, [supabase]);

  // This component doesn't render anything
  return null;
}
