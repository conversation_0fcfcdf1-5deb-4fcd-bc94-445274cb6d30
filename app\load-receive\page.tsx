"use client"

import { DashboardShell } from "@/components/dashboard-shell"
import { LoadReceiveManagement } from "@/components/load-receive-management"
import { PageLayout } from "@/components/page-layout"

export default function LoadReceivePage() {
  return (
    <PageLayout
      title="Load & Receive"
      subtitle="Load parcels onto vehicles and receive parcels from vehicles"
    >
      <DashboardShell>
        <LoadReceiveManagement />
      </DashboardShell>
    </PageLayout>
  )
}
