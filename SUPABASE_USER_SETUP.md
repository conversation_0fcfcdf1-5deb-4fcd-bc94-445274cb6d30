# Supabase User Management Setup

This document provides instructions on how to set up user management with Supabase, including the users table and Edge Function for automatic user creation.

## Database Setup

### 1. Create the Users Table

You can run the SQL script in `supabase/migrations/20240601000000_create_users_table.sql` in the Supabase SQL Editor to create the users table and set up the necessary triggers and policies.

Alternatively, you can use the Supabase CLI to apply the migration:

```bash
supabase migration up
```

### 2. Edge Function Setup

The Edge Function in `supabase/functions/on-user-created/index.ts` will automatically create a record in the public users table when a new user is created in Supabase Auth.

To deploy the Edge Function:

1. Install the Supabase CLI if you haven't already:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Deploy the Edge Function:
   ```bash
   supabase functions deploy on-user-created
   ```

4. Set up a webhook in the Supabase Dashboard:
   - Go to your Supabase project dashboard
   - Navigate to Database -> Webhooks
   - Create a new webhook with the following settings:
     - Name: on-user-created
     - Table: auth.users
     - Events: INSERT
     - URL: https://your-project-ref.supabase.co/functions/v1/on-user-created
     - HTTP Method: POST
     - Enable: Yes

## User Management Features

The user management system includes the following features:

1. **User Creation**: When a user signs up, a record is automatically created in the public users table.
2. **User Authentication**: Users can sign in with their email and password.
3. **User Profile**: Each user has a profile with additional information like name, role, and branch.
4. **Last Login Tracking**: The system tracks when a user last logged in.
5. **Role-Based Access Control**: Users can have different roles (user, admin, etc.) with different permissions.
6. **Branch Assignment**: Users can be assigned to specific branches.

## Helper Functions

The `lib/user-helpers.ts` file provides helper functions for common user management operations:

- `getCurrentUser()`: Get the current authenticated user with profile data
- `getUserById(id)`: Get a user by ID
- `getUsers(filters)`: Get all users (with optional filtering)
- `updateUserProfile(id, updates)`: Update a user's profile
- `updateLastLogin()`: Update the current user's last login timestamp
- `assignUserToBranch(userId, branchId)`: Assign a user to a branch
- `changeUserRole(userId, role)`: Change a user's role
- `setUserActiveStatus(userId, isActive)`: Activate or deactivate a user

## Row Level Security (RLS) Policies

The users table has the following RLS policies:

1. Users can view their own profile
2. Users with 'admin' role can view all profiles
3. Users can update their own profile (except role and branch_id)
4. Admins can update any profile
5. Service role can insert new users (for the edge function)

## Database Schema

The users table has the following schema:

```sql
CREATE TABLE public.users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'user',
  branch_id UUID REFERENCES public.branches(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  last_login TIMESTAMP WITH TIME ZONE,
  avatar_url TEXT
);
```

## Troubleshooting

If you encounter issues with the Edge Function or user creation:

1. Check the Supabase logs for the Edge Function
2. Verify that the webhook is properly configured
3. Check that the RLS policies are correctly set up
4. Ensure that the users table has the correct schema and constraints
