"use client"

import { ExpenseManagement } from "@/components/expense-management"
import { PageLayout } from "@/components/page-layout"
import { DashboardShell } from "@/components/dashboard-shell"

export default function ExpensesPage() {
  return (
    <PageLayout
      title="Expense Management"
      subtitle="Track and manage branch expenses"
    >
      <DashboardShell>
        <ExpenseManagement />
      </DashboardShell>
    </PageLayout>
  )
}