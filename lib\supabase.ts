import { createClient } from "@supabase/supabase-js";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

// Initialize the Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL ||
  "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";

// Create a direct client for server-side operations or when cookies aren't available
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
  // Disable realtime subscriptions to avoid WebSocket issues
  realtime: {
    params: {
      eventsPerSecond: 0, // Disable realtime
    },
  },
});

// Create a browser client that uses cookies for auth
export function createBrowserClient() {
  return createClientComponentClient({
    supabaseUrl,
    supabaseKey: supabaseAnonKey,
    options: {
      // Disable realtime subscriptions to avoid WebSocket issues
      realtime: {
        params: {
          eventsPerSecond: 0, // Disable realtime
        },
      },
    },
  });
}

// Helper functions for common operations
export async function fetchData<T>(table: string, query: any = {}) {
  const { data, error } = await supabase
    .from(table)
    .select()
    .match(query);

  if (error) {
    console.error("Error fetching data:", error);
    return null;
  }

  return data as T[];
}

export async function insertData<T>(table: string, data: T) {
  const { data: result, error } = await supabase
    .from(table)
    .insert(data)
    .select();

  if (error) {
    console.error("Error inserting data:", error);
    return null;
  }

  return result;
}

export async function updateData<T>(
  table: string,
  id: string | number,
  data: Partial<T>,
) {
  const { data: result, error } = await supabase
    .from(table)
    .update(data)
    .eq("id", id)
    .select();

  if (error) {
    console.error("Error updating data:", error);
    return null;
  }

  return result;
}

export async function deleteData(table: string, id: string | number) {
  const { error } = await supabase
    .from(table)
    .delete()
    .eq("id", id);

  if (error) {
    console.error("Error deleting data:", error);
    return false;
  }

  return true;
}
