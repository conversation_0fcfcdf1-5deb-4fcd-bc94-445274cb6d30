'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { getParcels, Parcel } from '@/lib/db-helpers';
import { Package, RefreshCw } from 'lucide-react';

export function SupabaseDataExample() {
  const [parcels, setParcels] = useState<Parcel[] | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchParcels = async () => {
    setLoading(true);
    try {
      const data = await getParcels();
      setParcels(data);
    } catch (error: any) {
      console.error('Error fetching parcels:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch parcels. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchParcels();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-xl">Recent Parcels</CardTitle>
          <CardDescription>Parcels from Supabase database</CardDescription>
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={fetchParcels}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : parcels && parcels.length > 0 ? (
          <div className="space-y-2">
            {parcels.map((parcel) => (
              <div
                key={parcel.id}
                className="flex items-center justify-between rounded-lg border p-3"
              >
                <div className="flex items-center space-x-3">
                  <Package className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium">{parcel.lrn}</p>
                    <p className="text-sm text-muted-foreground">
                      {parcel.sender_name} → {parcel.recipient_name}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className="rounded-full px-2 py-1 text-xs font-medium bg-primary/10 text-primary">
                    {parcel.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Package className="h-10 w-10 text-muted-foreground mb-3" />
            <p className="text-muted-foreground">No parcels found</p>
            <p className="text-sm text-muted-foreground">
              Parcels will appear here once they are created
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
