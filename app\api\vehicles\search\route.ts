import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/vehicles/search
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const registration_number = url.searchParams.get("registration_number");

    if (!registration_number) {
      return NextResponse.json(
        { error: "Missing required parameter: registration_number" },
        { status: 400 },
      );
    }

    // Normalize registration number by removing spaces and converting to uppercase
    const normalizedRegNumber = registration_number.replace(/\s+/g, "")
      .toUpperCase();

    // Search for vehicle by registration number with flexible matching
    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select(`
        vehicle_id,
        registration_number,
        vehicle_type,
        make_model,
        capacity,
        current_status,
        branch_id,
        branch:branches(name, code)
      `)
      .or(
        `registration_number.ilike.${normalizedRegNumber},registration_number.ilike.%${registration_number}%`,
      )
      .single();

    if (vehicleError || !vehicle) {
      return NextResponse.json(
        { error: "Vehicle not found" },
        { status: 404 },
      );
    }

    // Check if vehicle is active
    if (vehicle.current_status !== "Active") {
      return NextResponse.json(
        {
          error:
            `Vehicle is not active. Current status: ${vehicle.current_status}`,
        },
        { status: 400 },
      );
    }

    // For receiving operations, we only need to validate that the vehicle exists and is active
    // No need to check for loading charts or memos
    return NextResponse.json({
      vehicle: {
        vehicle_id: vehicle.vehicle_id,
        registration_number: vehicle.registration_number,
        vehicle_type: vehicle.vehicle_type,
        make_model: vehicle.make_model,
        capacity: vehicle.capacity,
        current_status: vehicle.current_status,
        branch_id: vehicle.branch_id,
        branch: vehicle.branch,
      },
      available: true,
    });
  } catch (error: any) {
    console.error("Error in GET /api/vehicles/search:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
