// Script to check the structure of the users table
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkUsersTable() {
  try {
    // Get the structure of the users table
    console.log("Checking users table structure...");

    // Try to get a single row to see the structure
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .limit(1);

    if (error) {
      console.error("Error getting users table structure:", error);
    } else {
      console.log("Users table structure:", data);
    }

    // Try to insert a test user with minimal fields
    console.log("Attempting to insert a test user with minimal fields...");

    // Try different role values
    const roles = [
      "admin",
      "booking_operator",
      "loading_operator",
      "expense_handler",
      "vehicle_coordinator",
    ];

    for (const role of roles) {
      console.log(`Trying to insert user with role: ${role}`);

      const testUser = {
        email: `test.${role}@kpn.com`,
        name: `Test ${role}`,
        role: role,
      };

      const { data: insertData, error: insertError } = await supabase
        .from("users")
        .insert([testUser])
        .select();

      if (insertError) {
        console.error(
          `Error inserting test user with role ${role}:`,
          insertError,
        );
      } else {
        console.log(
          `Test user with role ${role} inserted successfully:`,
          insertData,
        );
        break; // Stop after first successful insertion
      }
    }

    const { data: insertData, error: insertError } = await supabase
      .from("users")
      .insert([testUser])
      .select();

    if (insertError) {
      console.error("Error inserting test user:", insertError);
    } else {
      console.log("Test user inserted successfully:", insertData);
    }

    // Try to get the auth user
    console.log("Checking auth user...");

    const { data: authData, error: authError } = await supabase.auth.getUser();

    if (authError) {
      console.error("Error getting auth user:", authError);
    } else {
      console.log("Auth user:", authData);
    }
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

checkUsersTable();
