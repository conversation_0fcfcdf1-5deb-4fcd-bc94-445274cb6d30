<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KPN Branch Management - Login</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    :root {
      --primary: #003A8C;
      --primary-hover: #002a66;
      --background: #f9f9f9;
      --card: #ffffff;
      --border: #e2e8f0;
      --muted: #64748b;
      --muted-foreground: #94a3b8;
      --destructive: #e11d48;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background);
      color: #1e293b;
      line-height: 1.5;
    }
    
    .container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }
    
    .card {
      background-color: var(--card);
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      width: 100%;
      max-width: 24rem;
      overflow: hidden;
    }
    
    .card-header {
      padding: 1.5rem;
      text-align: center;
    }
    
    .card-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: var(--primary);
    }
    
    .card-description {
      color: var(--muted);
      font-size: 0.875rem;
    }
    
    .card-content {
      padding: 1.5rem;
      border-top: 1px solid var(--border);
    }
    
    .card-footer {
      padding: 1rem 1.5rem;
      border-top: 1px solid var(--border);
      text-align: center;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
    
    input {
      width: 100%;
      padding: 0.5rem;
      font-size: 0.875rem;
      border: 1px solid var(--border);
      border-radius: 0.25rem;
      background-color: var(--card);
    }
    
    input:focus {
      outline: 2px solid var(--primary);
      outline-offset: 2px;
    }
    
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.25rem;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      cursor: pointer;
      transition: background-color 0.2s, color 0.2s;
      border: none;
      width: 100%;
    }
    
    .button-primary {
      background-color: var(--primary);
      color: white;
    }
    
    .button-primary:hover {
      background-color: var(--primary-hover);
    }
    
    .button-link {
      background-color: transparent;
      color: var(--primary);
      text-decoration: underline;
    }
    
    .alert {
      padding: 0.75rem;
      border-radius: 0.25rem;
      margin-bottom: 1rem;
      font-size: 0.875rem;
    }
    
    .alert-error {
      background-color: rgba(225, 29, 72, 0.1);
      color: var(--destructive);
    }
    
    .alert-success {
      background-color: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }
    
    .logo {
      width: 4rem;
      height: 4rem;
      margin: 0 auto 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--primary);
      color: white;
      border-radius: 0.5rem;
      font-size: 2rem;
      font-weight: bold;
    }
    
    .spinner {
      border: 2px solid rgba(0, 0, 0, 0.1);
      border-top-color: var(--primary);
      border-radius: 50%;
      width: 1rem;
      height: 1rem;
      animation: spin 1s linear infinite;
      margin-right: 0.5rem;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <div class="card-header">
        <div class="logo">KPN</div>
        <h1 class="card-title">Branch Management</h1>
        <p class="card-description">Sign in to your account</p>
      </div>
      <div class="card-content">
        <form id="login-form">
          <div id="alert" style="display: none;"></div>
          
          <div class="form-group">
            <label for="email">Email</label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              placeholder="<EMAIL>"
              value="<EMAIL>"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="password">Password</label>
            <input 
              type="password" 
              id="password" 
              name="password" 
              placeholder="••••••••"
              required
            />
          </div>
          
          <button type="submit" class="button button-primary" id="login-button">
            <span id="button-text">Sign in</span>
          </button>
        </form>
      </div>
      <div class="card-footer">
        <p id="session-info">Checking session...</p>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize Supabase client
      const supabaseUrl = 'https://nekjeqxlwhfwyekeinnc.supabase.co';
      const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y';
      
      if (typeof supabase === 'undefined') {
        showAlert('Supabase library not loaded. Please refresh the page.', 'error');
        return;
      }
      
      const client = supabase.createClient(supabaseUrl, supabaseKey);
      
      // DOM elements
      const loginForm = document.getElementById('login-form');
      const emailInput = document.getElementById('email');
      const passwordInput = document.getElementById('password');
      const loginButton = document.getElementById('login-button');
      const buttonText = document.getElementById('button-text');
      const sessionInfo = document.getElementById('session-info');
      
      // Check if user is already logged in
      checkSession();
      
      // Handle form submission
      loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = emailInput.value.trim();
        const password = passwordInput.value;
        
        if (!email || !password) {
          showAlert('Please enter both email and password', 'error');
          return;
        }
        
        // Show loading state
        setLoading(true);
        
        try {
          // Sign in with email and password
          const { data, error } = await client.auth.signInWithPassword({
            email,
            password
          });
          
          if (error) {
            showAlert(error.message, 'error');
            setLoading(false);
            return;
          }
          
          // Update login history
          try {
            const { data: userData, error: userError } = await client
              .from('users')
              .select('user_id')
              .eq('email', email)
              .single();
            
            if (!userError && userData) {
              await client
                .from('users')
                .update({
                  login_history: JSON.stringify({
                    last_login: new Date().toISOString(),
                    login_count: 1
                  })
                })
                .eq('user_id', userData.user_id);
            }
          } catch (err) {
            console.error('Error updating login history:', err);
          }
          
          // Show success message
          showAlert('Login successful! Redirecting...', 'success');
          
          // Redirect to dashboard
          setTimeout(() => {
            window.location.href = '/standalone-dashboard.html';
          }, 1500);
        } catch (err) {
          console.error('Unexpected error:', err);
          showAlert('An unexpected error occurred', 'error');
          setLoading(false);
        }
      });
      
      // Helper functions
      function showAlert(message, type) {
        const alertEl = document.getElementById('alert');
        alertEl.textContent = message;
        alertEl.className = type === 'error' ? 'alert alert-error' : 'alert alert-success';
        alertEl.style.display = 'block';
      }
      
      function setLoading(isLoading) {
        if (isLoading) {
          buttonText.innerHTML = '<span class="spinner"></span> Signing in...';
          loginButton.disabled = true;
        } else {
          buttonText.textContent = 'Sign in';
          loginButton.disabled = false;
        }
      }
      
      async function checkSession() {
        try {
          const { data, error } = await client.auth.getSession();
          
          if (error) {
            sessionInfo.textContent = 'Error checking session';
            return;
          }
          
          if (data.session) {
            sessionInfo.textContent = `Logged in as ${data.session.user.email}`;
            
            // Redirect to dashboard if already logged in
            setTimeout(() => {
              window.location.href = '/standalone-dashboard.html';
            }, 1500);
          } else {
            sessionInfo.textContent = 'Not logged in';
          }
        } catch (err) {
          console.error('Error checking session:', err);
          sessionInfo.textContent = 'Error checking session';
        }
      }
    });
  </script>
</body>
</html>
