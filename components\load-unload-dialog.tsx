"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Search, Plus, Minus, Check, Package } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { SimpleProgress } from "@/components/ui/simple-progress"

interface LoadUnloadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  mode: "load" | "receive"
  vehicle: {
    id: string
    type: string
    route: string
    parcels: Array<{
      lrn: string
      status: string
      type: string
      itemCount: number
    }>
  }
}

interface ItemConfirmation {
  confirmed: number
  isComplete: boolean
}

export function LoadUnloadDialog({ open, onOpenChange, mode, vehicle }: LoadUnloadDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedParcel, setSelectedParcel] = useState<string | null>(null)
  const [itemConfirmations, setItemConfirmations] = useState<Record<string, ItemConfirmation>>({})
  const { toast } = useToast()

  // Reset state when dialog opens/closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSelectedParcel(null)
      setItemConfirmations({})
    }
    onOpenChange(open)
  }

  const handleParcelSelect = (lrn: string, itemCount: number) => {
    setSelectedParcel(lrn)
    if (!itemConfirmations[lrn]) {
      setItemConfirmations(prev => ({
        ...prev,
        [lrn]: { confirmed: 0, isComplete: false }
      }))
    }
  }

  const adjustItemCount = (lrn: string, increment: boolean) => {
    const parcel = vehicle.parcels.find(p => p.lrn === lrn)
    if (!parcel) return

    setItemConfirmations(prev => {
      const current = prev[lrn]?.confirmed || 0
      const newCount = increment
        ? Math.min(current + 1, parcel.itemCount)
        : Math.max(0, current - 1)

      return {
        ...prev,
        [lrn]: {
          confirmed: newCount,
          isComplete: newCount === parcel.itemCount
        }
      }
    })
  }

  const handleConfirmAll = (lrn: string) => {
    const parcel = vehicle.parcels.find(p => p.lrn === lrn)
    if (!parcel) return

    setItemConfirmations(prev => ({
      ...prev,
      [lrn]: {
        confirmed: parcel.itemCount,
        isComplete: true
      }
    }))
  }

  const handleComplete = () => {
    const completedParcels = Object.entries(itemConfirmations).filter(([_, status]) => status.confirmed > 0)

    if (completedParcels.length === 0) {
      toast({
        title: "No items confirmed",
        description: "Please confirm at least one item before completing.",
        variant: "destructive"
      })
      return
    }

    const summaryMessages = completedParcels.map(([lrn, status]) => {
      const parcel = vehicle.parcels.find(p => p.lrn === lrn)
      return `LR ${lrn}: ${status.confirmed}/${parcel?.itemCount} items`
    })

    toast({
      title: `${mode === 'load' ? 'Loading' : 'Receiving'} Complete`,
      description: (
        <div className="mt-2 space-y-1">
          {summaryMessages.map((msg, i) => (
            <p key={i}>{msg}</p>
          ))}
        </div>
      )
    })

    handleOpenChange(false)
  }

  const filteredParcels = vehicle.parcels.filter(parcel =>
    parcel.lrn.toLowerCase().includes(searchQuery.toLowerCase()) &&
    parcel.status === "pending" &&
    parcel.type === mode
  )

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{mode === "load" ? "Load Parcels" : "Receive Parcels"}</DialogTitle>
          <DialogDescription>
            {mode === "load" ? "Load parcels onto" : "Receive parcels from"} vehicle {vehicle.id} on route {vehicle.route}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by LR..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <ScrollArea className="h-[400px] rounded-md border">
            <div className="p-4 space-y-4">
              {filteredParcels.map((parcel) => (
                <div
                  key={parcel.lrn}
                  className={`rounded-lg border p-4 transition-colors ${
                    selectedParcel === parcel.lrn ? 'border-primary' : ''
                  }`}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="font-medium">LR: {parcel.lrn}</p>
                      <p className="text-sm text-muted-foreground">
                        {parcel.itemCount} items to {mode === "load" ? "load" : "receive"}
                      </p>
                    </div>
                    {itemConfirmations[parcel.lrn]?.isComplete ? (
                      <Badge className="bg-green-500">
                        <Check className="mr-1 h-4 w-4" />
                        Complete
                      </Badge>
                    ) : (
                      <Badge variant="outline">
                        {itemConfirmations[parcel.lrn]?.confirmed || 0}/{parcel.itemCount} Items
                      </Badge>
                    )}
                  </div>

                  {selectedParcel === parcel.lrn ? (
                    <div className="space-y-4">
                      <SimpleProgress
                        value={(itemConfirmations[parcel.lrn]?.confirmed || 0) / parcel.itemCount * 100}
                      />

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustItemCount(parcel.lrn, false)}
                            disabled={!itemConfirmations[parcel.lrn]?.confirmed}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="w-12 text-center font-medium">
                            {itemConfirmations[parcel.lrn]?.confirmed || 0}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustItemCount(parcel.lrn, true)}
                            disabled={itemConfirmations[parcel.lrn]?.confirmed === parcel.itemCount}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <Button
                          variant="outline"
                          onClick={() => handleConfirmAll(parcel.lrn)}
                          disabled={itemConfirmations[parcel.lrn]?.isComplete}
                        >
                          Confirm All
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => handleParcelSelect(parcel.lrn, parcel.itemCount)}
                    >
                      <Package className="mr-2 h-4 w-4" />
                      Confirm Items
                    </Button>
                  )}
                </div>
              ))}
              {filteredParcels.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  No pending parcels found
                </div>
              )}
            </div>
          </ScrollArea>

          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {Object.values(itemConfirmations).reduce((acc, curr) => acc + curr.confirmed, 0)} items confirmed
            </p>
            <div className="space-x-2">
              <Button
                variant="outline"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleComplete}
                disabled={Object.keys(itemConfirmations).length === 0}
              >
                Complete {mode === "load" ? "Loading" : "Receiving"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}