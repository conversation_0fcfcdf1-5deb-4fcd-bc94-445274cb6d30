"use client"

import { useState, useEffect } from "react"
import { Search, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExpenseDetailsDialog } from "@/components/expense-details-dialog"
import { NewExpense } from "@/components/new-expense"
import { useUser } from "@/hooks/use-user"
import { useToast } from "@/hooks/use-toast"

const statusColors = {
  pending: "bg-yellow-500",
  approved: "bg-green-500",
  rejected: "bg-red-500"
}

const statusLabels = {
  pending: "Pending",
  approved: "Approved",
  rejected: "Rejected"
}

const categoryLabels = {
  fuel: "Fuel",
  maintenance: "Vehicle Maintenance",
  supplies: "Office Supplies",
  utilities: "Utilities",
  driver_allowance: "Driver Allowance",
  equipment: "Equipment",
  repairs: "Repairs",
  other: "Other"
}

const paymentMethodLabels = {
  cash: "Cash",
  corporate_card: "Corporate Card",
  bank_transfer: "Bank Transfer",
  upi: "UPI Payment"
}

interface Expense {
  id: number;
  category: string;
  amount: number;
  submitted_at: string;
  description: string;
  approval_status: string;
  vendor_name: string;
  invoice_number: string;
  payment_method: string;
  approved_by?: number;
  approved_at?: string;
  rejected_by?: number;
  rejected_at?: string;
  rejection_reason?: string;
  receipt_url?: string;
  memo_number?: string;
  branch_id: number;
  branch?: {
    name: string;
    code: string;
  };
  submitter?: {
    name: string;
  };
  approver?: {
    name: string;
  };
  rejecter?: {
    name: string;
  };
}

export function ExpenseList() {
  const { toast } = useToast()
  const { user, userDetails } = useUser()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("history")
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<string | null>(null)

  // Fetch expenses from API
  useEffect(() => {
    const fetchExpenses = async () => {
      setIsLoading(true)
      try {
        let url = '/api/expenses'
        const params = new URLSearchParams()

        if (statusFilter) {
          params.append('status', statusFilter)
        }

        if (userDetails?.branch_id) {
          params.append('branch_id', userDetails.branch_id.toString())
        }

        if (params.toString()) {
          url += `?${params.toString()}`
        }

        const response = await fetch(url)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch expenses')
        }

        setExpenses(data.expenses || [])
      } catch (error: any) {
        console.error('Error fetching expenses:', error)
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to load expenses',
          variant: 'destructive',
        })
        setExpenses([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchExpenses()
  }, [userDetails, statusFilter, toast])

  // Filter expenses based on search query
  const filteredExpenses = expenses.filter(expense => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      (expense.category && expense.category.toLowerCase().includes(query)) ||
      (expense.description && expense.description.toLowerCase().includes(query)) ||
      (expense.vendor_name && expense.vendor_name.toLowerCase().includes(query)) ||
      (expense.invoice_number && expense.invoice_number.toLowerCase().includes(query)) ||
      (expense.memo_number && expense.memo_number.toLowerCase().includes(query))
    )
  })

  return (
    <div className="space-y-4">
      <div className="flex border-b">
        <button
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'history' ? 'text-primary border-b-2 border-primary' : 'text-gray-500'}`}
          onClick={() => setActiveTab('history')}
        >
          Expense History
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'new' ? 'text-primary border-b-2 border-primary' : 'text-gray-500'}`}
          onClick={() => setActiveTab('new')}
        >
          New Expense
        </button>
      </div>

      {activeTab === 'history' && (
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search expenses..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant={statusFilter === null ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter(null)}
            >
              All
            </Button>
            <Button
              variant={statusFilter === "Pending" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Pending")}
            >
              Pending
            </Button>
            <Button
              variant={statusFilter === "Approved" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Approved")}
            >
              Approved
            </Button>
            <Button
              variant={statusFilter === "Rejected" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Rejected")}
            >
              Rejected
            </Button>
          </div>
        </div>
      )}

      {activeTab === 'history' ? (
        isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading expenses...</span>
          </div>
        ) : filteredExpenses.length === 0 ? (
          <div className="text-center py-12 border rounded-lg">
            <p className="text-muted-foreground">No expenses found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredExpenses.map((expense) => (
              <div key={expense.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="font-medium">
                      {expense.memo_number ? `MEMO: ${expense.memo_number}` : `EXP-${expense.id}`} -
                      {categoryLabels[expense.category as keyof typeof categoryLabels] || expense.category}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {new Date(expense.submitted_at).toLocaleDateString()}
                    </p>
                  </div>
                  <Badge className={statusColors[expense.approval_status.toLowerCase() as keyof typeof statusColors]}>
                    {statusLabels[expense.approval_status.toLowerCase() as keyof typeof statusLabels]}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-x-8 gap-y-2 mb-4">
                  <div>
                    <p className="text-sm font-medium">Amount</p>
                    <p className="text-sm">₹{expense.amount.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Vendor</p>
                    <p className="text-sm">{expense.vendor_name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Invoice Number</p>
                    <p className="text-sm">{expense.invoice_number}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Payment Method</p>
                    <p className="text-sm">
                      {paymentMethodLabels[expense.payment_method as keyof typeof paymentMethodLabels] || expense.payment_method}
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedExpense(expense)
                      setIsDetailsOpen(true)
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )
      ) : (
        <NewExpense />
      )}

      {selectedExpense && (
        <ExpenseDetailsDialog
          open={isDetailsOpen}
          onOpenChange={setIsDetailsOpen}
          expense={selectedExpense}
        />
      )}
    </div>
  )
}