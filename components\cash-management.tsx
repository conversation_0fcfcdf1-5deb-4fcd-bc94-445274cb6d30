"use client"

import { useState, useEffect } from "react"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { format } from "date-fns"

// UI Components
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog<PERSON>rigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Alert, AlertDescription } from "@/components/ui/alert"

// Icons
import {
  Search,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  CreditCard,
  CalendarIcon,
  Plus,
  ArrowRight,
  FileText,
  Banknote,
  CheckCircle2,
  XCircle,
  Clock,
  Building,
  Building2,
  Smartphone,
  Loader2,
  AlertCircle
} from "lucide-react"

// Other imports
import { useToast } from "@/hooks/use-toast"
import { useUser } from "@/hooks/use-user"
import { QRCodeSVG } from "qrcode.react"

// Type definitions
interface DailyStats {
  date: string;
  branch_id: number;
  total_collection: number;
  total_expenses: number;
  remitted: number;
  net_amount: number;
  transaction_count: number;
  expenses_breakdown: Record<string, number>;
  pending_collections: number;
}

interface Remittance {
  remittance_id: number;
  branch_id: number;
  total_collections: number | null;
  approved_expenses: number | null;
  float_balance: number | null;
  remittable_amount: number;
  method: string;
  proof_url: string | null;
  reference_id: string | null;
  status: string;
  submitted_by: number | null;
  created_at: string;
  branch?: {
    name: string;
    code: string;
  };
  submitter?: {
    name: string;
  };
}

interface BranchBalance {
  date: string;
  branch_id: number;
  opening_balance: number;
  income: number;
  expenses: number;
  remittances: number;
  closing_balance: number;
  pending_expenses: number;
  pending_remittances: number;
  ledger_entry: any | null;
}

const statusColors = {
  pending: "bg-yellow-500",
  submitted: "bg-yellow-500",
  approved: "bg-green-500",
  completed: "bg-green-500",
  rejected: "bg-red-500"
}

const statusLabels = {
  pending: "Pending",
  submitted: "Submitted",
  approved: "Approved",
  completed: "Completed",
  rejected: "Rejected"
}

const remittanceFormSchema = z.object({
  amount: z.string().min(1, { message: "Amount is required" }),
  method: z.string().min(1, { message: "Payment method is required" }),
  paymentDetails: z.object({
    transferType: z.string().optional(), // For NEFT/IMPS/RTGS
    senderAccount: z.string().optional(),
    receiverAccount: z.string().optional(),
    reference: z.string().optional(),
    screenshot: z.any().optional(), // For file upload
    challan: z.any().optional(), // For bank deposit challan
  }),
  notes: z.string().optional(),
})

const savedAccounts = [
  { id: "ACC001", number: "**********", bank: "SBI", branch: "Main Branch" },
  { id: "ACC002", number: "**********", bank: "HDFC", branch: "City Branch" },
]

const transferTypes = [
  { value: "neft", label: "NEFT" },
  { value: "imps", label: "IMPS" },
  { value: "rtgs", label: "RTGS" },
]

export function CashManagement() {
  const { toast } = useToast()
  const { user, userDetails } = useUser()
  const [searchQuery, setSearchQuery] = useState("")
  const [isRemittanceDialogOpen, setIsRemittanceDialogOpen] = useState(false)
  const [selectedDay, setSelectedDay] = useState<DailyStats | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)

  // State for API data
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([])
  const [remittances, setRemittances] = useState<Remittance[]>([])
  const [branchBalance, setBranchBalance] = useState<BranchBalance | null>(null)
  const [isSubmittingRemittance, setIsSubmittingRemittance] = useState(false)

  // Fetch data on component mount
  useEffect(() => {
    if (userDetails?.branch_id) {
      fetchBranchBalance(userDetails.branch_id)
      fetchDailyStats(userDetails.branch_id)
      fetchRemittances(userDetails.branch_id)
    }
  }, [userDetails])

  // Fetch branch balance
  const fetchBranchBalance = async (branchId: number) => {
    try {
      setError(null)
      const response = await fetch(`/api/branch-balance?branch_id=${branchId}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch branch balance')
      }

      const data = await response.json()
      setBranchBalance(data)
    } catch (error: any) {
      console.error('Error fetching branch balance:', error)
      setError(error.message || 'Failed to fetch branch balance')
      // Set default values to prevent UI errors
      setBranchBalance({
        date: new Date().toISOString().split('T')[0],
        branch_id: branchId,
        opening_balance: 0,
        income: 0,
        expenses: 0,
        remittances: 0,
        closing_balance: 0,
        pending_expenses: 0,
        pending_remittances: 0,
        ledger_entry: null
      })
    }
  }

  // Fetch daily stats
  const fetchDailyStats = async (branchId: number) => {
    setIsLoading(true)
    try {
      setError(null)
      const response = await fetch(`/api/branch-balance/daily?branch_id=${branchId}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch daily stats')
      }

      const data = await response.json()
      // Sort daily stats by date (newest first)
      const sortedStats = (data.daily_stats || []).sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime()
      })
      setDailyStats(sortedStats)
    } catch (error: any) {
      console.error('Error fetching daily stats:', error)
      setError(error.message || 'Failed to fetch daily stats')
      setDailyStats([])
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch remittances
  const fetchRemittances = async (branchId: number) => {
    try {
      setError(null)
      const response = await fetch(`/api/remittances?branch_id=${branchId}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || 'Failed to fetch remittances')
      }

      const data = await response.json()
      setRemittances(data.remittances || [])
    } catch (error: any) {
      console.error('Error fetching remittances:', error)
      setError(error.message || 'Failed to fetch remittances')
      setRemittances([])
    }
  }

  // Calculate totals
  const totalIncomeToday = branchBalance?.income || 0
  const totalExpensesToday = branchBalance?.expenses || 0
  const amountToRemitToday = branchBalance?.closing_balance || 0
  const totalOutstanding = branchBalance?.pending_remittances || 0
  const totalApprovedExpenses = branchBalance?.expenses || 0
  const totalUnapprovedExpenses = branchBalance?.pending_expenses || 0

  const form = useForm<z.infer<typeof remittanceFormSchema>>({
    resolver: zodResolver(remittanceFormSchema),
    defaultValues: {
      amount: amountToRemitToday.toString(),
      method: "",
      paymentDetails: {
        transferType: "",
        senderAccount: "",
        receiverAccount: "",
        reference: "",
        screenshot: undefined,
        challan: undefined,
      },
      notes: "Daily remittance",
    },
  })

  // Update form amount when amountToRemitToday changes
  useEffect(() => {
    form.setValue('amount', amountToRemitToday.toString())
  }, [amountToRemitToday, form])

  const handleRemittanceSubmit = async (values: z.infer<typeof remittanceFormSchema>) => {
    if (!userDetails?.branch_id) {
      toast({
        title: "Error",
        description: "Branch ID not found. Please try again.",
        variant: "destructive"
      })
      return
    }

    setIsSubmittingRemittance(true)

    try {
      // Prepare remittance data
      const remittanceData = {
        branch_id: userDetails.branch_id,
        remittable_amount: parseFloat(values.amount),
        method: values.method,
        reference_id: values.paymentDetails?.reference || null,
        notes: values.notes || null,
        approved_expenses: totalApprovedExpenses, // Include approved expenses
        total_collections: totalIncomeToday, // Include total collections
        // Add other fields as needed
      }

      // Submit remittance to API
      const response = await fetch('/api/remittances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(remittanceData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit remittance')
      }

      const data = await response.json()

      toast({
        title: "Remittance Submitted",
        description: `Remittance of ₹${values.amount} has been recorded and is pending approval.`
      })

      // Refresh remittances list
      fetchRemittances(userDetails.branch_id)
      // Refresh branch balance
      fetchBranchBalance(userDetails.branch_id)

      setIsRemittanceDialogOpen(false)
    } catch (error: any) {
      console.error('Error submitting remittance:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to submit remittance",
        variant: "destructive"
      })
    } finally {
      setIsSubmittingRemittance(false)
    }
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!userDetails?.branch_id && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You are not assigned to a branch. Please contact your administrator.
          </AlertDescription>
        </Alert>
      )}

      {/* Dashboard Cards */}
      <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branch Balance</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{branchBalance?.closing_balance.toLocaleString() || "0"}</div>
            <p className="text-xs text-muted-foreground">
              Current cash in branch
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Expenses</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalApprovedExpenses.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total approved expenses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Expenses</CardTitle>
            <XCircle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalUnapprovedExpenses.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Collections</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalIncomeToday.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total collections for {format(new Date(), "PP")}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-primary/5 border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">To Be Remitted</CardTitle>
            <Banknote className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{amountToRemitToday.toLocaleString()}</div>
            <div className="flex items-center justify-between mt-2">
              <p className="text-xs text-muted-foreground">
                Total outstanding: ₹{totalOutstanding.toLocaleString()}
              </p>
              <Button
                size="sm"
                className="h-8"
                onClick={() => setIsRemittanceDialogOpen(true)}
                disabled={!userDetails?.branch_id}
              >
                <Plus className="h-3.5 w-3.5 mr-1" />
                Remit
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different sections */}
      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">Daily Stats</TabsTrigger>
          <TabsTrigger value="remittances">Remittance History</TabsTrigger>
        </TabsList>

        {/* Daily Stats Tab */}
        <TabsContent value="daily" className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex items-center space-x-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {searchQuery ? format(new Date(searchQuery), "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={searchQuery ? new Date(searchQuery) : undefined}
                    onSelect={(date) => setSearchQuery(date ? format(date, "yyyy-MM-dd") : "")}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery("")}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading daily stats...</span>
            </div>
          ) : dailyStats.length === 0 ? (
            <div className="text-center py-12 border rounded-lg">
              <p className="text-muted-foreground">No daily stats found</p>
            </div>
          ) : (
            <div className="grid gap-6">
              {dailyStats
                .filter(day => !searchQuery || day.date === searchQuery)
                .map((day) => (
                  <Card key={day.date}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div>
                        <CardTitle className="text-base font-medium">
                          {format(new Date(day.date), "PPP")}
                        </CardTitle>
                        <CardDescription>
                          Net Amount: ₹{day.net_amount.toLocaleString()}
                        </CardDescription>
                      </div>
                      <Badge variant="outline">
                        {day.transaction_count} Transactions
                      </Badge>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Collections</p>
                          <p className="text-sm text-green-600">₹{day.total_collection.toLocaleString()}</p>
                          <p className="text-xs text-muted-foreground">
                            {day.pending_collections} pending collections
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Expenses Breakdown</p>
                          <div className="space-y-1">
                            {Object.entries(day.expenses_breakdown || {}).map(([type, amount]) => (
                              <div key={type} className="flex justify-between text-sm">
                                <span className="capitalize">{type}</span>
                                <span className="text-red-600">₹{(amount as number).toLocaleString()}</span>
                              </div>
                            ))}
                            {Object.keys(day.expenses_breakdown || {}).length === 0 && (
                              <p className="text-xs text-muted-foreground">No expenses recorded</p>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Button
                          onClick={() => {
                            setSelectedDay(day)
                            setIsDetailsDialogOpen(true)
                          }}
                        >
                          View Detailed Stats
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          )}
        </TabsContent>

        {/* Remittances Tab */}
        <TabsContent value="remittances" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Remittance History</h3>
            <Button
              onClick={() => setIsRemittanceDialogOpen(true)}
              disabled={!userDetails?.branch_id}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Remittance
            </Button>
          </div>

          {remittances.length === 0 ? (
            <div className="text-center py-12 border rounded-lg">
              <p className="text-muted-foreground">No remittances found</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {remittances.map((remittance) => (
                    <TableRow key={remittance.remittance_id}>
                      <TableCell className="font-medium">RMT-{remittance.remittance_id}</TableCell>
                      <TableCell>{format(new Date(remittance.created_at), "yyyy-MM-dd")}</TableCell>
                      <TableCell>₹{remittance.remittable_amount.toLocaleString()}</TableCell>
                      <TableCell>
                        {remittance.method === "bank_transfer" ? "Bank Transfer" :
                         remittance.method === "upi" ? "UPI Payment" :
                         remittance.method === "bank_deposit" ? "Bank Deposit" :
                         remittance.method}
                      </TableCell>
                      <TableCell>{remittance.reference_id || "-"}</TableCell>
                      <TableCell>
                        <Badge
                          variant="default"
                          className={statusColors[remittance.status.toLowerCase() as keyof typeof statusColors]}
                        >
                          {statusLabels[remittance.status.toLowerCase() as keyof typeof statusLabels]}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {remittance.proof_url ? (
                          <Button variant="ghost" size="sm" asChild>
                            <a href={remittance.proof_url} target="_blank" rel="noopener noreferrer">
                              <FileText className="h-4 w-4" />
                              <span className="sr-only">View Receipt</span>
                            </a>
                          </Button>
                        ) : (
                          <Button variant="ghost" size="sm" disabled>
                            <FileText className="h-4 w-4" />
                            <span className="sr-only">No Receipt</span>
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Daily Stats Detail Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[725px]">
          <DialogHeader>
            <DialogTitle>
              Daily Statistics - {selectedDay && format(new Date(selectedDay.date), "PPP")}
            </DialogTitle>
          </DialogHeader>

          {selectedDay && (
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-sm">Total Collections</CardTitle>
                    <div className="text-2xl font-bold text-green-600">
                      ₹{selectedDay.total_collection.toLocaleString()}
                    </div>
                  </CardHeader>
                </Card>

                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-sm">Total Expenses</CardTitle>
                    <div className="text-2xl font-bold text-red-600">
                      ₹{selectedDay.total_expenses.toLocaleString()}
                    </div>
                  </CardHeader>
                </Card>

                <Card>
                  <CardHeader className="p-4">
                    <CardTitle className="text-sm">Net Amount</CardTitle>
                    <div className="text-2xl font-bold">
                      ₹{selectedDay.net_amount.toLocaleString()}
                    </div>
                  </CardHeader>
                </Card>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium">Expense Breakdown</h4>
                {Object.keys(selectedDay.expenses_breakdown || {}).length === 0 ? (
                  <div className="text-center py-4 border rounded-lg">
                    <p className="text-muted-foreground">No expenses recorded for this day</p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Category</TableHead>
                          <TableHead className="text-right">Amount</TableHead>
                          <TableHead className="text-right">% of Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Object.entries(selectedDay.expenses_breakdown || {}).map(([category, amount]) => (
                          <TableRow key={category}>
                            <TableCell className="capitalize">{category}</TableCell>
                            <TableCell className="text-right">₹{(amount as number).toLocaleString()}</TableCell>
                            <TableCell className="text-right">
                              {selectedDay.total_expenses > 0
                                ? (((amount as number) / selectedDay.total_expenses) * 100).toFixed(1)
                                : "0"}%
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Remittance Dialog */}
      <Dialog open={isRemittanceDialogOpen} onOpenChange={setIsRemittanceDialogOpen}>
        <DialogContent className="sm:max-w-[525px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Remittance</DialogTitle>
            <DialogDescription>
              Submit a new remittance to head office. The suggested amount is based on today's collections minus expenses.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={form.handleSubmit(handleRemittanceSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Amount (₹)</Label>
              <Input
                id="amount"
                placeholder="Enter amount"
                {...form.register("amount")}
              />
              {form.formState.errors.amount && (
                <p className="text-sm text-red-500">{form.formState.errors.amount.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Payment Method</Label>
              <RadioGroup
                defaultValue={form.getValues("method")}
                onValueChange={(value) => {
                  form.setValue("method", value)
                  // Reset payment details when method changes
                  form.setValue("paymentDetails", {})
                }}
                className="grid grid-cols-2 gap-4"
              >
                <div>
                  <RadioGroupItem value="Bank Transfer" id="bank_transfer" className="peer sr-only" />
                  <Label
                    htmlFor="bank_transfer"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                  >
                    <Building2 className="mb-2 h-6 w-6" />
                    <span>Bank Transfer</span>
                  </Label>
                </div>

                <div>
                  <RadioGroupItem value="UPI" id="upi" className="peer sr-only" />
                  <Label
                    htmlFor="upi"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                  >
                    <Smartphone className="mb-2 h-6 w-6" />
                    <span>UPI Payment</span>
                  </Label>
                </div>

                <div>
                  <RadioGroupItem value="Bank Deposit" id="bank_deposit" className="peer sr-only" />
                  <Label
                    htmlFor="bank_deposit"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                  >
                    <Building className="mb-2 h-6 w-6" />
                    <span>Bank Deposit</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Bank Transfer Details */}
            {form.watch("method") === "Bank Transfer" && (
              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-2">
                  <Label>Transfer Type</Label>
                  <Select
                    onValueChange={(value) => form.setValue("paymentDetails.transferType", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select transfer type" />
                    </SelectTrigger>
                    <SelectContent>
                      {transferTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Sender Account Number</Label>
                  <Input
                    placeholder="Enter your account number"
                    {...form.register("paymentDetails.senderAccount")}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Receiver Account</Label>
                  <Select
                    onValueChange={(value) => form.setValue("paymentDetails.receiverAccount", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select receiver account" />
                    </SelectTrigger>
                    <SelectContent>
                      {savedAccounts.map((account) => (
                        <SelectItem key={account.id} value={account.number}>
                          {account.bank} - {account.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Reference Number</Label>
                  <Input
                    placeholder="Enter reference number"
                    {...form.register("paymentDetails.reference")}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Payment Screenshot</Label>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => form.setValue("paymentDetails.screenshot", e.target.files?.[0])}
                  />
                </div>
              </div>
            )}

            {/* UPI Payment */}
            {form.watch("method") === "UPI" && (
              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex flex-col items-center justify-center">
                  <div className="text-center mb-4">
                    <p className="text-sm font-medium mb-2">Scan QR Code to Pay</p>
                    <p className="text-xs text-muted-foreground mb-4">
                      Amount: ₹{form.watch("amount") || "0"}
                    </p>
                    <div className="bg-white p-4 rounded-lg inline-block">
                      <QRCodeSVG
                        value={`upi://pay?pa=example@upi&pn=Your%20Company&am=${form.watch("amount") || "0"}&cu=INR`}
                        size={200}
                        level="H"
                        includeMargin={true}
                      />
                    </div>
                  </div>
                  <div className="w-full space-y-2">
                    <Label>UPI Transaction ID</Label>
                    <Input
                      placeholder="Enter UPI Transaction ID"
                      {...form.register("paymentDetails.reference")}
                    />
                    <p className="text-xs text-muted-foreground">
                      Please enter the UPI transaction ID after completing the payment
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Bank Deposit */}
            {form.watch("method") === "Bank Deposit" && (
              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-2">
                  <Label>Deposit Account</Label>
                  <Select
                    onValueChange={(value) => form.setValue("paymentDetails.receiverAccount", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select deposit account" />
                    </SelectTrigger>
                    <SelectContent>
                      {savedAccounts.map((account) => (
                        <SelectItem key={account.id} value={account.number}>
                          {account.bank} - {account.number}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Challan Photo</Label>
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={(e) => form.setValue("paymentDetails.challan", e.target.files?.[0])}
                  />
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label>Notes (Optional)</Label>
              <Textarea
                placeholder="Add any additional notes"
                {...form.register("notes")}
              />
            </div>

            <DialogFooter className="mt-6">
              <Button
                type="submit"
                disabled={isSubmittingRemittance || !form.watch("method")}
              >
                {isSubmittingRemittance ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Submit Remittance"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
