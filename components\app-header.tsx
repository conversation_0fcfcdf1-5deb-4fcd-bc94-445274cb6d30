"use client"

import { MainNav } from '@/components/main-nav'
import { UserNav } from '@/components/user-nav'
import { HeaderActions } from '@/components/header-actions'

interface AppHeaderProps {
  onMobileNavToggle: () => void
  showActions?: boolean
}

export function AppHeader({ onMobileNavToggle, showActions = false }: AppHeaderProps) {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white text-primary shadow-sm">
      {/* Subtle brand accent */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-primary" />

      <div className="container relative z-10 flex h-12 items-center px-3 md:px-4">
        <MainNav onMobileNavToggle={onMobileNavToggle} />
        <div className="ml-auto flex items-center space-x-2">
          {showActions && <HeaderActions />}
          <UserNav />
        </div>
      </div>
    </header>
  )
}
