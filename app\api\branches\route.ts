import { NextRequest, NextResponse } from "next/server";
import { getBranches } from "@/lib/db-helpers";

// GET /api/branches
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filters: Record<string, any> = {};

    // Extract filter parameters
    if (searchParams.has("status")) {
      filters.status = searchParams.get("status");
    }

    if (searchParams.has("branch_type")) {
      filters.branch_type = searchParams.get("branch_type");
    }

    if (searchParams.has("city_id")) {
      filters.city_id = parseInt(searchParams.get("city_id") || "0");
    }

    // Get branches with filters
    const branches = await getBranches(filters);

    if (!branches) {
      return NextResponse.json({ error: "Failed to fetch branches" }, {
        status: 500,
      });
    }

    return NextResponse.json(branches);
  } catch (error: any) {
    console.error("Error in GET /api/branches:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
