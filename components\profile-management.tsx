"use client"

import { useState, useEffect } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { InputWithIcon } from "@/components/ui/input-with-icon"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { User, Lock, Bell, Building, Phone, Mail, Camera, Briefcase, Loader2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useSupabase } from "@/contexts/supabase-provider"
import { getCurrentUser, updateUserProfile } from "@/lib/user-helpers"

const profileFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string()
    .min(10, "Phone number must be exactly 10 digits")
    .max(10, "Phone number must be exactly 10 digits")
    .regex(/^\d{10}$/, "Phone number must contain only digits"),
  notifyBookings: z.boolean().default(true),
  notifyVehicles: z.boolean().default(true),
  notifyExpenses: z.boolean().default(true),
})

const passwordFormSchema = z.object({
  currentPassword: z.string().min(8, "Password must be at least 8 characters"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Password must be at least 8 characters"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export function ProfileManagement() {
  const { toast } = useToast()
  const { user: authUser, supabase } = useSupabase()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [userProfile, setUserProfile] = useState<any>(null)
  const [branchName, setBranchName] = useState<string>("")
  const [isUpdating, setIsUpdating] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)

  const profileForm = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      notifyBookings: true,
      notifyVehicles: true,
      notifyExpenses: true,
    },
  })

  const passwordForm = useForm<z.infer<typeof passwordFormSchema>>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  // Fetch user profile data
  useEffect(() => {
    async function fetchUserProfile() {
      if (!authUser?.email) return;

      setIsLoading(true);
      try {
        const profile = await getCurrentUser();

        if (profile) {
          setUserProfile(profile);

          // Set form default values
          profileForm.reset({
            name: profile.name || "",
            email: profile.email || "",
            phone: profile.phone || "",
            notifyBookings: profile.notification_preferences?.bookings || true,
            notifyVehicles: profile.notification_preferences?.vehicles || true,
            notifyExpenses: profile.notification_preferences?.expenses || true,
          });

          // Fetch branch name if branch_id exists
          if (profile.branch_id) {
            const { data: branchData } = await fetch(`/api/branches/${profile.branch_id}`)
              .then(res => res.json());

            if (branchData) {
              setBranchName(branchData.name || "Unknown Branch");
            }
          }
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
        toast({
          title: "Error",
          description: "Failed to load profile data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchUserProfile();
  }, [authUser, profileForm, toast]);

  async function onProfileSubmit(values: z.infer<typeof profileFormSchema>) {
    if (!userProfile?.user_id) return;

    setIsUpdating(true);
    try {
      // Prepare notification preferences
      const notification_preferences = {
        bookings: values.notifyBookings,
        vehicles: values.notifyVehicles,
        expenses: values.notifyExpenses,
      };

      // Update user profile
      const updated = await updateUserProfile(userProfile.user_id, {
        name: values.name,
        phone: values.phone,
        notification_preferences,
      });

      if (updated) {
        setUserProfile({
          ...userProfile,
          name: values.name,
          phone: values.phone,
          notification_preferences,
        });

        toast({
          title: "Profile Updated",
          description: "Your profile information has been updated successfully.",
        });
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update Failed",
        description: "There was a problem updating your profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
      setIsEditing(false);
    }
  }

  async function onPasswordSubmit(values: z.infer<typeof passwordFormSchema>) {
    setIsChangingPassword(true);
    try {
      // First verify the current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: userProfile.email,
        password: values.currentPassword,
      });

      if (signInError) {
        throw new Error("Current password is incorrect");
      }

      // Update the password
      const { error: updateError } = await supabase.auth.updateUser({
        password: values.newPassword,
      });

      if (updateError) {
        throw new Error(updateError.message);
      }

      toast({
        title: "Password Changed",
        description: "Your password has been changed successfully.",
      });

      passwordForm.reset();
    } catch (error: any) {
      console.error("Error changing password:", error);
      toast({
        title: "Password Change Failed",
        description: error.message || "There was a problem changing your password. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsChangingPassword(false);
    }
  }

  return (
    <div className="space-y-6">


      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">Loading profile data...</p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your personal information and contact details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={authUser?.user_metadata?.avatar_url || ""} />
                  <AvatarFallback>
                    {userProfile?.name
                      ? `${userProfile.name.split(' ')[0]?.[0] || ''}${userProfile.name.split(' ')[1]?.[0] || ''}`
                      : userProfile?.email?.[0]?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" disabled>
                    <Camera className="mr-2 h-4 w-4" />
                    Change Photo
                  </Button>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary">{userProfile?.role || "User"}</Badge>
                    {branchName && <Badge variant="outline">{branchName}</Badge>}
                  </div>
                </div>
              </div>

              {userProfile?.role === "Manager" && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Role Permissions:</span>
                  </div>
                  <ul className="ml-6 list-disc text-sm text-muted-foreground">
                    <li>Manage branch operations and staff</li>
                    <li>Approve expenses up to ₹10,000</li>
                    <li>View branch analytics and reports</li>
                    <li>Handle customer escalations</li>
                  </ul>
                </div>
              )}

              <Form {...profileForm}>
                <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
                  <FormField
                    control={profileForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <InputWithIcon
                            disabled={!isEditing}
                            {...field}
                            prefixIcon={<User className="h-4 w-4 text-muted-foreground" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <InputWithIcon
                            disabled={true} // Email cannot be changed
                            type="email"
                            {...field}
                            prefixIcon={<Mail className="h-4 w-4 text-muted-foreground" />}
                          />
                        </FormControl>
                        <FormDescription>
                          Email address cannot be changed
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={profileForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <InputWithIcon
                            disabled={!isEditing}
                            {...field}
                            prefixIcon={<Phone className="h-4 w-4 text-muted-foreground" />}
                            placeholder="10-digit phone number"
                            maxLength={10}
                            onChange={(e) => {
                              // Only allow digits
                              const value = e.target.value.replace(/\D/g, '');
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Enter a 10-digit phone number without spaces or special characters
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {branchName && (
                    <div className="flex items-center space-x-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">{branchName}</span>
                    </div>
                  )}

                  {isEditing ? (
                    <div className="flex justify-end space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                        disabled={isUpdating}
                      >
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isUpdating}>
                        {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Save Changes
                      </Button>
                    </div>
                  ) : (
                    <Button
                      type="button"
                      onClick={() => setIsEditing(true)}
                    >
                      Edit Profile
                    </Button>
                  )}
                </form>
              </Form>
            </CardContent>
          </Card>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Security</CardTitle>
                <CardDescription>Manage your password and security settings</CardDescription>
              </CardHeader>
              <CardContent>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="w-full">
                      <Lock className="mr-2 h-4 w-4" />
                      Change Password
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Change Password</DialogTitle>
                      <DialogDescription>
                        Enter your current password and choose a new one
                      </DialogDescription>
                    </DialogHeader>
                    <Form {...passwordForm}>
                      <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                        <FormField
                          control={passwordForm.control}
                          name="currentPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Current Password</FormLabel>
                              <FormControl>
                                <Input type="password" {...field} disabled={isChangingPassword} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={passwordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Password</FormLabel>
                              <FormControl>
                                <Input type="password" {...field} disabled={isChangingPassword} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={passwordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirm New Password</FormLabel>
                              <FormControl>
                                <Input type="password" {...field} disabled={isChangingPassword} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <DialogFooter>
                          <Button type="submit" disabled={isChangingPassword}>
                            {isChangingPassword && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            Update Password
                          </Button>
                        </DialogFooter>
                      </form>
                    </Form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>

            {/* <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>Choose what updates you want to receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Form {...profileForm}>
                  <div className="space-y-4">
                    <FormField
                      control={profileForm.control}
                      name="notifyBookings"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Booking Updates</FormLabel>
                            <FormDescription>
                              Receive notifications about new bookings and status changes
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                // Auto-save notification preferences when toggled
                                if (userProfile?.user_id) {
                                  const notification_preferences = {
                                    ...userProfile.notification_preferences,
                                    bookings: checked,
                                  };
                                  updateUserProfile(userProfile.user_id, { notification_preferences });
                                }
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="notifyVehicles"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Vehicle Alerts</FormLabel>
                            <FormDescription>
                              Get notified about vehicle arrivals and departures
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                // Auto-save notification preferences when toggled
                                if (userProfile?.user_id) {
                                  const notification_preferences = {
                                    ...userProfile.notification_preferences,
                                    vehicles: checked,
                                  };
                                  updateUserProfile(userProfile.user_id, { notification_preferences });
                                }
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="notifyExpenses"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Expense Notifications</FormLabel>
                            <FormDescription>
                              Receive updates about expense approvals and rejections
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                // Auto-save notification preferences when toggled
                                if (userProfile?.user_id) {
                                  const notification_preferences = {
                                    ...userProfile.notification_preferences,
                                    expenses: checked,
                                  };
                                  updateUserProfile(userProfile.user_id, { notification_preferences });
                                }
                              }}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </Form>
              </CardContent>
            </Card> */}
          </div>
        </div>
      )}
    </div>
  )
}