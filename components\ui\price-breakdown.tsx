"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Loader2 } from "lucide-react"

interface ChargeSettings {
  setting_id: number
  setting_name: string
  setting_type: 'percentage' | 'flat'
  setting_value: number
  description: string
  is_active: boolean
}

interface PriceBreakdownProps {
  basePrice: number
  showTitle?: boolean
  className?: string
  onTotalChange?: (total: number) => void
}

interface CalculatedCharges {
  loadingCharges: number
  vehicleCharges: number
  gst: number
  total: number
}

export function PriceBreakdown({ 
  basePrice, 
  showTitle = true, 
  className = "",
  onTotalChange 
}: PriceBreakdownProps) {
  const [chargeSettings, setChargeSettings] = useState<ChargeSettings[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch charge settings from API
  useEffect(() => {
    const fetchChargeSettings = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/charge-settings')
        
        if (!response.ok) {
          throw new Error('Failed to fetch charge settings')
        }
        
        const settings = await response.json()
        setChargeSettings(settings)
        setError(null)
      } catch (err: any) {
        console.error('Error fetching charge settings:', err)
        setError(err.message)
        // Use default settings if API fails
        setChargeSettings([
          {
            setting_id: 1,
            setting_name: 'loading_unloading_percentage',
            setting_type: 'percentage',
            setting_value: 10.00,
            description: 'Loading and unloading charges as percentage of base price',
            is_active: true
          },
          {
            setting_id: 2,
            setting_name: 'vehicle_charge_flat',
            setting_type: 'flat',
            setting_value: 40.00,
            description: 'Flat vehicle charge amount',
            is_active: true
          },
          {
            setting_id: 3,
            setting_name: 'gst_percentage',
            setting_type: 'percentage',
            setting_value: 5.00,
            description: 'GST percentage on base price',
            is_active: true
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchChargeSettings()
  }, [])

  // Calculate charges based on settings
  const calculateCharges = (): CalculatedCharges => {
    if (!chargeSettings.length || !basePrice) {
      return {
        loadingCharges: 0,
        vehicleCharges: 0,
        gst: 0,
        total: basePrice || 0
      }
    }

    const loadingSetting = chargeSettings.find(s => s.setting_name === 'loading_unloading_percentage')
    const vehicleSetting = chargeSettings.find(s => s.setting_name === 'vehicle_charge_flat')
    const gstSetting = chargeSettings.find(s => s.setting_name === 'gst_percentage')

    const loadingCharges = loadingSetting 
      ? Math.round(basePrice * (loadingSetting.setting_value / 100))
      : Math.round(basePrice * 0.10) // 10% default

    const vehicleCharges = vehicleSetting 
      ? vehicleSetting.setting_value
      : 40 // ₹40 default

    const gst = gstSetting 
      ? Math.round(basePrice * (gstSetting.setting_value / 100))
      : Math.round(basePrice * 0.05) // 5% default

    const total = basePrice + loadingCharges + vehicleCharges + gst

    return {
      loadingCharges,
      vehicleCharges,
      gst,
      total
    }
  }

  const charges = calculateCharges()

  // Notify parent component of total change
  useEffect(() => {
    if (onTotalChange) {
      onTotalChange(charges.total)
    }
  }, [charges.total, onTotalChange])

  if (loading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader>
            <CardTitle>Price Breakdown</CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2 text-sm text-muted-foreground">Loading charges...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle>Price Breakdown</CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-3">
        {error && (
          <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded">
            Using default charges (API unavailable)
          </div>
        )}
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Base Price:</span>
            <span className="font-medium">₹{basePrice.toFixed(2)}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              Loading/Unloading ({chargeSettings.find(s => s.setting_name === 'loading_unloading_percentage')?.setting_value || 10}%):
            </span>
            <span className="font-medium">₹{charges.loadingCharges.toFixed(2)}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Vehicle Charges:</span>
            <span className="font-medium">₹{charges.vehicleCharges.toFixed(2)}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              GST ({chargeSettings.find(s => s.setting_name === 'gst_percentage')?.setting_value || 5}%):
            </span>
            <span className="font-medium">₹{charges.gst.toFixed(2)}</span>
          </div>
          
          <Separator />
          
          <div className="flex justify-between items-center">
            <span className="font-semibold">Total Amount:</span>
            <span className="font-bold text-lg text-primary">₹{charges.total.toFixed(2)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Export the calculation function for use in other components
export const calculatePriceBreakdown = (
  basePrice: number,
  chargeSettings?: ChargeSettings[]
): CalculatedCharges => {
  if (!basePrice) {
    return {
      loadingCharges: 0,
      vehicleCharges: 0,
      gst: 0,
      total: 0
    }
  }

  const loadingSetting = chargeSettings?.find(s => s.setting_name === 'loading_unloading_percentage')
  const vehicleSetting = chargeSettings?.find(s => s.setting_name === 'vehicle_charge_flat')
  const gstSetting = chargeSettings?.find(s => s.setting_name === 'gst_percentage')

  const loadingCharges = loadingSetting 
    ? Math.round(basePrice * (loadingSetting.setting_value / 100))
    : Math.round(basePrice * 0.10)

  const vehicleCharges = vehicleSetting 
    ? vehicleSetting.setting_value
    : 40

  const gst = gstSetting 
    ? Math.round(basePrice * (gstSetting.setting_value / 100))
    : Math.round(basePrice * 0.05)

  const total = basePrice + loadingCharges + vehicleCharges + gst

  return {
    loadingCharges,
    vehicleCharges,
    gst,
    total
  }
}
