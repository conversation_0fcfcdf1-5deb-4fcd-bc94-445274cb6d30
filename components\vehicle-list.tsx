"use client"

import { useState } from "react"
import { Search, Truck, ArrowRight } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { LoadUnloadDialog } from "@/components/load-unload-dialog"
import { VehicleDetailsDialog } from "@/components/vehicle-details-dialog"
import { useToast } from "@/hooks/use-toast"

interface VehicleListProps {
  mode: "load" | "receive"
}

// Demo data
const vehicles = [
  {
    id: "TN01BR5678",
    type: "Delivery Van",
    route: "Chennai Central → Coimbatore Main",
    driver: {
      name: "<PERSON><PERSON><PERSON>",
      phone: "+91 98765 43210",
    },
    status: "arrived",
    scheduledArrival: "2024-03-21T08:30:00",
    scheduledDeparture: "2024-03-21T10:30:00",
    actualArrival: "2024-03-21T08:25:00",
    parcels: [
      { lrn: "LRN123456", status: "pending", type: "load", itemCount: 2 },
      { lrn: "LRN789012", status: "pending", type: "receive", itemCount: 1 },
      { lrn: "LRN345678", status: "pending", type: "load", itemCount: 3 },
    ],
    nextStop: "Coimbatore Main",
    capacity: {
      total: "850 kg",
      used: "650 kg",
      available: "200 kg"
    }
  },
  {
    id: "TN02BR1234",
    type: "Mini Truck",
    route: "Madurai Central → Chennai Central",
    driver: {
      name: "Rajkumar Venkatesh",
      phone: "+91 87654 32109",
    },
    status: "in_transit",
    scheduledArrival: "2024-03-21T11:00:00",
    scheduledDeparture: "2024-03-21T13:00:00",
    parcels: [
      { lrn: "LRN901234", status: "pending", type: "receive", itemCount: 2 },
      { lrn: "LRN567890", status: "pending", type: "load", itemCount: 1 },
    ],
    nextStop: "Chennai Central",
    capacity: {
      total: "1200 kg",
      used: "800 kg",
      available: "400 kg"
    }
  },
  {
    id: "TN03BR7890",
    type: "Cargo Truck",
    route: "Chennai Central → Salem Junction",
    driver: {
      name: "Senthil Kumar",
      phone: "+91 76543 21098",
    },
    status: "departed",
    scheduledArrival: "2024-03-21T07:00:00",
    scheduledDeparture: "2024-03-21T09:00:00",
    actualArrival: "2024-03-21T07:10:00",
    actualDeparture: "2024-03-21T09:15:00",
    parcels: [
      { lrn: "LRN234567", status: "completed", type: "load", itemCount: 4 },
      { lrn: "LRN890123", status: "completed", type: "receive", itemCount: 2 },
    ],
    nextStop: "Salem Junction",
    capacity: {
      total: "2500 kg",
      used: "2000 kg",
      available: "500 kg"
    }
  }
]

const statusColors = {
  arrived: "bg-green-500",
  in_transit: "bg-yellow-500",
  departed: "bg-blue-500"
}

const statusLabels = {
  arrived: "Arrived",
  in_transit: "In Transit",
  departed: "Departed"
}

export function VehicleList({ mode }: VehicleListProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [hasSearched, setHasSearched] = useState(false)
  const [selectedVehicle, setSelectedVehicle] = useState<typeof vehicles[0] | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isLoadUnloadOpen, setIsLoadUnloadOpen] = useState(false)
  const { toast } = useToast()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    const foundVehicle = vehicles.find(
      vehicle => vehicle.id.toLowerCase() === searchQuery.toLowerCase()
    )
    
    if (foundVehicle) {
      const hasRelevantParcels = foundVehicle.parcels.some(
        parcel => parcel.type === mode && parcel.status === "pending"
      )
      
      if (hasRelevantParcels) {
        setSelectedVehicle(foundVehicle)
      } else {
        toast({
          title: "No Pending Parcels",
          description: `Vehicle ${foundVehicle.id} has no pending parcels to ${mode}`,
          variant: "destructive"
        })
        setSelectedVehicle(null)
      }
    } else {
      toast({
        title: "Vehicle Not Found",
        description: "Please check the vehicle ID and try again",
        variant: "destructive"
      })
      setSelectedVehicle(null)
    }
    setHasSearched(true)
  }

  const handleClear = () => {
    setSearchQuery("")
    setSelectedVehicle(null)
    setHasSearched(false)
  }

  const handleStatusUpdate = (vehicleId: string, newStatus: "arrived" | "departed") => {
    toast({
      title: `Vehicle ${statusLabels[newStatus]}`,
      description: `Vehicle ${vehicleId} has been marked as ${statusLabels[newStatus].toLowerCase()}.`
    })
  }

  return (
    <div className="space-y-6">
      {!selectedVehicle && (
        <div className="flex flex-col items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md p-6 shadow-lg border-2">
            <CardContent className="space-y-6 p-0">
              <div className="text-center space-y-2">
                <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Truck className="h-8 w-8 text-primary" />
                </div>
                <h2 className="text-2xl font-bold tracking-tight">
                  {mode === "load" ? "Load Parcels" : "Receive Parcels"}
                </h2>
                <p className="text-muted-foreground">
                  Enter the vehicle ID to {mode === "load" ? "load" : "receive"} parcels
                </p>
              </div>

              <form onSubmit={handleSearch} className="space-y-4">
                <div className="relative">
                  <div className="relative group">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground transition-colors group-hover:text-primary" />
                    <Input
                      placeholder="Enter vehicle ID..."
                      className="pl-10 h-12 text-lg transition-all border-2 hover:border-primary/50 focus:border-primary"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex justify-center space-x-3">
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full max-w-xs transition-transform hover:scale-105"
                  >
                    <span>Search Vehicle</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  {hasSearched && (
                    <Button
                      type="button"
                      variant="outline"
                      size="lg"
                      onClick={handleClear}
                      className="transition-transform hover:scale-105"
                    >
                      Clear
                    </Button>
                  )}
                </div>
              </form>

              {hasSearched && !selectedVehicle && (
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <p className="text-muted-foreground">
                    No matching vehicle found or no pending parcels to {mode}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {selectedVehicle && (
        <div className="space-y-6">
          <div className="sticky top-0 z-10 bg-background pt-2 pb-4 border-b">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-2xl font-semibold">Vehicle Details</h2>
                <p className="text-muted-foreground">
                  {selectedVehicle.id} • {selectedVehicle.type}
                </p>
              </div>
              <Button variant="outline" onClick={handleClear}>Search Another Vehicle</Button>
            </div>
            
            <div className="flex flex-wrap gap-4">
              {selectedVehicle.status === "in_transit" && (
                <Button
                  size="lg"
                  onClick={() => handleStatusUpdate(selectedVehicle.id, "arrived")}
                >
                  Mark as Arrived
                </Button>
              )}
              {selectedVehicle.status === "arrived" && (
                <>
                  <Button 
                    size="lg"
                    onClick={() => setIsLoadUnloadOpen(true)}
                  >
                    {mode === "load" ? "Load Parcels" : "Receive Parcels"}
                  </Button>
                  <Button 
                    size="lg"
                    variant="secondary"
                    onClick={() => handleStatusUpdate(selectedVehicle.id, "departed")}
                  >
                    Mark as Departed
                  </Button>
                </>
              )}
              <Button 
                size="lg"
                variant="outline" 
                onClick={() => setIsDetailsOpen(true)}
              >
                View Full Details
              </Button>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* Vehicle Info Card */}
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge className={statusColors[selectedVehicle.status]}>
                    {statusLabels[selectedVehicle.status]}
                  </Badge>
                </div>
                <Separator />
                <div>
                  <p className="font-medium">Current Route</p>
                  <p className="text-muted-foreground">{selectedVehicle.route}</p>
                </div>
                <div>
                  <p className="font-medium">Next Stop</p>
                  <p className="text-muted-foreground">{selectedVehicle.nextStop}</p>
                </div>
              </CardContent>
            </Card>

            {/* Driver Info Card */}
            <Card>
              <CardHeader>
                <CardTitle>Driver Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">Name</p>
                  <p className="text-lg">{selectedVehicle.driver.name}</p>
                </div>
                <div>
                  <p className="font-medium">Contact</p>
                  <p className="text-lg">{selectedVehicle.driver.phone}</p>
                </div>
              </CardContent>
            </Card>

            {/* Schedule Card */}
            <Card>
              <CardHeader>
                <CardTitle>Schedule</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">Scheduled Arrival</p>
                  <p className="text-lg">{new Date(selectedVehicle.scheduledArrival).toLocaleString()}</p>
                </div>
                <div>
                  <p className="font-medium">Scheduled Departure</p>
                  <p className="text-lg">{new Date(selectedVehicle.scheduledDeparture).toLocaleString()}</p>
                </div>
                {selectedVehicle.actualArrival && (
                  <div>
                    <p className="font-medium">Actual Arrival</p>
                    <p className="text-lg">{new Date(selectedVehicle.actualArrival).toLocaleString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Capacity Card */}
            <Card>
              <CardHeader>
                <CardTitle>Capacity Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">Total Capacity</p>
                  <p className="text-lg">{selectedVehicle.capacity.total}</p>
                </div>
                <div>
                  <p className="font-medium">Used Capacity</p>
                  <p className="text-lg">{selectedVehicle.capacity.used}</p>
                </div>
                <div>
                  <p className="font-medium">Available Capacity</p>
                  <p className="text-lg">{selectedVehicle.capacity.available}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {selectedVehicle && (
        <>
          <VehicleDetailsDialog
            open={isDetailsOpen}
            onOpenChange={setIsDetailsOpen}
            vehicle={selectedVehicle}
          />
          <LoadUnloadDialog
            open={isLoadUnloadOpen}
            onOpenChange={setIsLoadUnloadOpen}
            mode={mode}
            vehicle={selectedVehicle}
          />
        </>
      )}
    </div>
  )
}
