-- Create a new enum type with all the values we want based on the flow diagram
CREATE TYPE memo_status_new AS ENUM ('Created', 'In Transit', 'Received', 'Completed');

-- Update the table to use the new enum type
-- This will convert existing values to the new type
-- 'Loaded' will be mapped to 'In Transit' as it seems to be the equivalent
ALTER TABLE memos
  ALTER COLUMN status TYPE memo_status_new
  USING (CASE
           WHEN status::text = 'Loaded' THEN 'In Transit'
           ELSE status::text
         END)::memo_status_new;

-- Drop the old enum type
DROP TYPE memo_status;

-- Rename the new enum type to the original name
ALTER TYPE memo_status_new RENAME TO memo_status;

-- Update the default value for the status column
ALTER TABLE memos ALTER COLUMN status SET DEFAULT 'Created';
