-- 1. ENUM Types

CREATE TYPE branch_status      AS ENUM ('Active','Inactive');
CREATE TYPE branch_type        AS ENUM ('Main','Sub');
CREATE TYPE user_role          AS ENUM ('Super Admin','Admin','Manager');
CREATE TYPE payment_mode       AS ENUM ('Paid','To Pay');
CREATE TYPE parcel_status      AS ENUM ('Booked','Loaded','Received','Delivered');
CREATE TYPE vehicle_status     AS ENUM ('Active','Maintenance','Retired');
CREATE TYPE driver_status      AS ENUM ('Active','On Leave','Inactive');
CREATE TYPE memo_status        AS ENUM ('Created','Received','Completed');
CREATE TYPE op_type            AS ENUM ('Load','Receive');
CREATE TYPE txn_type           AS ENUM ('Collection','Expense');
CREATE TYPE approval_status    AS ENUM ('Pending','Approved','Rejected');
CREATE TYPE remittance_method  AS ENUM ('Bank Transfer','UPI','Bank Deposit');
CREATE TYPE remittance_status  AS ENUM ('Submitted','Verified');



-- 2. branches

CREATE TABLE branches (
  branch_id                  SERIAL PRIMARY KEY,
  name                       VARCHAR(255)       NOT NULL,
  code                       CHAR(3)            NOT NULL UNIQUE,
  address                    VARCHAR(255),
  phone                      VARCHAR(20),
  email                      VARCHAR(255),
  operating_hours            JSONB,
  status                     branch_status      NOT NULL DEFAULT 'Active',
  location_coordinates       JSONB,  -- {"lat":12.34,"lng":56.78}
  branch_type                branch_type        NOT NULL DEFAULT 'Sub',
  manager_id                 INT,
  service_area               VARCHAR(255),

  postal_code                VARCHAR(10),
  google_maps_link           TEXT,
  photo_url                  TEXT,
  has_booking                BOOLEAN            DEFAULT FALSE,
  has_delivery               BOOLEAN            DEFAULT FALSE,
  has_transshipment          BOOLEAN            DEFAULT FALSE,
  door_delivery_available    BOOLEAN            DEFAULT FALSE,
  employee_count             INT                DEFAULT 0,
  head_employee_id           INT,  -- FK added below
  commission_percentage      DECIMAL(5,2),
  loading_percentage         DECIMAL(5,2),
  unloading_percentage       DECIMAL(5,2),
  transshipment_charges      DECIMAL(10,2)
);

ALTER TABLE branches
  ADD FOREIGN KEY (head_employee_id) REFERENCES users(user_id);



-- 3. users

CREATE TABLE users (
  user_id                    SERIAL PRIMARY KEY,
  name                       VARCHAR(255)       NOT NULL,
  email                      VARCHAR(255) UNIQUE NOT NULL,
  phone                      VARCHAR(20),
  role                       user_role          NOT NULL,
  branch_id                  INT                REFERENCES branches(branch_id) ON DELETE SET NULL,
  notification_preferences   JSONB,
  two_fa_enabled             BOOLEAN            NOT NULL DEFAULT FALSE,
  login_history              JSONB
);



-- 4. parceltypes

CREATE TABLE parceltypes (
  type_id                     SERIAL PRIMARY KEY,
  type_name                   VARCHAR(100) NOT NULL,
  base_price                  NUMERIC(10,2),
  per_kg_rate                 NUMERIC(10,2),
  weight_restriction          NUMERIC(10,2),
  special_handling_instructions TEXT
);



-- 5. parcels

CREATE TABLE parcels (
  parcel_id                  SERIAL PRIMARY KEY,
  lr_number                  VARCHAR(50)     UNIQUE NOT NULL,
  booking_datetime           TIMESTAMPTZ     DEFAULT now(),
  sender_name                VARCHAR(255)    NOT NULL,
  sender_phone               VARCHAR(20),
  sender_branch_id           INT             REFERENCES branches(branch_id) ON DELETE SET NULL,
  recipient_name             VARCHAR(255)    NOT NULL,
  recipient_phone            VARCHAR(20),
  delivery_branch_id         INT             REFERENCES branches(branch_id) ON DELETE SET NULL,
  number_of_items            INT,
  item_type                  INT             REFERENCES parceltypes(type_id) ON DELETE SET NULL,
  weight                     NUMERIC(10,2),
  payment_mode               payment_mode    NOT NULL DEFAULT 'Paid',
  delivery_charges           NUMERIC(10,2),
  base_price                 NUMERIC(10,2),
  tax_details                JSONB,
  total_amount               NUMERIC(10,2),
  current_status             parcel_status   NOT NULL DEFAULT 'Booked',
  expected_delivery_date     DATE,
  actual_delivery_date       DATE,
  proof_of_delivery_url      VARCHAR(255)
);



-- 6. vehicles

CREATE TABLE vehicles (
  vehicle_id                 SERIAL PRIMARY KEY,
  registration_number        VARCHAR(50)      NOT NULL,
  vehicle_type               VARCHAR(50),
  make_model                 VARCHAR(100),
  year                       INT,
  capacity                   INT,
  current_status             vehicle_status   NOT NULL DEFAULT 'Active',
  maintenance_history        JSONB,
  branch_id                  INT              REFERENCES branches(branch_id) ON DELETE SET NULL,
  insurance_provider         VARCHAR(255),
  insurance_policy_number    VARCHAR(100),
  insurance_expiry           DATE
);



-- 7. drivers

CREATE TABLE drivers (
  driver_id                  SERIAL PRIMARY KEY,
  name                       VARCHAR(255)     NOT NULL,
  contact_number             VARCHAR(20),
  status                     driver_status    NOT NULL DEFAULT 'Active'
);



-- 8. memos

CREATE TABLE memos (
  memo_id                    SERIAL PRIMARY KEY,
  memo_number                VARCHAR(50)      UNIQUE NOT NULL,
  vehicle_id                 INT              REFERENCES vehicles(vehicle_id) ON DELETE SET NULL,
  driver_ids                 JSONB,           -- Array of driver IDs: [1, 2]
  from_branch_id             INT              REFERENCES branches(branch_id) ON DELETE SET NULL,
  to_branch_id               INT              REFERENCES branches(branch_id) ON DELETE SET NULL,
  via_points                 JSONB,           -- Array of branch IDs for intermediate stops: [3, 4, 5]
  created_by                 INT              REFERENCES users(user_id) ON DELETE SET NULL,
  status                     memo_status      NOT NULL DEFAULT 'Created',
  created_at                 TIMESTAMPTZ      DEFAULT now(),
  received_at                TIMESTAMPTZ,
  received_by                INT              REFERENCES users(user_id) ON DELETE SET NULL,
  fuel_consumed              NUMERIC(10,2),
  bata_amount                NUMERIC(10,2),
  salary_amount              NUMERIC(10,2),
  total_expense              NUMERIC(10,2),
  notes                      TEXT,
  vehicle_validation         JSONB,           -- {"fc_valid": true, "insurance_valid": true, "permit_valid": true, "tax_valid": true}
  driver_validation          JSONB            -- {"dl_valid": true, "eligibility_valid": true}
);



-- 9. operations

CREATE TABLE operations (
  operation_id               SERIAL PRIMARY KEY,
  operation_type             op_type          NOT NULL,
  vehicle_id                 INT              REFERENCES vehicles(vehicle_id) ON DELETE CASCADE,
  parcel_id                  INT              REFERENCES parcels(parcel_id) ON DELETE CASCADE,
  confirmed_item_count       INT,
  total_item_count           INT,
  operator_id                INT              REFERENCES users(user_id) ON DELETE SET NULL,
  operation_timestamp        TIMESTAMPTZ      DEFAULT now()
);



-- 10. financialtransactions

CREATE TABLE financialtransactions (
  transaction_id             SERIAL PRIMARY KEY,
  branch_id                  INT              REFERENCES branches(branch_id) ON DELETE CASCADE,
  transaction_type           txn_type         NOT NULL,
  amount                     NUMERIC(10,2)    NOT NULL,
  payment_mode               VARCHAR(50),
  date_time                  TIMESTAMPTZ      DEFAULT now(),
  reference_number           VARCHAR(100),
  description                TEXT,
  attachment_url             VARCHAR(255),
  approval_status            approval_status  NOT NULL DEFAULT 'Pending'
);



-- 11. remittance

CREATE TABLE remittance (
  remittance_id              SERIAL PRIMARY KEY,
  branch_id                  INT              REFERENCES branches(branch_id) ON DELETE CASCADE,
  total_collections          NUMERIC(10,2),
  approved_expenses          NUMERIC(10,2),
  float_balance              NUMERIC(10,2),
  remittable_amount          NUMERIC(10,2),
  method                     remittance_method NOT NULL,
  proof_url                  VARCHAR(255),
  reference_id               VARCHAR(100),
  status                     remittance_status NOT NULL DEFAULT 'Submitted',
  submitted_by               INT              REFERENCES users(user_id) ON DELETE SET NULL,
  created_at                 TIMESTAMPTZ      DEFAULT now()
);



-- 12. accountsledger

CREATE TABLE accountsledger (
  ledger_id                  SERIAL PRIMARY KEY,
  branch_id                  INT              REFERENCES branches(branch_id) ON DELETE CASCADE,
  ledger_date                DATE             NOT NULL,
  opening_balance            NUMERIC(10,2)    NOT NULL,
  total_collection           NUMERIC(10,2),
  total_expense              NUMERIC(10,2),
  remitted                   NUMERIC(10,2),
  closing_balance            NUMERIC(10,2)    NOT NULL
);



-- 13. auditlogs

CREATE TABLE auditlogs (
  log_id                     SERIAL PRIMARY KEY,
  timestamp                  TIMESTAMPTZ      DEFAULT now(),
  user_id                    INT              REFERENCES users(user_id) ON DELETE CASCADE,
  action_type                VARCHAR(100),
  action_details             TEXT,
  ip_address                 VARCHAR(50),
  device_info                VARCHAR(100)
);



-- 14. notifications

CREATE TABLE notifications (
  notification_id            SERIAL PRIMARY KEY,
  target_user_id             INT              REFERENCES users(user_id) ON DELETE CASCADE,
  notification_type          VARCHAR(50),
  message                    TEXT,
  read_status                BOOLEAN          DEFAULT FALSE,
  timestamp                  TIMESTAMPTZ      DEFAULT now()
);



-- 15. cities

CREATE TABLE cities (
  city_id     INTEGER                     NOT NULL
              DEFAULT nextval('cities_city_id_seq'::regclass),
  name        TEXT                        NOT NULL,
  state       TEXT,
  country     TEXT                        DEFAULT 'India'::text,
  created_at  TIMESTAMPTZ                  NOT NULL DEFAULT now(),
  updated_at  TIMESTAMPTZ                  NOT NULL DEFAULT now(),
  PRIMARY KEY (city_id)
);
-- Complete vehicles table with every column from the JSON metadata:

CREATE TABLE vehicles (
  vehicle_id                 SERIAL                PRIMARY KEY,
  registration_number        VARCHAR(50)           NOT NULL,
  vehicle_type               VARCHAR(50),
  make_model                 VARCHAR(100),
  year                       INTEGER,
  capacity                   INTEGER,
  current_status             vehicle_status        NOT NULL DEFAULT 'Active',
  maintenance_history        JSONB,
  branch_id                  INTEGER               REFERENCES branches(branch_id) ON DELETE SET NULL,
  insurance_provider         VARCHAR(255),
  insurance_policy_number    VARCHAR(100),
  insurance_expiry           DATE,
  vehicle_number             VARCHAR(20),
  registration_type          registration_type,
  registration_expiry_date   DATE,
  ownership_type             ownership_type,
  owner_name                 VARCHAR(100),
  bank_name                  VARCHAR(100),
  lessor_name                VARCHAR(100),
  lease_start_date           DATE,
  lease_end_date             DATE,
  gvw_kgs                    NUMERIC,
  toll_category              toll_category,
  vehicle_body_type          VARCHAR(50),
  number_of_axles            INTEGER,
  tyre_size                  VARCHAR(50),
  fuel_type                  fuel_type,
  fuel_tank_capacity_litres  NUMERIC,
  cng_capacity_kgs           NUMERIC,
  rc_expiry_date             DATE,
  fitness_expiry_date        DATE,
  fitness_certificate_url    TEXT,
  permit_type                VARCHAR(50),
  permit_expiry_date         DATE,
  permit_document_url        TEXT,
  assigned_driver_id         INTEGER               REFERENCES drivers(driver_id) ON DELETE SET NULL,
  created_at                 TIMESTAMPTZ           NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at                 TIMESTAMPTZ           NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by                 INTEGER               REFERENCES users(user_id) ON DELETE SET NULL,
  updated_by                 INTEGER               REFERENCES users(user_id) ON DELETE SET NULL
);
