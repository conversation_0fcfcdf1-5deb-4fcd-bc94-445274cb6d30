-- Add general quantity field to parcel_actions table
-- This field will store the quantity for all action types

-- Step 1: Add the new quantity field
ALTER TABLE parcel_actions 
ADD COLUMN quantity INT;

-- Step 2: Add comment for documentation
COMMENT ON COLUMN parcel_actions.quantity IS 'General quantity field for all action types: total items for Booked, loaded items for Loaded, received items for Received, delivered items for Delivered';

-- Step 3: Update existing "Booked" actions to include the total number of items
UPDATE parcel_actions 
SET quantity = (
  SELECT p.number_of_items 
  FROM parcels p 
  WHERE p.parcel_id = parcel_actions.parcel_id
)
WHERE action_type = 'Booked' 
AND quantity IS NULL;

-- Step 4: Update existing "Loaded" actions to copy from quantity_loaded
UPDATE parcel_actions 
SET quantity = quantity_loaded
WHERE action_type = 'Loaded' 
AND quantity_loaded IS NOT NULL 
AND quantity IS NULL;

-- Step 5: Update existing "Received" actions to copy from quantity_received
UPDATE parcel_actions 
SET quantity = quantity_received
WHERE action_type = 'Received' 
AND quantity_received IS NOT NULL 
AND quantity IS NULL;

-- Step 6: Update existing "Delivered" actions to include the total number of items
UPDATE parcel_actions 
SET quantity = (
  SELECT p.number_of_items 
  FROM parcels p 
  WHERE p.parcel_id = parcel_actions.parcel_id
)
WHERE action_type = 'Delivered' 
AND quantity IS NULL;

-- Step 7: Update the create_initial_parcel_action function to include quantity
CREATE OR REPLACE FUNCTION create_initial_parcel_action()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_name VARCHAR(255);
BEGIN
  -- Only create action for new parcels (INSERT operation)
  -- Get sender branch name for the booking location
  SELECT name INTO v_branch_name 
  FROM branches 
  WHERE branch_id = NEW.sender_branch_id;
  
  -- Insert initial "Booked" action record with quantity
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    quantity,
    remarks,
    created_by
  ) VALUES (
    NEW.parcel_id,
    'Booked',
    NEW.booking_datetime,
    NEW.sender_branch_id,
    v_branch_name,
    NEW.number_of_items,
    'Parcel booked at origin branch',
    NULL -- Will be set by application code when available
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Update the process_parcel_loading function to include quantity
CREATE OR REPLACE FUNCTION process_parcel_loading()
RETURNS TRIGGER AS $$
DECLARE
  v_parcel_id INT;
  v_parcel_delivery_branch_id INT;
  v_loading_destination_branch_id INT;
  v_loading_type VARCHAR(20);
  v_user_id INT;
  v_chart_number VARCHAR(50);
BEGIN
  -- Get parcel information
  SELECT p.parcel_id, p.delivery_branch_id 
  INTO v_parcel_id, v_parcel_delivery_branch_id
  FROM parcels p 
  WHERE p.lr_number = NEW.lr_number;
  
  -- Get loading chart information
  SELECT lc.destination_branch_id, lc.chart_number
  INTO v_loading_destination_branch_id, v_chart_number
  FROM loading_charts lc 
  WHERE lc.chart_id = NEW.chart_id;
  
  -- Determine loading type based on destination match
  IF v_parcel_delivery_branch_id = v_loading_destination_branch_id THEN
    v_loading_type := 'Direct';
  ELSE
    v_loading_type := 'Via';
  END IF;
  
  -- Update the loading chart item with loading type and destination
  UPDATE loading_chart_items 
  SET 
    loading_type = v_loading_type,
    destination_branch_id = v_loading_destination_branch_id
  WHERE item_id = NEW.item_id;
  
  -- Update parcel status to 'Loaded'
  UPDATE parcels 
  SET current_status = 'Loaded'
  WHERE parcel_id = v_parcel_id;
  
  -- Get user ID from auth user
  SELECT user_id INTO v_user_id
  FROM users 
  WHERE auth_id = (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id);
  
  -- Create parcel action record with both quantity and quantity_loaded
  INSERT INTO parcel_actions (
    parcel_id,
    action_type,
    action_timestamp,
    branch_id,
    location_name,
    vehicle_id,
    operator_id,
    destination_branch_id,
    loading_type,
    quantity,
    quantity_loaded,
    remarks,
    reference_number,
    created_by
  )
  SELECT
    v_parcel_id,
    'Loaded',
    NOW(),
    (SELECT branch_id FROM users WHERE user_id = v_user_id), -- Current user's branch
    (SELECT name FROM branches WHERE branch_id = (SELECT branch_id FROM users WHERE user_id = v_user_id)),
    lc.vehicle_id,
    v_user_id,
    v_loading_destination_branch_id,
    v_loading_type,
    NEW.quantity, -- General quantity field
    NEW.quantity, -- Specific quantity_loaded field
    'Loaded via chart ' || v_chart_number || ' (' || v_loading_type || ' loading)',
    v_chart_number,
    (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id)
  FROM loading_charts lc
  WHERE lc.chart_id = NEW.chart_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
