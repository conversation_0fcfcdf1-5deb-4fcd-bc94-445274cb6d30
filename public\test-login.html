<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Login</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 500px;
        margin: 0 auto;
        padding: 20px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input {
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
      }
      button {
        background-color: #003a8c;
        color: white;
        border: none;
        padding: 10px 15px;
        cursor: pointer;
      }
      button:hover {
        background-color: #002a66;
      }
      .result {
        margin-top: 20px;
        padding: 10px;
        border: 1px solid #ccc;
        background-color: #f9f9f9;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <h1>Test Login</h1>

    <div class="form-group">
      <label for="email">Email:</label>
      <input type="email" id="email" value="<EMAIL>" />
    </div>

    <div class="form-group">
      <label for="password">Password:</label>
      <input type="password" id="password" />
    </div>

    <button id="login-btn">Login</button>
    <button id="check-session-btn">Check Session</button>
    <button id="logout-btn">Logout</button>

    <div class="result" id="result">Results will appear here...</div>

    <script>
      // Initialize Supabase client
      const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
      const supabaseKey =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";

      // Wait for the Supabase library to load
      document.addEventListener("DOMContentLoaded", () => {
        if (typeof supabase === "undefined") {
          console.error("Supabase library not loaded");
          document.getElementById("result").textContent =
            "Error: Supabase library not loaded. Please check the console.";
          return;
        }

        const supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);

        // DOM elements
        const emailInput = document.getElementById("email");
        const passwordInput = document.getElementById("password");
        const loginBtn = document.getElementById("login-btn");
        const checkSessionBtn = document.getElementById("check-session-btn");
        const logoutBtn = document.getElementById("logout-btn");
        const resultDiv = document.getElementById("result");

        // Login function
        loginBtn.addEventListener("click", async () => {
          const email = emailInput.value;
          const password = passwordInput.value;

          if (!email || !password) {
            resultDiv.textContent = "Please enter both email and password";
            return;
          }

          resultDiv.textContent = "Logging in...";

          try {
            const { data, error } =
              await supabaseClient.auth.signInWithPassword({
                email,
                password,
              });

            if (error) {
              resultDiv.textContent = `Error: ${error.message}`;
              return;
            }

            resultDiv.textContent = `Login successful!\n\nUser: ${JSON.stringify(
              data.user,
              null,
              2
            )}\n\nSession: ${JSON.stringify(data.session, null, 2)}`;

            // Redirect to dashboard after 2 seconds
            setTimeout(() => {
              window.location.href = "/simple-dashboard";
            }, 2000);
          } catch (error) {
            resultDiv.textContent = `Unexpected error: ${error.message}`;
          }
        });

        // Check session function
        checkSessionBtn.addEventListener("click", async () => {
          resultDiv.textContent = "Checking session...";

          try {
            const { data, error } = await supabaseClient.auth.getSession();

            if (error) {
              resultDiv.textContent = `Error: ${error.message}`;
              return;
            }

            if (data.session) {
              resultDiv.textContent = `Active session found!\n\nUser: ${JSON.stringify(
                data.session.user,
                null,
                2
              )}\n\nExpires: ${new Date(
                data.session.expires_at * 1000
              ).toLocaleString()}`;
            } else {
              resultDiv.textContent = "No active session found.";
            }
          } catch (error) {
            resultDiv.textContent = `Unexpected error: ${error.message}`;
          }
        });

        // Logout function
        logoutBtn.addEventListener("click", async () => {
          resultDiv.textContent = "Logging out...";

          try {
            const { error } = await supabaseClient.auth.signOut();

            if (error) {
              resultDiv.textContent = `Error: ${error.message}`;
              return;
            }

            resultDiv.textContent = "Logout successful!";
          } catch (error) {
            resultDiv.textContent = `Unexpected error: ${error.message}`;
          }
        });

        // Check session on page load
        (async () => {
          try {
            const { data } = await supabaseClient.auth.getSession();

            if (data.session) {
              resultDiv.textContent = `Already logged in!\n\nUser: ${JSON.stringify(
                data.session.user,
                null,
                2
              )}\n\nExpires: ${new Date(
                data.session.expires_at * 1000
              ).toLocaleString()}`;
            }
          } catch (error) {
            console.error("Error checking session:", error);
          }
        })();
      });
    </script>
  </body>
</html>
