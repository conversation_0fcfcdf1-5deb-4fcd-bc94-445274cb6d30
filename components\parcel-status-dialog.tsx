"use client"

import { useState, useEffect } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Banknote, Smartphone } from "lucide-react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "./ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select"
import { Input } from "./ui/input"
import { Button } from "./ui/button"
import { Textarea } from "./ui/textarea"
import { RadioGroup, RadioGroupItem } from "./ui/radio-group"
import { Label } from "./ui/label"
import { useToast } from "@/hooks/use-toast"

const statusUpdateSchema = z.object({
  newStatus: z.string().min(1, "Please select a new status"),
  notes: z.string().optional(),
  // Original recipient details (non-editable, for reference)
  originalRecipientDetails: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  // Collector details (person actually receiving the parcel)
  collectorDetails: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
    idProof: z.string().optional(),
    idNumber: z.string().optional(),
    signature: z.string().optional(),
  }).optional(),
  paymentDetails: z.object({
    amount: z.string().min(1, "Amount is required"),
    method: z.string().min(1, "Payment method is required"),
    transactionId: z.string().optional(), // For online payments
  }).optional().refine(data => {
    // If payment details are provided, both amount and method must be present
    if (data) {
      return data.amount && data.method
    }
    return true
  }, {
    message: "Both amount and payment method are required"
  })
})

const availableTransitions = {
  "Booked": [
    { value: "In Transit", label: "Mark as In Transit" },
    { value: "Cancelled", label: "Cancel Booking" },
  ],
  "In Transit": [
    { value: "To Be Delivered", label: "Mark for Delivery" },
    { value: "Cancelled", label: "Cancel" },
  ],
  "To Be Delivered": [
    { value: "Delivered", label: "Mark as Delivered" },
    { value: "Cancelled", label: "Cancel" },
  ],
  "Delivered": [], // No further transitions available
  "Cancelled": [], // No further transitions available
}

const paymentMethods = [
  { value: "cash", label: "Cash Payment" },
  { value: "online", label: "Online Payment" },
]

const idProofTypes = [
  { value: "aadhar", label: "Aadhar Card" },
  { value: "pan", label: "PAN Card" },
  { value: "voter", label: "Voter ID" },
  { value: "driving", label: "Driving License" },
  { value: "passport", label: "Passport" },
  { value: "other", label: "Other" },
]

interface ParcelStatusDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  parcel: {
    lr: string
    status: string
    itemCount?: number
    paymentType: string
    price: string
    recipientName?: string
    recipientPhone?: string
  }
  onStatusUpdate: (data: z.infer<typeof statusUpdateSchema>) => void
}

export function ParcelStatusDialog({
  open,
  onOpenChange,
  parcel,
  onStatusUpdate,
}: ParcelStatusDialogProps) {
  const { toast } = useToast()
  const [showPayment, setShowPayment] = useState(false)
  const [showRecipientDetails, setShowRecipientDetails] = useState(false)
  const supabase = createClientComponentClient()

  const form = useForm<z.infer<typeof statusUpdateSchema>>({
    resolver: zodResolver(statusUpdateSchema),
    defaultValues: {
      newStatus: "",
      notes: "",
      originalRecipientDetails: {
        name: parcel.recipientName || "",
        phone: parcel.recipientPhone || "",
      },
      collectorDetails: {
        name: "",
        phone: "",
        idProof: "",
        idNumber: "",
        signature: "",
      },
      paymentDetails: undefined
    }
  })

  const selectedStatus = form.watch("newStatus")

  // Show appropriate sections based on status transition
  useEffect(() => {
    // Show recipient details when marking as Delivered from To Be Delivered
    const shouldShowRecipientDetails =
      parcel.status === "To Be Delivered" &&
      selectedStatus === "Delivered"

    // Show payment section when marking as delivered for to_pay parcels
    const shouldShowPayment =
      parcel.status === "To Be Delivered" &&
      selectedStatus === "Delivered" &&
      parcel.paymentType === "to_pay"

    setShowRecipientDetails(shouldShowRecipientDetails)
    setShowPayment(shouldShowPayment)

    // Set original recipient details
    if (shouldShowRecipientDetails) {
      form.setValue("originalRecipientDetails", {
        name: parcel.recipientName || "",
        phone: parcel.recipientPhone || "",
      })
    }

    // Set payment details if needed
    if (shouldShowPayment) {
      form.setValue("paymentDetails", {
        amount: parcel.price,
        method: "",
        transactionId: "",
      })
    } else {
      form.setValue("paymentDetails", undefined)
    }
  }, [selectedStatus, parcel.status, parcel.paymentType, form, parcel.price, parcel.recipientName, parcel.recipientPhone])

  async function onSubmit(values: z.infer<typeof statusUpdateSchema>) {
    // Validate collector details if required
    if (showRecipientDetails) {
      if (!values.collectorDetails?.name || !values.collectorDetails?.phone) {
        toast({
          title: "Collector Details Required",
          description: "Please enter the name and phone number of the person receiving the parcel",
          variant: "destructive"
        })
        return
      }

      // Validate ID proof if provided
      if (values.collectorDetails.idProof && !values.collectorDetails.idNumber) {
        toast({
          title: "ID Number Required",
          description: "Please enter the ID number for the selected ID proof",
          variant: "destructive"
        })
        return
      }
    }

    // Validate payment details if required
    if (showPayment) {
      if (!values.paymentDetails?.method || !values.paymentDetails?.amount) {
        toast({
          title: "Payment Details Required",
          description: "Please enter both payment method and amount",
          variant: "destructive"
        })
        return
      }

      // Validate that the amount matches the required amount
      if (values.paymentDetails.amount !== parcel.price) {
        toast({
          title: "Invalid Amount",
          description: "The collected amount must match the required payment",
          variant: "destructive"
        })
        return
      }

      // Validate transaction ID for online payments
      if (values.paymentDetails.method === 'online' && !values.paymentDetails.transactionId) {
        toast({
          title: "Transaction ID Required",
          description: "Please enter the transaction ID for online payment",
          variant: "destructive"
        })
        return
      }

      try {
        // Get user's branch ID from session
        const { data: { session } } = await supabase.auth.getSession()

        if (!session) {
          console.error("No session found when processing payment")
          toast({
            title: "Authentication Error",
            description: "Could not verify your session. Please try again.",
            variant: "destructive"
          })
          return
        }

        // Get user details to find branch ID
        const { data: userData } = await supabase
          .from('users')
          .select('branch_id')
          .eq('auth_id', session.user.id)
          .single()

        if (!userData || !userData.branch_id) {
          console.error("Could not determine branch ID for payment")
          toast({
            title: "Branch Error",
            description: "Could not determine your branch. Please try again.",
            variant: "destructive"
          })
          return
        }

        // Import the payment helper function
        const { createParcelPaymentTransaction } = await import('@/lib/payment-helpers')

        // Create a financial transaction for this payment
        const transaction = await createParcelPaymentTransaction(
          userData.branch_id,
          parseFloat(values.paymentDetails.amount),
          values.paymentDetails.method,
          parcel.lr,
          `Payment collected for parcel ${parcel.lr} via ${values.paymentDetails.method}`
        )

        if (!transaction) {
          console.error("Failed to create payment transaction")
          toast({
            title: "Payment Error",
            description: "Failed to record payment. Please try again.",
            variant: "destructive"
          })
          return
        }
      } catch (error) {
        console.error("Error processing payment:", error)
        toast({
          title: "Payment Error",
          description: "An error occurred while processing payment. Please try again.",
          variant: "destructive"
        })
        return
      }
    }

    // Submit the update
    onStatusUpdate(values)
    onOpenChange(false)
    form.reset()
  }

  // Step-by-step approach
  const [step, setStep] = useState(1)
  // Add a confirmation step at the end
  const totalSteps = showPayment ? 4 : showRecipientDetails ? 3 : 2

  // Reset step when dialog opens
  useEffect(() => {
    if (open) {
      setStep(1)
    }
  }, [open])

  // Move to next step
  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      form.handleSubmit(onSubmit)()
    }
  }

  // Move to previous step
  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  // Check if current step is valid before proceeding
  const validateCurrentStep = () => {
    if (step === 1) {
      // Validate status selection
      if (!selectedStatus) {
        toast({
          title: "Status Required",
          description: "Please select a new status for the parcel",
          variant: "destructive"
        })
        return false
      }
      return true
    }

    if (step === 2 && showRecipientDetails) {
      // Validate collector details
      const collectorDetails = form.getValues("collectorDetails")
      if (!collectorDetails?.name || !collectorDetails?.phone) {
        toast({
          title: "Collector Details Required",
          description: "Please enter the name and phone number of the person receiving the parcel",
          variant: "destructive"
        })
        return false
      }

      // Validate ID proof if provided
      if (collectorDetails.idProof && !collectorDetails.idNumber) {
        toast({
          title: "ID Number Required",
          description: "Please enter the ID number for the selected ID proof",
          variant: "destructive"
        })
        return false
      }
      return true
    }

    if (step === 2 && showPayment) {
      // Validate payment details
      const paymentDetails = form.getValues("paymentDetails")
      if (!paymentDetails?.method) {
        toast({
          title: "Payment Method Required",
          description: "Please select a payment method",
          variant: "destructive"
        })
        return false
      }

      if (!paymentDetails.amount || paymentDetails.amount !== parcel.price) {
        toast({
          title: "Invalid Amount",
          description: "The collected amount must match the required payment",
          variant: "destructive"
        })
        return false
      }

      // Validate transaction ID for online payments
      if (paymentDetails.method === 'online' && !paymentDetails.transactionId) {
        toast({
          title: "Transaction ID Required",
          description: "Please enter the transaction ID for online payment",
          variant: "destructive"
        })
        return false
      }
      return true
    }

    return true
  }

  // Handle next button click with validation
  const handleNext = () => {
    if (validateCurrentStep()) {
      nextStep()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Update Parcel Status</DialogTitle>
          <DialogDescription>
            Update status for parcel {parcel.lr}
          </DialogDescription>
        </DialogHeader>

        {/* Progress indicator */}
        {totalSteps > 1 && (
          <div className="w-full mb-4">
            <div className="flex justify-between mb-2">
              {Array.from({ length: totalSteps }).map((_, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2
                    ${step > index + 1 ? 'bg-primary text-primary-foreground border-primary' :
                      step === index + 1 ? 'border-primary text-primary' : 'border-gray-300 text-gray-400'}`}
                >
                  {index + 1}
                </div>
              ))}
            </div>
            <div className="relative w-full h-2 bg-gray-200 rounded-full">
              <div
                className="absolute top-0 left-0 h-2 bg-primary rounded-full transition-all duration-300"
                style={{ width: `${((step - 1) / (totalSteps - 1)) * 100}%` }}
              ></div>
            </div>
            <div className="flex justify-between mt-1 text-xs text-gray-500">
              <span>Select Status</span>
              {showRecipientDetails && <span>Collector Details</span>}
              {showPayment && <span>Payment</span>}
              <span>Confirmation</span>
            </div>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Step 1: Status Selection */}
            {step === 1 && (
              <div className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg mb-4">
                  <h3 className="font-medium text-blue-800">Parcel Information</h3>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div>
                      <p className="text-sm text-gray-500">LR Number</p>
                      <p className="font-medium">{parcel.lr}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Current Status</p>
                      <p className="font-medium">{parcel.status}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Recipient</p>
                      <p className="font-medium">{parcel.recipientName || "Not specified"}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Payment Type</p>
                      <p className="font-medium">{parcel.paymentType === "to_pay" ? "To Pay" : "Paid"}</p>
                    </div>
                  </div>
                </div>

                <FormField
                  control={form.control}
                  name="newStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select new status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableTransitions[parcel.status as keyof typeof availableTransitions]?.map((transition) => (
                            <SelectItem key={transition.value} value={transition.value}>
                              {transition.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Step 2: Recipient Details */}
            {step === 2 && showRecipientDetails && (
              <div className="space-y-4">
                {/* Original Recipient Details (non-editable) */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium">Original Recipient Details</h3>
                  <p className="text-sm text-muted-foreground">These are the details provided during booking</p>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <p className="text-sm text-gray-500">Recipient Name</p>
                      <p className="font-medium">{parcel.recipientName || "Not specified"}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Recipient Phone</p>
                      <p className="font-medium">{parcel.recipientPhone || "Not specified"}</p>
                    </div>
                  </div>
                </div>

                {/* Collector Details (editable) */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium">Collector Details</h3>
                  <p className="text-sm text-muted-foreground">Enter details of the person collecting the parcel</p>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="collectorDetails.name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Collector Name</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter collector name"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="collectorDetails.phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Collector Phone</FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="Enter collector phone"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="collectorDetails.idProof"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ID Proof Type (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select ID proof type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {idProofTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="collectorDetails.idNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ID Number (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter ID number"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Payment Collection */}
            {step === 3 && showPayment && (
              <div className="space-y-4">
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-medium">Payment Collection</h3>
                  <div className="bg-yellow-100 border border-yellow-200 rounded p-3 my-3 text-sm">
                    <p className="font-medium text-yellow-800">Payment Due: ₹{parcel.price}</p>
                    <p className="text-yellow-700 text-xs">This parcel has a pending payment that needs to be collected.</p>
                  </div>

                  <FormField
                    control={form.control}
                    name="paymentDetails.method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Method</FormLabel>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="grid grid-cols-2 gap-4 pt-2"
                        >
                          {paymentMethods.map((method) => (
                            <div key={method.value}>
                              <RadioGroupItem
                                value={method.value}
                                id={method.value}
                                className="peer sr-only"
                              />
                              <Label
                                htmlFor={method.value}
                                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                              >
                                {method.value === "cash" ? (
                                  <Banknote className="mb-2 h-6 w-6" />
                                ) : (
                                  <Smartphone className="mb-2 h-6 w-6" />
                                )}
                                <p className="text-sm font-medium">{method.label}</p>
                              </Label>
                            </div>
                          ))}
                        </RadioGroup>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paymentDetails.amount"
                    render={({ field }) => (
                      <FormItem className="mt-4">
                        <FormLabel>Amount Received</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter amount"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Transaction ID for online payments */}
                  {form.watch("paymentDetails.method") === "online" && (
                    <FormField
                      control={form.control}
                      name="paymentDetails.transactionId"
                      render={({ field }) => (
                        <FormItem className="mt-4">
                          <FormLabel>Transaction ID</FormLabel>
                          <FormControl>
                            <Input
                              type="text"
                              placeholder="Enter transaction ID"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Step 4: Confirmation */}
            {step === totalSteps && (
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-800">Confirmation</h3>
                  <p className="text-sm text-green-700 mb-4">Please review the information before finalizing</p>

                  <div className="space-y-4">
                    <div className="bg-white rounded p-3 border">
                      <h4 className="font-medium text-sm">Status Update</h4>
                      <p className="text-sm">New Status: <span className="font-medium">{selectedStatus}</span></p>
                      {form.getValues("notes") && (
                        <p className="text-sm mt-1">Notes: {form.getValues("notes")}</p>
                      )}
                    </div>

                    {showRecipientDetails && (
                      <div className="bg-white rounded p-3 border">
                        <h4 className="font-medium text-sm">Collector Details</h4>
                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div>
                            <p className="text-xs text-gray-500">Name</p>
                            <p className="text-sm">{form.getValues("collectorDetails.name")}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Phone</p>
                            <p className="text-sm">{form.getValues("collectorDetails.phone")}</p>
                          </div>

                          {form.getValues("collectorDetails.idProof") && (
                            <div>
                              <p className="text-xs text-gray-500">ID Proof</p>
                              <p className="text-sm">{form.getValues("collectorDetails.idProof")}: {form.getValues("collectorDetails.idNumber")}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {showPayment && (
                      <div className="bg-white rounded p-3 border">
                        <h4 className="font-medium text-sm">Payment Details</h4>
                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div>
                            <p className="text-xs text-gray-500">Amount</p>
                            <p className="text-sm">₹{form.getValues("paymentDetails.amount")}</p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500">Method</p>
                            <p className="text-sm">{form.getValues("paymentDetails.method") === "cash" ? "Cash Payment" : "Online Payment"}</p>
                          </div>
                          {form.getValues("paymentDetails.method") === "online" && form.getValues("paymentDetails.transactionId") && (
                            <div>
                              <p className="text-xs text-gray-500">Transaction ID</p>
                              <p className="text-sm">{form.getValues("paymentDetails.transactionId")}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <DialogFooter className="flex justify-between">
              {step > 1 && (
                <Button type="button" variant="outline" onClick={prevStep}>
                  Back
                </Button>
              )}
              <Button type="button" onClick={step === totalSteps ? form.handleSubmit(onSubmit) : handleNext}>
                {step === totalSteps ? (showPayment ? "Collect Payment & Update" : "Update Status") : "Next"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
