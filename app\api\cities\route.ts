import { NextResponse } from "next/server";
import { getCities } from "@/lib/db-helpers";
import { createClient } from "@supabase/supabase-js";

// Initialize the Supabase client with fallback values
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL ||
  "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Cities to add
const initialCities = [
  { name: "Chennai", state: "Tamil Nadu", country: "India" },
  { name: "Coimbatore", state: "Tamil Nadu", country: "India" },
  { name: "Madurai", state: "Tamil Nadu", country: "India" },
  { name: "Salem", state: "Tamil Nadu", country: "India" },
  { name: "Trichy", state: "Tamil Nadu", country: "India" },
];

// Function to ensure the cities table exists
async function ensureCitiesTable() {
  try {
    // Check if cities table exists by trying to select from it
    const { error } = await supabase.from("cities").select("count").limit(1);

    if (error && error.code === "42P01") {
      // Table doesn't exist, create it
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS public.cities (
          city_id SERIAL PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          state TEXT,
          country TEXT DEFAULT 'India',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );

        -- Add city_id column to branches table if it doesn't exist
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'branches' AND column_name = 'city_id'
          ) THEN
            ALTER TABLE public.branches ADD COLUMN city_id INTEGER REFERENCES public.cities(city_id);
            CREATE INDEX IF NOT EXISTS branches_city_id_idx ON public.branches (city_id);
          END IF;
        END $$;
      `;

      // Execute the query using PostgreSQL function
      const { error: createError } = await supabase.rpc("exec_sql", {
        sql: createTableQuery,
      });

      if (createError) {
        console.error("Error creating cities table:", createError);
        return false;
      }

      // Add initial cities
      for (const city of initialCities) {
        const { error: insertError } = await supabase.from("cities").insert([
          city,
        ]);

        if (insertError && insertError.code !== "23505") { // Ignore unique violation errors
          console.error(`Error adding city ${city.name}:`, insertError);
        }
      }
    }

    return true;
  } catch (error: any) {
    console.error("Error ensuring cities table:", error);
    return false;
  }
}

export async function GET() {
  try {
    // Try to ensure the cities table exists
    await ensureCitiesTable();

    // Get cities
    const cities = await getCities();

    if (!cities) {
      return NextResponse.json(
        { error: "Failed to fetch cities" },
        { status: 500 },
      );
    }

    return NextResponse.json({ cities });
  } catch (error: any) {
    console.error("Error in cities API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
