"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { CashManagement } from "@/components/cash-management"
import { ExpenseManagement } from "@/components/expense-management"
import { ReportsAnalytics } from "@/components/reports-analytics"
import { PageLayout } from "@/components/page-layout"
import { DashboardShell } from "@/components/dashboard-shell"

export default function OperationsPage() {
  return (
    <PageLayout
      title="Accounts"
      subtitle="Manage cash collections, expenses, and reports"
    >
      <DashboardShell>

        <Tabs defaultValue="cash" className="space-y-3">
          <TabsList>
            <TabsTrigger value="cash">Cash Management</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>
          <TabsContent value="cash" className="space-y-3">
            <CashManagement />
          </TabsContent>
          <TabsContent value="expenses" className="space-y-3">
            <ExpenseManagement />
          </TabsContent>
          <TabsContent value="reports" className="space-y-3">
            <ReportsAnalytics />
          </TabsContent>
        </Tabs>
      </DashboardShell>
    </PageLayout>
  )
}
