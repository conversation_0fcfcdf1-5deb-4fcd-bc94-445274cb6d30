import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/user/current-branch
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("🔍 Getting current user branch for user:", session.user.id);

    // Get user information from the users table
    const { data: user, error: userError } = await supabase
      .from("users")
      .select(`
        user_id,
        name,
        email,
        role,
        branch_id,
        branch:branches(
          branch_id,
          name,
          code,
          city_id,
          city:cities(name)
        )
      `)
      .eq("auth_user_id", session.user.id)
      .single();

    if (userError || !user) {
      console.error("Error finding user:", userError);
      return NextResponse.json(
        { error: "User not found in system" },
        { status: 404 }
      );
    }

    console.log("✅ Found user:", {
      user_id: user.user_id,
      name: user.name,
      branch_id: user.branch_id,
      branch_name: user.branch?.name
    });

    return NextResponse.json({
      user: {
        user_id: user.user_id,
        name: user.name,
        email: user.email,
        role: user.role,
        branch_id: user.branch_id,
        branch: user.branch
      }
    });

  } catch (error: any) {
    console.error("Error in GET /api/user/current-branch:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
