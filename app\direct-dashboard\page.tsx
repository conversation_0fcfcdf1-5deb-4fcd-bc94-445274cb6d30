"use client"

import { useEffect } from "react"
import { supabase } from "@/lib/supabase"

export default function DirectDashboardPage() {
  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log("DirectDashboard: Checking authentication...")
        
        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (sessionError) {
          console.error("DirectDashboard: Session error:", sessionError)
          return
        }
        
        if (!session) {
          console.log("DirectDashboard: No session found, redirecting to login")
          window.location.href = "/login"
          return
        }
        
        console.log("DirectDashboard: Session found, redirecting to root")
        
        // Force a direct navigation to the root path
        // This bypasses any middleware or other components that might be causing issues
        window.location.replace("/")
      } catch (error: any) {
        console.error("DirectDashboard: Error checking authentication:", error)
      }
    }
    
    checkAuth()
  }, [])
  
  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Redirecting to Dashboard...</h1>
        <p>Please wait while we redirect you to the dashboard.</p>
      </div>
    </div>
  )
}
