"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { NewMemo } from "@/components/new-memo"
import { MemoList } from "@/components/memo-list"

export function MemoManagement() {
  return (
    <div className="space-y-4">
      <Tabs defaultValue="manage" className="space-y-4">
        <TabsList>
          <TabsTrigger value="manage">Manage Memos</TabsTrigger>
          <TabsTrigger value="new">New Memo</TabsTrigger>
        </TabsList>
        <TabsContent value="manage" className="space-y-4">
          <MemoList />
        </TabsContent>
        <TabsContent value="new" className="space-y-4">
          <NewMemo />
        </TabsContent>
      </Tabs>
    </div>
  )
}