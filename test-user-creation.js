// Test script to verify user creation in the public table
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testUserCreation() {
  try {
    // Test inserting a user with a valid role
    console.log("Testing user creation with valid role...");

    const testUser = {
      email: "<EMAIL>",
      name: "Test Manager",
      role: "Manager",
    };

    const { data, error } = await supabase
      .from("users")
      .insert([testUser])
      .select();

    if (error) {
      console.error("Error creating test user:", error);
    } else {
      console.log("Test user created successfully:", data);
    }

    // Get all users to see the structure
    console.log("Getting all users...");

    const { data: allUsers, error: getUsersError } = await supabase
      .from("users")
      .select("*");

    if (getUsersError) {
      console.error("Error getting users:", getUsersError);
    } else {
      console.log("All users:", allUsers);
    }
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

testUserCreation();
