import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getUserIdFromAuthId } from "@/lib/user-helpers";

// GET /api/memos
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const branch_id = url.searchParams.get("branch_id");

    // Get user's branch ID from the request headers
    // This should be set by the auth middleware
    const userBranchId = url.searchParams.get("user_branch_id");

    // Build query
    let query = supabase
      .from("memos")
      .select(`
        *,
        vehicle:vehicles(registration_number, vehicle_type),
        from_branch:branches!from_branch_id(name, code),
        to_branch:branches!to_branch_id(name, code),
        creator:users!created_by(name)
      `);

    // Add filters if provided
    if (status) {
      query = query.eq("status", status);
    }

    // If branch_id is provided, use it; otherwise use the user's branch ID
    const effectiveBranchId = branch_id || userBranchId;

    if (effectiveBranchId) {
      query = query.or(
        `from_branch_id.eq.${effectiveBranchId},to_branch_id.eq.${effectiveBranchId}`,
      );
    }

    // Execute query
    const { data: memos, error } = await query.order("created_at", {
      ascending: false,
    });

    if (error) {
      console.error("Error fetching memos:", error);
      return NextResponse.json({ error: "Failed to fetch memos" }, {
        status: 500,
      });
    }

    return NextResponse.json({ memos });
  } catch (error: any) {
    console.error("Error in GET /api/memos:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/memos
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (
      !body.vehicle_id || !body.from_branch_id || !body.to_branch_id ||
      !body.driver_ids
    ) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: vehicle_id, from_branch_id, to_branch_id, driver_ids",
        },
        { status: 400 },
      );
    }

    // Generate memo number
    const memo_number = generateMemoNumber();

    // Create memo
    // Get the numeric user ID from the auth ID using our helper function
    let numericUserId = null;
    if (body.created_by && typeof body.created_by === "string") {
      try {
        numericUserId = await getUserIdFromAuthId(body.created_by);
        if (numericUserId) {
          console.log(
            `Found numeric user_id ${numericUserId} for auth_id ${body.created_by}`,
          );
        } else {
          console.log(
            `Could not find numeric user_id for auth_id ${body.created_by}`,
          );
        }
      } catch (userLookupError: any) {
        console.error("Error looking up user:", userLookupError);
      }
    }

    // Create memo with the correct user ID format
    const { data: memo, error } = await supabase
      .from("memos")
      .insert({
        memo_number,
        vehicle_id: body.vehicle_id,
        driver_ids: body.driver_ids,
        from_branch_id: body.from_branch_id,
        to_branch_id: body.to_branch_id,
        via_points: body.via_points || [],
        created_by: numericUserId, // Use the numeric ID if found, otherwise null
        status: "Created",
        vehicle_validation: body.vehicle_validation,
        driver_validation: body.driver_validation,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating memo:", error);
      return NextResponse.json({ error: "Failed to create memo" }, {
        status: 500,
      });
    }

    return NextResponse.json({ memo });
  } catch (error: any) {
    console.error("Error in POST /api/memos:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// Helper function to generate memo number
function generateMemoNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, "0");
  return `MEM${year}${month}${day}${random}`;
}
