"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Check, Copy, RefreshCw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface MockPaymentQRProps {
  amount: string
  reference: string
  onPaymentComplete: () => void
}

export function MockPaymentQR({
  amount,
  reference,
  onPaymentComplete
}: MockPaymentQRProps) {
  const { toast } = useToast()
  const [copied, setCopied] = useState(false)
  const [paymentAmount, setPaymentAmount] = useState(amount)
  const [isVerifying, setIsVerifying] = useState(false)

  // Mock UPI ID
  const upiId = "kpnlogistics@ybl"

  // Generate a mock QR code (just a placeholder image)
  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=upi://pay?pa=${upiId}&pn=KPN%20Logistics&am=${amount}&tr=${reference}&cu=INR`

  const handleCopyUPI = () => {
    navigator.clipboard.writeText(upiId)
    setCopied(true)
    toast({
      title: "UPI ID Copied",
      description: "UPI ID has been copied to clipboard",
    })
    setTimeout(() => setCopied(false), 2000)
  }

  const handleVerifyPayment = () => {
    setIsVerifying(true)
    
    // Simulate payment verification
    setTimeout(() => {
      setIsVerifying(false)
      onPaymentComplete()
      toast({
        title: "Payment Verified",
        description: "Your payment has been verified successfully",
      })
    }, 2000)
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-1">Scan & Pay</h3>
        <p className="text-sm text-muted-foreground">
          Amount: ₹{amount} • Reference: {reference}
        </p>
      </div>

      <div className="flex justify-center">
        <Card className="p-4 border-dashed">
          <div className="flex flex-col items-center">
            <img 
              src={qrCodeUrl} 
              alt="Payment QR Code" 
              className="w-48 h-48 mb-4"
            />
            <div className="flex items-center gap-2 text-sm">
              <span className="font-medium">UPI ID: {upiId}</span>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-6 w-6" 
                onClick={handleCopyUPI}
              >
                {copied ? (
                  <Check className="h-3 w-3" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>
        </Card>
      </div>

      <div className="space-y-4 border rounded-lg p-4">
        <div className="space-y-2">
          <Label htmlFor="payment-amount">Enter Payment Amount</Label>
          <Input
            id="payment-amount"
            type="number"
            value={paymentAmount}
            onChange={(e) => setPaymentAmount(e.target.value)}
            placeholder="Enter amount paid"
          />
        </div>

        <Button 
          className="w-full" 
          onClick={handleVerifyPayment}
          disabled={isVerifying || !paymentAmount}
        >
          {isVerifying ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Verifying Payment...
            </>
          ) : (
            "Verify Payment"
          )}
        </Button>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>For demonstration purposes only.</p>
        <p>In a production environment, this would integrate with a real payment gateway.</p>
      </div>
    </div>
  )
}
