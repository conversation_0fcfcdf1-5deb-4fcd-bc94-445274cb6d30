"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"

import { useToast } from "@/hooks/use-toast"
import { useSupabaseAuth } from "@/hooks/use-supabase-auth"
import { useSupabase } from "@/contexts/supabase-provider"
import { updateLastLogin, createUserInPublicTable } from "@/lib/user-helpers"
import { Package, Loader2, UserPlus, LogIn, Eye, EyeOff } from "lucide-react"

const loginFormSchema = z.object({
  email: z.string().min(1, "Email is required").refine((email) => {
    // Simple email validation that's more lenient than <PERSON><PERSON>'s built-in email()
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }, "Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

const signupFormSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
    .trim(),
  email: z.string().min(1, "Email is required").refine((email) => {
    // Simple email validation that's more lenient than Zod's built-in email()
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }, "Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Password must be at least 8 characters"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
})

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { signIn, signUp } = useSupabaseAuth()
  const { supabase } = useSupabase()
  const [isLoading, setIsLoading] = useState(false)
  const [showSignup, setShowSignup] = useState(false) // Toggle for showing signup section
  const [signupEnabled, setSignupEnabled] = useState(false) // Whether signup is enabled (set to false to hide signup)
  const [showLoginPassword, setShowLoginPassword] = useState(false) // Toggle for showing login password
  const [showSignupPassword, setShowSignupPassword] = useState(false) // Toggle for showing signup password
  const [showConfirmPassword, setShowConfirmPassword] = useState(false) // Toggle for showing confirm password



  // Check if signup should be enabled via query parameter
  useEffect(() => {
    const showSignup = searchParams.get('showSignup')
    if (showSignup === 'true') {
      setSignupEnabled(true)
    } else {
      setSignupEnabled(false)
      setShowSignup(false)
    }
  }, [searchParams])

  // Login form
  const loginForm = useForm<z.infer<typeof loginFormSchema>>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  // Signup form
  const signupForm = useForm<z.infer<typeof signupFormSchema>>({
    resolver: zodResolver(signupFormSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  })

  // Handle login submission
  async function onLoginSubmit(values: z.infer<typeof loginFormSchema>) {
    setIsLoading(true)

    try {
      // Trim the email to remove any accidental spaces
      const formattedEmail = values.email.trim()

      console.log("Login data:", {
        email: formattedEmail,
        emailValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formattedEmail)
      })

      try {
        // Sign in directly with Supabase instead of using the hook
        const { data, error } = await supabase.auth.signInWithPassword({
          email: formattedEmail,
          password: values.password,
        })

        if (error) {
          console.error("Login error:", error)
          toast({
            title: "Login Failed",
            description: error.message,
            variant: "destructive"
          })
          return
        }

        // Update the last login timestamp
        await updateLastLogin()

        console.log("Login successful, redirecting to check-branch page")
        toast({
          title: "Login Successful",
          description: "Checking your account...",
        })

        // No need to store auth flag in localStorage
        // The session cookie will be used for authentication

        // Redirect to the check-branch page which will verify if the user has a branch assigned
        console.log("Login successful, redirecting to check-branch page")
        // Use window.location to ensure we get the correct port
        window.location.href = '/check-branch'
      } catch (signInError: any) {
        console.error("Unexpected sign-in error:", signInError)
        toast({
          title: "Login Failed",
          description: "An unexpected error occurred during sign-in",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error("Login error:", error)
      toast({
        title: "Login Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle signup submission
  async function onSignupSubmit(values: z.infer<typeof signupFormSchema>) {
    setIsLoading(true)

    try {
      // Trim and format the name properly
      const formattedName = values.name.trim()
      // Trim the email to remove any accidental spaces
      const formattedEmail = values.email.trim()

      const metadata = {
        name: formattedName,
        full_name: formattedName, // Add both formats to ensure compatibility
      }

      console.log("Signup data:", {
        email: formattedEmail,
        name: formattedName,
        emailValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formattedEmail)
      })

      // Sign up the user with the hook
      const success = await signUp(formattedEmail, values.password, metadata)

      if (success) {
        // Create a user record in the public users table
        try {
          // Get the user that was just created
          const { data: { user } } = await supabase.auth.getUser()

          if (user) {
            // Use the helper function to create a user in the public table
            const publicUser = await createUserInPublicTable(
              user.id,
              formattedEmail,
              formattedName,
              'Manager' // Explicitly set role to Manager
            )

            if (!publicUser) {
              console.warn("User created in auth but not in public table")
            } else {
              console.log("User created successfully in public table:", publicUser)
            }
          }
        } catch (error: any) {
          console.error("Error creating user in public table:", error)
        }
      }

      toast({
        title: "Account Created",
        description: "Please check your email to confirm your account.",
      })
      // Reset form and switch to login tab
      signupForm.reset()
      setShowSignup(false)
    } catch (error: any) {
      console.error("Signup error:", error)
      toast({
        title: "Signup Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="space-y-2 text-center">
          <div className="flex justify-center mb-4">
            <Package className="h-16 w-16 text-primary" />
          </div>
          <CardTitle className="text-2xl">KPN Branch Management</CardTitle>
          <CardDescription>
            {showSignup ? "Create an account to manage your branch" : "Sign in to manage your branch operations"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {signupEnabled ? (
            <div className="mb-4">
              <div className="flex justify-between items-center">
                <Button
                  variant={showSignup ? "outline" : "default"}
                  className="w-1/2 rounded-r-none"
                  onClick={() => setShowSignup(false)}
                  type="button"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </Button>
                <Button
                  variant={showSignup ? "default" : "outline"}
                  className="w-1/2 rounded-l-none"
                  onClick={() => setShowSignup(true)}
                  type="button"
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  Sign Up
                </Button>
              </div>
            </div>
          ) : null}

          {!showSignup || !signupEnabled ? (
            // Login Form
            <Form {...loginForm}>
              <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <div className="font-medium">
                    <label htmlFor="login-email">Email</label>
                  </div>
                  <Input
                    id="login-email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    value={loginForm.watch("email")}
                    onChange={(e) => loginForm.setValue("email", e.target.value)}
                    className="w-full"
                  />
                  {loginForm.formState.errors.email && (
                    <p className="text-sm font-medium text-destructive">
                      {loginForm.formState.errors.email.message}
                    </p>
                  )}
                </div>
                <FormField
                  control={loginForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showLoginPassword ? "text" : "password"}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowLoginPassword(!showLoginPassword)}
                            tabIndex={-1}
                          >
                            {showLoginPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">
                              {showLoginPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </form>
            </Form>
          ) : signupEnabled ? (
            // Signup Form
            <Form {...signupForm}>
              <form onSubmit={signupForm.handleSubmit(onSignupSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <div className="font-medium">
                    <label htmlFor="fullname">Full Name</label>
                  </div>
                  <Input
                    id="fullname"
                    type="text"
                    placeholder="John Doe"
                    autoComplete="name"
                    value={signupForm.watch("name")}
                    onChange={(e) => signupForm.setValue("name", e.target.value)}
                    className="w-full"
                  />
                  {signupForm.formState.errors.name && (
                    <p className="text-sm font-medium text-destructive">
                      {signupForm.formState.errors.name.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="font-medium">
                    <label htmlFor="email">Email</label>
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    value={signupForm.watch("email")}
                    onChange={(e) => signupForm.setValue("email", e.target.value)}
                    className="w-full"
                  />
                  {signupForm.formState.errors.email && (
                    <p className="text-sm font-medium text-destructive">
                      {signupForm.formState.errors.email.message}
                    </p>
                  )}
                </div>
                <FormField
                  control={signupForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showSignupPassword ? "text" : "password"}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowSignupPassword(!showSignupPassword)}
                            tabIndex={-1}
                          >
                            {showSignupPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">
                              {showSignupPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={signupForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showConfirmPassword ? "text" : "password"}
                            {...field}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            tabIndex={-1}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                            <span className="sr-only">
                              {showConfirmPassword ? "Hide password" : "Show password"}
                            </span>
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    "Create Account"
                  )}
                </Button>
              </form>
            </Form>
          ) : null}
        </CardContent>
        <CardFooter className="flex justify-center">
          {!showSignup && (
            <Button variant="link" onClick={() => window.location.href = "/forgot-password"}>
              Forgot your password?
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}