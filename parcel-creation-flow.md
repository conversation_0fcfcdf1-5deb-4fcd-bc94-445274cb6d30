# Parcel Creation Flow

## 1. User Interface Flow

### Step 1: Sender Information
- User enters sender details:
  - Sender name
  - Sender phone number (10 digits)
  - Sender branch (selected from branch list)

### Step 2: Recipient Information
- User enters recipient details:
  - Recipient name
  - Recipient phone number (10 digits)
  - Recipient branch (selected from branch list)

### Step 3: Parcel Details
- User adds items:
  - Item type (selected from parcel types)
  - Item count
  - Item weight (if per-item weight option selected)
- User selects weight option:
  - Per-item weight
  - Total weight
- User enters declared value (optional)
- User enters e-Way bill number (required for values > ₹50,000)

### Step 4: Delivery Options
- User selects payment mode:
  - Paid
  - To Pay
- User selects delivery type:
  - Standard
  - Express
  - Premium
- System calculates suggested price based on:
  - Weight
  - Distance between branches
  - Parcel type
  - Delivery type
- User can adjust final price manually

### Step 5: Review & Confirm
- User reviews all entered information
- System displays total charges including GST
- User confirms booking

## 2. Technical Flow

### LR Number Generation
```javascript
// Format: BRANCH_CODE-YYYYMMDD-XXXX
const today = new Date();
const dateStr = today.getFullYear().toString() +
  (today.getMonth() + 1).toString().padStart(2, '0') +
  today.getDate().toString().padStart(2, '0');

// Get branch code from user's branch
let branchCode = 'KPN'; // Default fallback
if (userBranchId && branchList.length > 0) {
  const userBranch = branchList.find(branch => branch.branch_id === userBranchId);
  if (userBranch && userBranch.code) {
    branchCode = userBranch.code;
  }
}

// Generate sequential number (in production, this would be from DB)
const sequentialNumber = Math.floor(1000 + Math.random() * 9000);
const lrNumber = `${branchCode}-${dateStr}-${sequentialNumber}`;
```

### Data Preparation
```javascript
const parcelData = {
  lr_number: lrNumber,
  sender_name: formData.senderName,
  sender_phone: formData.senderPhone,
  sender_branch_id: senderBranchId,
  recipient_name: formData.recipientName,
  recipient_phone: formData.recipientPhone,
  delivery_branch_id: recipientBranchId,
  number_of_items: formData.items.reduce((total, item) => total + item.count, 0),
  item_type: formData.items[0]?.type ? parseInt(formData.items[0].type) : null,
  weight: formData.weightOption === 'total'
    ? parseFloat(formData.totalWeight)
    : formData.items.reduce((total, item) => total + (parseFloat(item.weight || '0') * item.count), 0),
  payment_mode: formData.paymentType === 'paid' ? 'Paid' : 'To Pay',
  delivery_charges: parseFloat(formData.finalPrice.toString()),
  base_price: parseFloat(formData.suggestedPrice.toString()),
  tax_details: JSON.stringify({
    gst: Math.round(formData.suggestedPrice * 0.18),
    loading_charges: Math.round(formData.suggestedPrice * 0.10),
    vehicle_charges: Math.round(formData.suggestedPrice * 0.15)
  }),
  total_amount: parseFloat(formData.finalPrice.toString()),
  current_status: 'Booked',
  expected_delivery_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  eway_bill_number: formData.eWayBillNo || null,
  declared_value: formData.value ? parseFloat(formData.value) : null,
  delivery_type: formData.deliveryType
};
```

### API Submission
1. First attempt: Direct Supabase client insertion
```javascript
const { data, error } = await supabase
  .from('parcels')
  .insert(parcelData)
  .select()
  .single();
```

2. Fallback: API route if direct insertion fails
```javascript
const response = await fetch('/api/parcels', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(parcelData),
});
```

### Payment Handling
- If payment mode is "Paid":
  - Open payment checking dialog
  - On payment confirmation, show payment success dialog
- If payment mode is "To Pay":
  - Directly show booking confirmation dialog

### Booking Completion
- Show booking confirmation dialog with LR number
- Option to generate receipt
- Option to share booking details via WhatsApp/SMS
- Reset form for new booking

## 3. Database Schema

### Parcels Table
- `parcel_id`: Primary key
- `lr_number`: Unique identifier (BRANCH_CODE-YYYYMMDD-XXXX)
- `sender_name`: String
- `sender_phone`: String (10 digits)
- `sender_branch_id`: Foreign key to branches table
- `recipient_name`: String
- `recipient_phone`: String (10 digits)
- `delivery_branch_id`: Foreign key to branches table
- `number_of_items`: Integer
- `item_type`: Foreign key to parcel_types table
- `weight`: Decimal
- `payment_mode`: String ("Paid" or "To Pay")
- `delivery_charges`: Decimal
- `base_price`: Decimal
- `tax_details`: JSON string
- `total_amount`: Decimal
- `current_status`: String ("Booked", "In Transit", etc.)
- `expected_delivery_date`: Date
- `eway_bill_number`: String (optional)
- `declared_value`: Decimal (optional)
- `delivery_type`: String ("standard", "express", "same_day")
- `booking_datetime`: Timestamp with timezone (default: now())



