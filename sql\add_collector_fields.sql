-- Add collector_details and recipient_id_details columns to parcels table
ALTER TABLE parcels 
ADD COLUMN collector_details JSONB DEFAULT NULL,
ADD COLUMN recipient_id_details JSONB DEFAULT NULL;

-- Comment on columns
COMMENT ON COLUMN parcels.collector_details IS 'JSON object containing details of the person who collected the parcel (name, phone, ID proof type, ID number)';
COMMENT ON COLUMN parcels.recipient_id_details IS 'JSON object containing ID proof details of the recipient (ID type, ID number)';

-- Update database.types.ts to include these new fields
-- You'll need to manually update the database.types.ts file to include:
-- collector_details: J<PERSON> | null;
-- recipient_id_details: J<PERSON> | null;
-- in the parcels Row, Insert, and Update types
