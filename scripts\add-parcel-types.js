// <PERSON>ript to add parcel types to the database
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Define parcel types
const parcelTypes = [
  {
    type_name: "Document",
    base_price: 50,
    special_handling_instructions: "Handle with care. Keep dry.",
  },
  {
    type_name: "Package",
    base_price: 100,
    special_handling_instructions: "Standard handling.",
  },
  {
    type_name: "Electronics",
    base_price: 150,
    special_handling_instructions: "Handle with care. Avoid moisture.",
  },
  {
    type_name: "Fragile",
    base_price: 200,
    special_handling_instructions: "Extremely fragile. Handle with extra care.",
  },
  {
    type_name: "Perishable",
    base_price: 180,
    special_handling_instructions: "Keep refrigerated. Deliver quickly.",
  },
  {
    type_name: "Clothing",
    base_price: 120,
    special_handling_instructions: "Keep dry.",
  },
  {
    type_name: "Books",
    base_price: 100,
    special_handling_instructions: "Keep dry. Stack flat.",
  },
];

async function addParcelTypes() {
  try {
    console.log("Adding parcel types...");

    for (const parcelType of parcelTypes) {
      // Check if parcel type already exists
      const { data: existingType, error: checkError } = await supabase
        .from("parceltypes")
        .select("type_id, type_name")
        .eq("type_name", parcelType.type_name)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        console.error(
          `Error checking for existing parcel type ${parcelType.type_name}:`,
          checkError,
        );
        continue;
      }

      if (existingType) {
        console.log(
          `Parcel type ${parcelType.type_name} already exists:`,
          existingType,
        );
        continue;
      }

      // Create the parcel type
      const { data, error } = await supabase
        .from("parceltypes")
        .insert([parcelType])
        .select();

      if (error) {
        console.error(
          `Error creating parcel type ${parcelType.type_name}:`,
          error,
        );
        continue;
      }

      console.log(
        `Parcel type ${parcelType.type_name} created successfully:`,
        data[0],
      );
    }

    console.log("Finished adding parcel types");
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function listParcelTypes() {
  try {
    console.log("Listing parcel types...");

    const { data, error } = await supabase
      .from("parceltypes")
      .select("*");

    if (error) {
      console.error("Error listing parcel types:", error);
      return;
    }

    console.log("Parcel types:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function main() {
  console.log("Starting script...");

  try {
    // Add parcel types
    await addParcelTypes();

    // List all parcel types
    await listParcelTypes();

    console.log("Script completed successfully!");
  } catch (error) {
    console.error("Error in main function:", error);
  }
}

main();
