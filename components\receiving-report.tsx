"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { FileText, Download, Share, Truck, Package, Calendar, MapPin } from "lucide-react"

interface ReceivingReportData {
  vehicle: {
    registration_number: string
    vehicle_type: string
    make_model: string
  }
  receiving_branch: {
    name: string
    code: string
  }
  receiving_date: string
  operator_name: string
  parcels: Array<{
    lr_number: string
    sender_name: string
    recipient_name: string
    sender_branch: string
    delivery_branch: string
    original_quantity: number
    received_quantity: number
    status: string
    warning?: string
  }>
  summary: {
    total_parcels: number
    total_items_received: number
    successful: number
    failed: number
  }
}

interface ReceivingReportProps {
  reportData: ReceivingReportData
  onClose?: () => void
}

export function ReceivingReport({ reportData, onClose }: ReceivingReportProps) {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [isSharing, setIsSharing] = useState(false)

  const generatePDF = async () => {
    setIsGeneratingPDF(true)
    try {
      // TODO: Implement PDF generation
      // This would typically use a library like jsPDF or call a backend service
      console.log("Generating PDF report...", reportData)
      
      // Placeholder for PDF generation
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // For now, just show a success message
      alert("PDF generation feature will be implemented soon!")
    } catch (error) {
      console.error("Error generating PDF:", error)
      alert("Failed to generate PDF report")
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const shareReport = async () => {
    setIsSharing(true)
    try {
      // TODO: Implement WhatsApp sharing
      // This would typically format the report data and use WhatsApp API
      console.log("Sharing report via WhatsApp...", reportData)
      
      // Placeholder for WhatsApp sharing
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // For now, just show a success message
      alert("WhatsApp sharing feature will be implemented soon!")
    } catch (error) {
      console.error("Error sharing report:", error)
      alert("Failed to share report")
    } finally {
      setIsSharing(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Receiving Report
            </CardTitle>
            <CardDescription>
              Vehicle receiving operation summary
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={generatePDF}
              disabled={isGeneratingPDF}
            >
              <Download className="h-4 w-4 mr-2" />
              {isGeneratingPDF ? "Generating..." : "Download PDF"}
            </Button>
            <Button 
              variant="outline" 
              onClick={shareReport}
              disabled={isSharing}
            >
              <Share className="h-4 w-4 mr-2" />
              {isSharing ? "Sharing..." : "Share via WhatsApp"}
            </Button>
            {onClose && (
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Report Header */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Truck className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Vehicle Information</span>
            </div>
            <div className="pl-6 space-y-1 text-sm">
              <p><strong>Registration:</strong> {reportData.vehicle.registration_number}</p>
              <p><strong>Type:</strong> {reportData.vehicle.vehicle_type}</p>
              <p><strong>Model:</strong> {reportData.vehicle.make_model}</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-green-600" />
              <span className="font-medium">Receiving Details</span>
            </div>
            <div className="pl-6 space-y-1 text-sm">
              <p><strong>Branch:</strong> {reportData.receiving_branch.name} ({reportData.receiving_branch.code})</p>
              <p><strong>Date:</strong> {formatDate(reportData.receiving_date)}</p>
              <p><strong>Operator:</strong> {reportData.operator_name}</p>
            </div>
          </div>
        </div>

        <Separator />

        {/* Summary */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2 mb-3">
            <Package className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800">Summary</span>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{reportData.summary.total_parcels}</div>
              <div className="text-blue-700">Total Parcels</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{reportData.summary.total_items_received}</div>
              <div className="text-green-700">Items Received</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{reportData.summary.successful}</div>
              <div className="text-green-700">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{reportData.summary.failed}</div>
              <div className="text-red-700">Failed</div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Parcel Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Parcel Details</h3>
          
          <div className="space-y-3">
            {reportData.parcels.map((parcel, index) => (
              <div key={parcel.lr_number} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <div className="font-medium text-lg">{parcel.lr_number}</div>
                    <div className="text-sm text-gray-600">
                      {parcel.sender_name} → {parcel.recipient_name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {parcel.sender_branch} → {parcel.delivery_branch}
                    </div>
                  </div>
                  <Badge variant={parcel.status === "success" ? "default" : "destructive"}>
                    {parcel.status === "success" ? "Received" : "Failed"}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Original Quantity:</span>
                    <span className="ml-2 font-medium">{parcel.original_quantity}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Received Quantity:</span>
                    <span className="ml-2 font-medium">{parcel.received_quantity}</span>
                  </div>
                </div>
                
                {parcel.warning && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                    <strong>Warning:</strong> {parcel.warning}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>This report was generated automatically by the KPN Parcel Service system.</p>
          <p>Report generated on: {formatDate(new Date().toISOString())}</p>
        </div>
      </CardContent>
    </Card>
  )
}

// Example usage component
export function ReceivingReportExample() {
  const sampleData: ReceivingReportData = {
    vehicle: {
      registration_number: "KA01AB1234",
      vehicle_type: "Truck",
      make_model: "Tata LPT 1613"
    },
    receiving_branch: {
      name: "Bangalore Main",
      code: "BLR001"
    },
    receiving_date: new Date().toISOString(),
    operator_name: "John Doe",
    parcels: [
      {
        lr_number: "BLR001-20241201-0001",
        sender_name: "ABC Company",
        recipient_name: "XYZ Store",
        sender_branch: "Mumbai Central",
        delivery_branch: "Bangalore Main",
        original_quantity: 5,
        received_quantity: 5,
        status: "success"
      },
      {
        lr_number: "BLR001-20241201-0002",
        sender_name: "DEF Industries",
        recipient_name: "PQR Enterprises",
        sender_branch: "Delhi North",
        delivery_branch: "Bangalore Main",
        original_quantity: 3,
        received_quantity: 4,
        status: "success",
        warning: "Received 4 items, but parcel originally had 3 items"
      }
    ],
    summary: {
      total_parcels: 2,
      total_items_received: 9,
      successful: 2,
      failed: 0
    }
  }

  return <ReceivingReport reportData={sampleData} />
}
