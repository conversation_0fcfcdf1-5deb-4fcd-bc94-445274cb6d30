import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/remittances/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Get remittance by ID
    const { data: remittance, error } = await supabase
      .from("remittance")
      .select(`
        *,
        branch:branches(name, code),
        submitter:users!submitted_by(name)
      `)
      .eq("remittance_id", id)
      .single();

    if (error) {
      console.error("Error fetching remittance:", error);
      return NextResponse.json({ error: "Failed to fetch remittance" }, {
        status: 500,
      });
    }

    if (!remittance) {
      return NextResponse.json({ error: "Remittance not found" }, {
        status: 404,
      });
    }

    // Get associated financial transaction
    const { data: transaction, error: txnError } = await supabase
      .from("financial_transactions")
      .select("*")
      .eq("reference_number", `RMT-${id}`)
      .maybeSingle();

    if (txnError) {
      console.error("Error fetching associated transaction:", txnError);
      // Continue anyway, as we have the remittance
    }

    return NextResponse.json({
      remittance,
      transaction: transaction || null,
    });
  } catch (error: any) {
    console.error(`Error in GET /api/remittances/${params.id}:`, error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// PATCH /api/remittances/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = params.id;
    const body = await request.json();

    // Get existing remittance
    const { data: existingRemittance, error: fetchError } = await supabase
      .from("remittance")
      .select("*")
      .eq("remittance_id", id)
      .single();

    if (fetchError) {
      console.error("Error fetching remittance:", fetchError);
      return NextResponse.json({ error: "Failed to fetch remittance" }, {
        status: 500,
      });
    }

    if (!existingRemittance) {
      return NextResponse.json({ error: "Remittance not found" }, {
        status: 404,
      });
    }

    // Update remittance
    const { data: updatedRemittance, error: updateError } = await supabase
      .from("remittance")
      .update({
        status: body.status || existingRemittance.status,
        proof_url: body.proof_url || existingRemittance.proof_url,
        reference_id: body.reference_id || existingRemittance.reference_id,
      })
      .eq("remittance_id", id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating remittance:", updateError);
      return NextResponse.json({ error: "Failed to update remittance" }, {
        status: 500,
      });
    }

    // If status is being updated, also update the associated financial transaction
    if (body.status && body.status !== existingRemittance.status) {
      const txnApprovalStatus = body.status === "Approved"
        ? "Approved"
        : "Pending";

      const { error: txnUpdateError } = await supabase
        .from("financial_transactions")
        .update({
          status: txnApprovalStatus,
        })
        .eq("reference_number", `RMT-${id}`);

      if (txnUpdateError) {
        console.error("Error updating associated transaction:", txnUpdateError);
        // Continue anyway, as the remittance was updated successfully
      }
    }

    return NextResponse.json({ remittance: updatedRemittance });
  } catch (error: any) {
    console.error(`Error in PATCH /api/remittances/${params.id}:`, error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
