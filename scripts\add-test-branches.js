// Script to add test branches to the database
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function to get city ID by name
async function getCityIdByName(cityName) {
  const { data, error } = await supabase
    .from("cities")
    .select("city_id")
    .eq("name", cityName)
    .single();

  if (error) {
    console.error(`Error getting city ID for ${cityName}:`, error);
    return null;
  }

  return data.city_id;
}

// Define test branches
const testBranches = [
  {
    name: "Chennai Central",
    code: "CHE",
    address: "45, Anna <PERSON>, Chennai - 600002",
    phone: "+91 44 2345 6789",
    email: "<EMAIL>",
    operating_hours: JSON.stringify({
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "15:00" },
      sunday: { open: "closed", close: "closed" },
    }),
    status: "Active",
    location_coordinates: JSON.stringify({ lat: 13.0827, lng: 80.2707 }),
    branch_type: "Main",
    service_area: "Chennai Metropolitan Area",
    city: "Chennai",
  },
  {
    name: "Coimbatore Main",
    code: "CBE",
    address: "123, DB Road, RS Puram, Coimbatore - 641002",
    phone: "+91 422 2345 6789",
    email: "<EMAIL>",
    operating_hours: JSON.stringify({
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "15:00" },
      sunday: { open: "closed", close: "closed" },
    }),
    status: "Active",
    location_coordinates: JSON.stringify({ lat: 11.0168, lng: 76.9558 }),
    branch_type: "Main",
    service_area: "Coimbatore District",
    city: "Coimbatore",
  },
  {
    name: "Madurai Central",
    code: "MDU",
    address: "56, North Veli Street, Madurai - 625001",
    phone: "+91 452 2345 6789",
    email: "<EMAIL>",
    operating_hours: JSON.stringify({
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "15:00" },
      sunday: { open: "closed", close: "closed" },
    }),
    status: "Active",
    location_coordinates: JSON.stringify({ lat: 9.9252, lng: 78.1198 }),
    branch_type: "Main",
    service_area: "Madurai District",
    city: "Madurai",
  },
  {
    name: "Salem Junction",
    code: "SLM",
    address: "78, Saradha College Road, Salem - 636007",
    phone: "+91 427 2345 6789",
    email: "<EMAIL>",
    operating_hours: JSON.stringify({
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "15:00" },
      sunday: { open: "closed", close: "closed" },
    }),
    status: "Active",
    location_coordinates: JSON.stringify({ lat: 11.6643, lng: 78.1460 }),
    branch_type: "Main",
    service_area: "Salem District",
    city: "Salem",
  },
  {
    name: "Trichy Central",
    code: "TRY",
    address: "34, Bharathiar Salai, Trichy - 620001",
    phone: "+91 431 2345 6789",
    email: "<EMAIL>",
    operating_hours: JSON.stringify({
      monday: { open: "09:00", close: "18:00" },
      tuesday: { open: "09:00", close: "18:00" },
      wednesday: { open: "09:00", close: "18:00" },
      thursday: { open: "09:00", close: "18:00" },
      friday: { open: "09:00", close: "18:00" },
      saturday: { open: "10:00", close: "15:00" },
      sunday: { open: "closed", close: "closed" },
    }),
    status: "Active",
    location_coordinates: JSON.stringify({ lat: 10.7905, lng: 78.7047 }),
    branch_type: "Main",
    service_area: "Trichy District",
    city: "Trichy",
  },
];

async function addTestBranches() {
  try {
    console.log("Adding test branches...");

    // First, make sure cities exist
    console.log("Checking cities...");
    for (const branch of testBranches) {
      if (!branch.city) continue;

      // Check if city exists
      const { data: existingCity, error: cityCheckError } = await supabase
        .from("cities")
        .select("city_id, name")
        .eq("name", branch.city)
        .single();

      if (cityCheckError && cityCheckError.code !== "PGRST116") {
        console.error(
          `Error checking for city ${branch.city}:`,
          cityCheckError,
        );
        continue;
      }

      if (!existingCity) {
        console.log(`City ${branch.city} does not exist, creating it...`);

        // Create the city
        const { data: newCity, error: cityCreateError } = await supabase
          .from("cities")
          .insert([{
            name: branch.city,
            state: "Tamil Nadu",
            country: "India",
          }])
          .select();

        if (cityCreateError) {
          console.error(`Error creating city ${branch.city}:`, cityCreateError);
          continue;
        }

        console.log(`City ${branch.city} created successfully:`, newCity[0]);
      } else {
        console.log(`City ${branch.city} already exists:`, existingCity);
      }
    }

    // Now add branches with city_id
    for (const branch of testBranches) {
      // Get city_id
      let cityId = null;
      if (branch.city) {
        const { data: city, error: cityError } = await supabase
          .from("cities")
          .select("city_id")
          .eq("name", branch.city)
          .single();

        if (cityError) {
          console.error(`Error getting city_id for ${branch.city}:`, cityError);
          continue;
        }

        cityId = city.city_id;
      }

      // Check if branch with this code already exists
      const { data: existingBranch, error: checkError } = await supabase
        .from("branches")
        .select("branch_id, name, code")
        .eq("code", branch.code)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        console.error(
          `Error checking for existing branch ${branch.code}:`,
          checkError,
        );
        continue;
      }

      if (existingBranch) {
        console.log(
          `Branch ${branch.code} already exists, updating with city_id...`,
          existingBranch,
        );

        // Update the branch with city_id
        const { data: updatedBranch, error: updateError } = await supabase
          .from("branches")
          .update({ city_id: cityId })
          .eq("branch_id", existingBranch.branch_id)
          .select();

        if (updateError) {
          console.error(
            `Error updating branch ${branch.code} with city_id:`,
            updateError,
          );
          continue;
        }

        console.log(
          `Branch ${branch.code} updated with city_id:`,
          updatedBranch[0],
        );
        continue;
      }

      // Create a copy of the branch object without the city property
      const branchToInsert = { ...branch };
      delete branchToInsert.city;

      // Add city_id
      if (cityId) {
        branchToInsert.city_id = cityId;
      }

      // Create the branch
      const { data, error } = await supabase
        .from("branches")
        .insert([branchToInsert])
        .select();

      if (error) {
        console.error(`Error creating branch ${branch.code}:`, error);
        continue;
      }

      console.log(`Branch ${branch.code} created successfully:`, data[0]);
    }

    console.log("Finished adding test branches");
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function listBranches() {
  try {
    console.log("Listing branches...");

    const { data, error } = await supabase
      .from("branches")
      .select("*");

    if (error) {
      console.error("Error listing branches:", error);
      return;
    }

    console.log("Branches:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function main() {
  console.log("Starting script...");

  try {
    // Add test branches
    await addTestBranches();

    // List all branches
    await listBranches();

    console.log("Script completed successfully!");
  } catch (error) {
    console.error("Error in main function:", error);
  }
}

main();
