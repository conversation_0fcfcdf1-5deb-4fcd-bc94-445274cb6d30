import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/validate-lr-simple
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number");

    if (!lr_number) {
      return NextResponse.json(
        { valid: false, message: "LR number is required" },
        { status: 400 },
      );
    }

    // Get the parcel details - only check if it exists and has valid status
    const { data: parcel, error: parcelError } = await supabase
      .from("parcels")
      .select(`
        *,
        sender_branch:branches!sender_branch_id(name, code),
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    if (parcelError || !parcel) {
      console.error("Error finding parcel:", parcelError);
      return NextResponse.json(
        { 
          valid: false, 
          message: "Parcel not found" 
        },
        { status: 404 },
      );
    }

    // Check if parcel status allows receiving
    const allowedStatuses = ["Booked", "Loaded", "Received"];
    if (!allowedStatuses.includes(parcel.current_status)) {
      return NextResponse.json(
        { 
          valid: false, 
          message: `Cannot receive parcel with status: ${parcel.current_status}. Only Booked, Loaded, or Received parcels can be received.` 
        },
        { status: 400 },
      );
    }

    // Return the parcel details
    return NextResponse.json({
      valid: true,
      parcel: {
        parcel_id: parcel.parcel_id,
        lr_number: parcel.lr_number,
        sender_name: parcel.sender_name,
        recipient_name: parcel.recipient_name,
        number_of_items: parcel.number_of_items,
        current_status: parcel.current_status,
        sender_branch: parcel.sender_branch,
        delivery_branch: parcel.delivery_branch,
        weight: parcel.weight,
        item_type: parcel.item_type,
        total_amount: parcel.total_amount,
        payment_mode: parcel.payment_mode
      }
    });

  } catch (error: any) {
    console.error("Error in GET /api/parcels/validate-lr-simple:", error);
    return NextResponse.json(
      { 
        valid: false, 
        message: "Internal server error" 
      },
      { status: 500 }
    );
  }
}
