import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// Helper function to get today's date range
function getTodayDateRange() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const todayStart = today.toISOString();

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  const todayEnd = tomorrow.toISOString();

  return { todayStart, todayEnd };
}

// Helper function to get date range for a specific date
function getDateRange(dateString: string | null) {
  if (!dateString) {
    return getTodayDateRange();
  }

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.warn(
        `Invalid date string: ${dateString}, using today's date instead`,
      );
      return getTodayDateRange();
    }

    date.setHours(0, 0, 0, 0);
    const dateStart = date.toISOString();

    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    const dateEnd = nextDay.toISOString();

    return { dateStart, dateEnd };
  } catch (error) {
    console.warn(
      `Error parsing date: ${dateString}, using today's date instead`,
      error,
    );
    return getTodayDateRange();
  }
}

// GET /api/branch-balance
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const branch_id = url.searchParams.get("branch_id");
    const date = url.searchParams.get("date"); // Optional date parameter

    if (!branch_id) {
      return NextResponse.json({ error: "Branch ID is required" }, {
        status: 400,
      });
    }

    // Get date range
    let { dateStart, dateEnd } = date
      ? getDateRange(date)
      : getTodayDateRange();

    // Ensure we have valid date strings for the query
    if (!dateStart || !dateEnd) {
      console.warn("Invalid date range, using today's date range");
      const { todayStart, todayEnd } = getTodayDateRange();
      dateStart = todayStart;
      dateEnd = todayEnd;
    }

    // Get today's transactions
    const { data: transactions, error: txnError } = await supabase
      .from("financial_transactions")
      .select("*")
      .eq("branch_id", branch_id)
      .gte("transaction_date", dateStart)
      .lt("transaction_date", dateEnd);

    if (txnError) {
      console.error("Error fetching transactions:", txnError);
      return NextResponse.json({ error: "Failed to fetch transactions" }, {
        status: 500,
      });
    }

    // Calculate totals
    const income = transactions
      .filter((t) => {
        // Only include collections that are approved
        if (
          t.transaction_type !== "Collection" ||
          t.approval_status !== "Approved"
        ) {
          return false;
        }

        // Check if this transaction should be included in branch balance
        // Parse metadata if it exists
        let metadata = {};
        try {
          if (t.metadata) {
            metadata = typeof t.metadata === "string"
              ? JSON.parse(t.metadata)
              : t.metadata;
          }
        } catch (e) {
          console.error("Error parsing transaction metadata:", e);
        }

        // If metadata explicitly says not to include in branch balance, exclude it
        if (metadata && metadata.include_in_branch_balance === false) {
          return false;
        }

        // If it's an online payment (not cash), exclude it from branch balance
        // This is a fallback for transactions without metadata
        if (
          t.payment_method &&
          ["online", "upi", "card", "bank_transfer"].includes(
            t.payment_method.toLowerCase(),
          ) &&
          !metadata.include_in_branch_balance
        ) {
          return false;
        }

        return true;
      })
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const expenses = transactions
      .filter((t) =>
        t.transaction_type === "Expense" && t.approval_status === "Approved"
      )
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const remittances = transactions
      .filter((t) =>
        t.transaction_type === "Remittance" && t.approval_status === "Approved"
      )
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    // Get the ledger entry for the date
    const dateObj = date ? new Date(date) : new Date();
    const formattedDate = dateObj.toISOString().split("T")[0];

    const { data: ledgerEntry, error: ledgerError } = await supabase
      .from("accountsledger")
      .select("*")
      .eq("branch_id", branch_id)
      .eq("ledger_date", formattedDate)
      .maybeSingle();

    if (ledgerError) {
      console.error("Error fetching ledger entry:", ledgerError);
      // Continue anyway, we'll calculate without the ledger entry
    }

    // Get previous day's ledger entry for opening balance
    const prevDate = new Date(dateObj);
    prevDate.setDate(prevDate.getDate() - 1);
    const formattedPrevDate = prevDate.toISOString().split("T")[0];

    const { data: prevLedgerEntry, error: prevLedgerError } = await supabase
      .from("accountsledger")
      .select("closing_balance")
      .eq("branch_id", branch_id)
      .eq("ledger_date", formattedPrevDate)
      .maybeSingle();

    if (prevLedgerError) {
      console.error("Error fetching previous ledger entry:", prevLedgerError);
      // Continue anyway, we'll use 0 as the opening balance
    }

    // Calculate opening and closing balances
    const openingBalance = prevLedgerEntry?.closing_balance || 0;
    // We'll calculate the closing balance after getting the approved expenses from the expenses table

    // Get pending expenses
    const { data: pendingExpenses, error: pendingExpError } = await supabase
      .from("expenses")
      .select("amount")
      .eq("branch_id", branch_id)
      .eq("approval_status", "Pending");

    if (pendingExpError) {
      console.error("Error fetching pending expenses:", pendingExpError);
      // Continue anyway, we'll use 0 as the pending expenses
    }

    const totalPendingExpenses = pendingExpenses
      ? pendingExpenses.reduce((sum, e) => sum + parseFloat(e.amount), 0)
      : 0;

    // Get approved expenses (for the current day)
    const { data: approvedExpenses, error: approvedExpError } = await supabase
      .from("expenses")
      .select("amount")
      .eq("branch_id", branch_id)
      .eq("approval_status", "Approved")
      .gte("approved_at", dateStart)
      .lt("approved_at", dateEnd);

    if (approvedExpError) {
      console.error("Error fetching approved expenses:", approvedExpError);
      // Continue anyway, we'll use the expenses from financial_transactions
    }

    // Use the approved expenses from the expenses table if available
    const totalApprovedExpenses = approvedExpenses
      ? approvedExpenses.reduce((sum, e) => sum + parseFloat(e.amount), 0)
      : expenses;

    // Calculate closing balance using the approved expenses
    const closingBalance = openingBalance + income - totalApprovedExpenses -
      remittances;

    // Get pending remittances
    const { data: pendingRemittances, error: pendingRemError } = await supabase
      .from("remittance")
      .select("remittable_amount")
      .eq("branch_id", branch_id)
      .eq("status", "Submitted");

    if (pendingRemError) {
      console.error("Error fetching pending remittances:", pendingRemError);
      // Continue anyway, we'll use 0 as the pending remittances
    }

    const totalPendingRemittances = pendingRemittances
      ? pendingRemittances.reduce(
        (sum, r) => sum + parseFloat(r.remittable_amount),
        0,
      )
      : 0;

    return NextResponse.json({
      date: formattedDate,
      branch_id: parseInt(branch_id),
      opening_balance: openingBalance,
      income,
      expenses: totalApprovedExpenses, // Use the count from expenses table
      remittances,
      closing_balance: closingBalance,
      pending_expenses: totalPendingExpenses,
      pending_remittances: totalPendingRemittances,
      ledger_entry: ledgerEntry || null,
    });
  } catch (error: any) {
    console.error("Error in GET /api/branch-balance:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
