import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'db', 'parcel_status_history_migration.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return NextResponse.json({ error: 'Failed to execute migration' }, { status: 500 });
    }
    
    return NextResponse.json({ message: 'Parcel status history migration applied successfully' });
  } catch (error) {
    console.error('Error in parcel status history migration:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
