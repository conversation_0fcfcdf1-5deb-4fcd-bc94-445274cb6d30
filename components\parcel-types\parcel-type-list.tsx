'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, Plus, Search, Edit, Trash } from 'lucide-react';
import { ParcelType } from '@/lib/db-helpers';
import { ParcelTypeDialog } from './parcel-type-dialog';
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog';

export function ParcelTypeList() {
  const [parcelTypes, setParcelTypes] = useState<ParcelType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedParcelType, setSelectedParcelType] = useState<ParcelType | null>(null);
  const { toast } = useToast();

  // Fetch parcel types
  const fetchParcelTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/parceltypes');
      
      if (!response.ok) {
        throw new Error('Failed to fetch parcel types');
      }
      
      const data = await response.json();
      setParcelTypes(data);
    } catch (error: any) {
      console.error('Error fetching parcel types:', error);
      toast({
        title: 'Error',
        description: 'Failed to load parcel types',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load parcel types on component mount
  useEffect(() => {
    fetchParcelTypes();
  }, []);

  // Handle search
  const filteredParcelTypes = parcelTypes.filter((type) =>
    type.type_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle edit
  const handleEdit = (parcelType: ParcelType) => {
    setSelectedParcelType(parcelType);
    setIsDialogOpen(true);
  };

  // Handle delete
  const handleDelete = (parcelType: ParcelType) => {
    setSelectedParcelType(parcelType);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedParcelType) return;
    
    try {
      const response = await fetch(`/api/parceltypes/${selectedParcelType.type_id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete parcel type');
      }
      
      toast({
        title: 'Success',
        description: 'Parcel type deleted successfully',
      });
      
      // Refresh the list
      fetchParcelTypes();
    } catch (error: any) {
      console.error('Error deleting parcel type:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete parcel type',
        variant: 'destructive',
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedParcelType(null);
    }
  };

  // Handle dialog close
  const handleDialogClose = (refresh: boolean) => {
    setIsDialogOpen(false);
    setSelectedParcelType(null);
    
    if (refresh) {
      fetchParcelTypes();
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Parcel Types</CardTitle>
            <CardDescription>Manage parcel types and pricing</CardDescription>
          </div>
          <Button onClick={() => setIsDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add New Type
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search parcel types..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {loading ? (
          <div className="flex h-[300px] items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type Name</TableHead>
                  <TableHead>Base Price</TableHead>
                  <TableHead>Per KG Rate</TableHead>
                  <TableHead>Weight Restriction</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredParcelTypes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      No parcel types found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredParcelTypes.map((type) => (
                    <TableRow key={type.type_id}>
                      <TableCell className="font-medium">{type.type_name}</TableCell>
                      <TableCell>₹{type.base_price?.toFixed(2) || '0.00'}</TableCell>
                      <TableCell>₹{type.per_kg_rate?.toFixed(2) || '0.00'}</TableCell>
                      <TableCell>
                        {type.weight_restriction ? `${type.weight_restriction} kg` : 'None'}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(type)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(type)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {/* Parcel Type Dialog */}
      <ParcelTypeDialog
        open={isDialogOpen}
        onClose={handleDialogClose}
        parcelType={selectedParcelType}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        open={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Parcel Type"
        description={`Are you sure you want to delete the parcel type "${selectedParcelType?.type_name}"? This action cannot be undone.`}
      />
    </Card>
  );
}
