import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/financial-transactions
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const transaction_type = url.searchParams.get("transaction_type");
    const branch_id = url.searchParams.get("branch_id");
    const from_date = url.searchParams.get("from_date");
    const to_date = url.searchParams.get("to_date");
    const approval_status = url.searchParams.get("approval_status");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    // Build query
    let query = supabase
      .from("financial_transactions")
      .select(`
        *,
        branch:branches(name, code)
      `);

    // Add filters if provided
    if (transaction_type) {
      query = query.eq("transaction_type", transaction_type);
    }

    if (branch_id) {
      query = query.eq("branch_id", branch_id);
    }

    if (from_date) {
      try {
        // Validate date
        const fromDateObj = new Date(from_date);
        if (!isNaN(fromDateObj.getTime())) {
          query = query.gte("transaction_date", fromDateObj.toISOString());
        } else {
          console.warn(`Invalid from_date: ${from_date}, ignoring this filter`);
        }
      } catch (error) {
        console.warn(
          `Error parsing from_date: ${from_date}, ignoring this filter`,
          error,
        );
      }
    }

    if (to_date) {
      try {
        // Validate date
        const toDateObj = new Date(to_date);
        if (!isNaN(toDateObj.getTime())) {
          query = query.lte("transaction_date", toDateObj.toISOString());
        } else {
          console.warn(`Invalid to_date: ${to_date}, ignoring this filter`);
        }
      } catch (error) {
        console.warn(
          `Error parsing to_date: ${to_date}, ignoring this filter`,
          error,
        );
      }
    }

    if (approval_status) {
      query = query.eq("status", approval_status);
    }

    // Add pagination
    query = query.order("transaction_date", { ascending: false })
      .range(offset, offset + limit - 1);

    // Execute query
    const { data: transactions, error, count } = await query;

    if (error) {
      console.error("Error fetching financial transactions:", error);
      return NextResponse.json({
        error: "Failed to fetch financial transactions",
      }, {
        status: 500,
      });
    }

    return NextResponse.json({ transactions, count });
  } catch (error: any) {
    console.error("Error in GET /api/financial-transactions:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/financial-transactions
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.branch_id || !body.transaction_type || !body.amount) {
      return NextResponse.json(
        {
          error: "Missing required fields: branch_id, transaction_type, amount",
        },
        { status: 400 },
      );
    }

    // Process metadata if provided
    let metadata = null;
    if (body.metadata) {
      // If metadata is provided as a string, keep it as is
      // If it's an object, stringify it
      metadata = typeof body.metadata === "string"
        ? body.metadata
        : JSON.stringify(body.metadata);
    }

    // Create financial transaction
    const { data: transaction, error } = await supabase
      .from("financial_transactions")
      .insert({
        branch_id: body.branch_id,
        transaction_type: body.transaction_type,
        amount: body.amount,
        payment_method: body.payment_mode || null,
        reference_number: body.reference_number || null,
        description: body.description || null,
        attachment_url: body.attachment_url || null,
        status: body.status || body.approval_status || "Pending", // Use status if provided, otherwise use approval_status
        approval_status: body.approval_status || body.status || "Pending", // Use approval_status if provided, otherwise use status
        transaction_date: new Date().toISOString(),
        metadata: metadata,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating financial transaction:", error);
      return NextResponse.json({
        error: "Failed to create financial transaction",
      }, {
        status: 500,
      });
    }

    return NextResponse.json({ transaction });
  } catch (error: any) {
    console.error("Error in POST /api/financial-transactions:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
