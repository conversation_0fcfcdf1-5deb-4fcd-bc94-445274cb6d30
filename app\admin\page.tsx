"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { DashboardShell } from "@/components/dashboard-shell"
import { PageLayout } from "@/components/page-layout"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

export default function AdminPage() {
  const router = useRouter()

  return (
    <PageLayout
      title="Admin Dashboard"
      subtitle="System administration tools"
    >
      <DashboardShell>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Database Migrations</CardTitle>
              <CardDescription>
                Run database migrations to update the schema
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Apply database schema changes and updates.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => router.push('/admin/migrations')}
                className="w-full"
              >
                Go to Migrations
              </Button>
            </CardFooter>
          </Card>
        </div>
      </DashboardShell>
    </PageLayout>
  )
}
