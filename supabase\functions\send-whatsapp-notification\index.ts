// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// CORS headers for the function
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// WhatsApp API configuration
const WATI_API_URL =
  "https://live-mt-server.wati.io/7454/api/v1/sendTemplateMessages";
const WATI_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Is61XbGlbosFzbbOupy8fer0oI2Hx8lnmvCNbsCtK4E";

// Define parcel status type
type ParcelStatus =
  | "Booked"
  | "In Transit"
  | "To Be Received"
  | "To Be Delivered"
  | "Delivered";

// Template mapping based on parcel status
const statusToTemplate: Record<ParcelStatus, string> = {
  "Booked": "booking_confirmed",
  "In Transit": "in_transit_2",
  "To Be Received": "arrived_at_hub",
  "To Be Delivered": "arrived_at_hub",
  "Delivered": "delivered",
};

// Broadcast name mapping based on parcel status
const statusToBroadcastName: Record<ParcelStatus, string> = {
  "Booked": "booking-confirmation",
  "In Transit": "in-transit-update",
  "To Be Received": "hub-arrival-notice",
  "To Be Delivered": "hub-arrival-notice",
  "Delivered": "delivery-confirmation",
};

// Function to send WhatsApp notification
async function sendWhatsAppNotification(
  templateName: string,
  broadcastName: string,
  phoneNumber: string,
  params: { name: string; value: string }[],
) {
  try {
    // Format phone number (ensure it has country code)
    const formattedPhone = phoneNumber.startsWith("91")
      ? phoneNumber
      : phoneNumber.startsWith("+91")
      ? phoneNumber.substring(1)
      : `91${phoneNumber}`;

    const payload = {
      template_name: templateName,
      broadcast_name: broadcastName,
      receivers: [
        {
          whatsappNumber: formattedPhone,
          customParams: params,
        },
      ],
    };

    const response = await fetch(WATI_API_URL, {
      method: "POST",
      headers: {
        "accept": "*/*",
        "Authorization": `Bearer ${WATI_TOKEN}`,
        "Content-Type": "application/json-patch+json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`WhatsApp API error: ${response.status} - ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error sending WhatsApp notification:", error);
    throw error;
  }
}

// Main function to handle the webhook request
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get the request body
    const { record, old_record } = await req.json() as {
      record: {
        parcel_id: number;
        current_status: string;
        [key: string]: unknown;
      };
      old_record: { current_status: string; [key: string]: unknown };
    };

    // Check if this is a parcel status change
    if (
      !record || !old_record ||
      record.current_status === old_record.current_status
    ) {
      return new Response(
        JSON.stringify({ message: "No status change detected" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }

    // Get the new status
    const newStatus = record.current_status as ParcelStatus;

    // Check if we have a template for this status
    if (!Object.keys(statusToTemplate).includes(newStatus)) {
      return new Response(
        JSON.stringify({
          message: `No template configured for status: ${newStatus}`,
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }

    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization") || "" },
        },
      },
    );

    // Get more details about the parcel
    const { data: parcelData, error: parcelError } = await supabaseClient
      .from("parcels")
      .select(`
        *,
        sender_branch:branches!parcels_sender_branch_id_fkey(name),
        delivery_branch:branches!parcels_delivery_branch_id_fkey(name)
      `)
      .eq("parcel_id", record.parcel_id)
      .single();

    if (parcelError) {
      console.error("Error fetching parcel details:", parcelError);
      return new Response(
        JSON.stringify({ error: "Failed to fetch parcel details" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Get the status history for this parcel
    const { data: statusHistory, error: historyError } = await supabaseClient
      .from("parcel_status_history")
      .select("*")
      .eq("parcel_id", record.parcel_id)
      .order("timestamp", { ascending: false })
      .limit(1);

    if (historyError) {
      console.error("Error fetching status history:", historyError);
    }

    // Get the latest status update
    const latestStatusUpdate = statusHistory && statusHistory.length > 0
      ? statusHistory[0]
      : null;

    // Prepare WhatsApp template parameters based on status
    let templateParams: { name: string; value: string }[] = [];
    const templateName = statusToTemplate[newStatus];
    const broadcastName = statusToBroadcastName[newStatus];

    switch (newStatus) {
      case "Booked":
        templateParams = [
          { name: "var1", value: parcelData.sender_name },
          { name: "var2", value: parcelData.lr_number },
          {
            name: "var3",
            value: new Date(parcelData.booking_datetime).toLocaleDateString(),
          },
          {
            name: "var4",
            value: parcelData.sender_branch?.name || "our branch",
          },
        ];
        break;

      case "In Transit":
        templateParams = [
          { name: "var1", value: parcelData.sender_name },
          { name: "var2", value: parcelData.lr_number },
          {
            name: "var3",
            value: latestStatusUpdate?.location ||
              parcelData.sender_branch?.name || "our branch",
          },
          {
            name: "var4",
            value: latestStatusUpdate
              ? new Date(latestStatusUpdate.timestamp).toLocaleString()
              : new Date().toLocaleString(),
          },
          {
            name: "var5",
            value: parcelData.expected_delivery_date
              ? new Date(parcelData.expected_delivery_date).toLocaleDateString()
              : "soon",
          },
        ];
        break;

      case "To Be Received":
      case "To Be Delivered":
        templateParams = [
          { name: "var1", value: parcelData.sender_name },
          { name: "var2", value: parcelData.lr_number },
          {
            name: "var3",
            value: parcelData.delivery_branch?.name || "destination",
          },
          {
            name: "var4",
            value: latestStatusUpdate
              ? new Date(latestStatusUpdate.timestamp).toLocaleString()
              : new Date().toLocaleString(),
          },
        ];
        break;

      case "Delivered":
        templateParams = [
          { name: "var1", value: parcelData.sender_name },
          { name: "var2", value: parcelData.lr_number },
          {
            name: "var3",
            value: parcelData.actual_delivery_date
              ? new Date(parcelData.actual_delivery_date).toLocaleString()
              : new Date().toLocaleString(),
          },
          {
            name: "var4",
            value: parcelData.delivery_branch?.name || "destination",
          },
        ];
        break;
    }

    // Send the WhatsApp notification if we have a phone number
    if (parcelData.sender_phone) {
      try {
        const result = await sendWhatsAppNotification(
          templateName,
          broadcastName,
          parcelData.sender_phone,
          templateParams,
        );

        return new Response(
          JSON.stringify({
            success: true,
            message: `WhatsApp notification sent for status: ${newStatus}`,
            result,
          }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" } },
        );
      } catch (error) {
        console.error("Error sending WhatsApp notification:", error);
        return new Response(
          JSON.stringify({
            error: `Failed to send WhatsApp notification: ${error.message}`,
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          },
        );
      }
    } else {
      return new Response(
        JSON.stringify({
          message: "No sender phone number available for notification",
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }
  } catch (error: unknown) {
    console.error("Error processing request:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(
      JSON.stringify({ error: errorMessage }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});

/* To invoke:
  1. Go to your Supabase project dashboard
  2. Navigate to Database -> Webhooks
  3. Create a new webhook with the following settings:
     - Name: send-whatsapp-notification
     - Table: parcels
     - Events: UPDATE
     - Filter: current_status
     - URL: https://your-project-ref.supabase.co/functions/v1/send-whatsapp-notification
     - HTTP Method: POST
     - Enable: Yes
*/
