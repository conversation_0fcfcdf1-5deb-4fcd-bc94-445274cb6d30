// Script to check if authentication is working correctly
const { createClient } = require('@supabase/supabase-js');

// Initialize the Supabase client
const supabaseUrl = 'https://nekjeqxlwhfwyekeinnc.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Email and password for testing
const email = '<EMAIL>';
const password = 'your_password_here'; // Replace with your actual password

async function checkAuth() {
  try {
    console.log('Signing in...');
    
    // Sign in with email and password
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('Error signing in:', error);
      return;
    }
    
    console.log('Sign in successful!');
    console.log('User:', data.user);
    console.log('Session:', data.session);
    
    // Get the user's profile from the public users table
    console.log('\nFetching user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return;
    }
    
    console.log('User profile:', profile);
    
    // Get the user's branch
    if (profile.branch_id) {
      console.log('\nFetching user branch...');
      const { data: branch, error: branchError } = await supabase
        .from('branches')
        .select('*')
        .eq('branch_id', profile.branch_id)
        .single();
      
      if (branchError) {
        console.error('Error fetching branch:', branchError);
        return;
      }
      
      console.log('Branch:', branch);
    } else {
      console.log('User is not assigned to any branch');
    }
    
    // Sign out
    console.log('\nSigning out...');
    const { error: signOutError } = await supabase.auth.signOut();
    
    if (signOutError) {
      console.error('Error signing out:', signOutError);
      return;
    }
    
    console.log('Sign out successful!');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the check
checkAuth();
