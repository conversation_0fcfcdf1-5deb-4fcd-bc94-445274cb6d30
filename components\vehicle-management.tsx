"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { VehicleList } from "@/components/vehicle-list"

export function VehicleManagement() {
  return (
    <div className="space-y-4">
      <Tabs defaultValue="receive" className="space-y-4">
        <TabsList>
          <TabsTrigger value="receive">Receive</TabsTrigger>
          <TabsTrigger value="load">Load</TabsTrigger>
        </TabsList>
        <TabsContent value="receive" className="space-y-4">
          <VehicleList mode="receive" />
        </TabsContent>
        <TabsContent value="load" className="space-y-4">
          <VehicleList mode="load" />
        </TabsContent>
      </Tabs>
    </div>
  )
}
