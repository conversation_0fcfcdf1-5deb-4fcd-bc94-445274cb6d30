import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/drivers/validation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.driver_id && !body.driver_number) {
      return NextResponse.json(
        { error: "Missing required field: driver_id or driver_number" },
        { status: 400 },
      );
    }

    // Query to get driver details
    let query = supabase.from("drivers").select("*");

    if (body.driver_id) {
      query = query.eq("driver_id", body.driver_id);
    } else if (body.driver_number) {
      query = query.eq("contact_number", body.driver_number);
    }

    const { data: driver, error } = await query.single();

    if (error || !driver) {
      return NextResponse.json({ error: "Driver not found" }, { status: 404 });
    }

    // Check if driver is active
    if (driver.status !== "Active") {
      return NextResponse.json({
        valid: false,
        driver_id: driver.driver_id,
        validation: {
          status_valid: false,
          dl_valid: false,
          eligibility_valid: false,
        },
        message: `Driver is not active. Current status: ${driver.status}`,
      });
    }

    // Check if driver is already assigned to an active memo
    const { data: activeMemos, error: memosError } = await supabase
      .from("memos")
      .select("memo_id, memo_number, status")
      .eq("status", "Created")
      .contains("driver_ids", [driver.driver_id]);

    if (memosError) {
      console.error("Error checking active memos:", memosError);
      return NextResponse.json(
        { error: "Failed to check driver availability" },
        { status: 500 },
      );
    }

    const isAssignedToActiveMemo = activeMemos && activeMemos.length > 0;

    // Check DL validity if license_expiry_date is available
    const currentDate = new Date();
    const dlValid = driver.license_expiry_date
      ? new Date(driver.license_expiry_date) > currentDate
      : true; // If no expiry date is stored, assume valid

    // Check if driver's license type matches the vehicle type (if vehicle_id is provided)
    let vehicleTypeValid = true;
    let vehicleType = null;

    if (body.vehicle_id) {
      const { data: vehicle, error: vehicleError } = await supabase
        .from("vehicles")
        .select("vehicle_type")
        .eq("vehicle_id", body.vehicle_id)
        .single();

      if (!vehicleError && vehicle) {
        vehicleType = vehicle.vehicle_type;

        // Check if driver's license type is compatible with vehicle type
        // This is a simplified check - in a real system, you'd have more detailed compatibility rules
        if (driver.license_type && vehicleType) {
          // Example compatibility check:
          // Heavy license can drive any vehicle
          // Medium license can drive medium and light vehicles
          // Light license can only drive light vehicles
          if (driver.license_type === "Light" && vehicleType !== "Light") {
            vehicleTypeValid = false;
          } else if (
            driver.license_type === "Medium" && vehicleType === "Heavy"
          ) {
            vehicleTypeValid = false;
          }
        }
      }
    }

    // Eligibility is based on DL validity, vehicle compatibility, and not being in another active memo
    const eligibilityValid = dlValid && vehicleTypeValid &&
      !isAssignedToActiveMemo;

    const validation = {
      status_valid: true,
      dl_valid: dlValid,
      vehicle_type_valid: vehicleTypeValid,
      eligibility_valid: eligibilityValid,
    };

    const allValid = Object.values(validation).every((value) => value === true);

    // Prepare detailed message
    let message = "";
    if (allValid) {
      message = "Driver validation successful";
    } else {
      if (!dlValid) {
        message += "Driver's license has expired. ";
      }
      if (!vehicleTypeValid) {
        message +=
          `Driver's license type (${driver.license_type}) is not compatible with the vehicle type (${vehicleType}). `;
      }
      if (isAssignedToActiveMemo) {
        message += `Driver is already assigned to memo(s): ${
          activeMemos.map((m) => m.memo_number).join(", ")
        }. `;
      }
      if (!message) {
        message =
          "Driver validation failed. Please check the validation details.";
      }
    }

    return NextResponse.json({
      valid: allValid,
      driver_id: driver.driver_id,
      validation,
      driver: {
        name: driver.name,
        contact_number: driver.contact_number,
        license_type: driver.license_type,
        license_expiry_date: driver.license_expiry_date,
      },
      active_memos: isAssignedToActiveMemo ? activeMemos : [],
      message: message.trim(),
    });
  } catch (error: any) {
    console.error("Error in POST /api/drivers/validation:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
