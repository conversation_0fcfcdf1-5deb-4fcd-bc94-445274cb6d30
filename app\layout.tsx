import './globals.css'
import type { Metadata } from 'next'
import { Poppins } from 'next/font/google'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { OfflineIndicator } from '@/components/offline-indicator'
import { CommandPalette } from '@/components/command-palette'
import { TooltipProvider } from '@/components/ui/tooltip'
import { SupabaseProvider } from '@/contexts/supabase-provider'

const poppins = Poppins({
  weight: ['300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'KPN Branch Management',
  description: 'Modern branch management solution for KPN',
  icons: {
    icon: '/favicon.ico',
    apple: '/favicon.ico',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>

      <body className={poppins.className}>
        <SupabaseProvider>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
            <TooltipProvider>
              {children}
              <Toaster />
              <OfflineIndicator />
              <CommandPalette />
            </TooltipProvider>
          </ThemeProvider>
        </SupabaseProvider>
      </body>
    </html>
  )
}