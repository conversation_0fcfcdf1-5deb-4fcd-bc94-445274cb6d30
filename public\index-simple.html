<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redirecting...</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      background-color: #f9f9f9;
    }
    .container {
      text-align: center;
      padding: 2rem;
      border-radius: 8px;
      background-color: white;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      color: #003A8C;
      margin-bottom: 1rem;
    }
    p {
      margin-bottom: 2rem;
      color: #666;
    }
    .loader {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #003A8C;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }
    button {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    .primary {
      background-color: #003A8C;
      color: white;
    }
    .secondary {
      background-color: #f3f3f3;
      color: #333;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="loader"></div>
    <h1>KPN Branch Management</h1>
    <p>Redirecting to the simple dashboard...</p>
    <div class="buttons">
      <button class="primary" onclick="window.location.href='/simple-dashboard'">Go to Dashboard</button>
      <button class="secondary" onclick="window.location.href='/simple-login'">Go to Login</button>
    </div>
  </div>
  
  <script>
    // Redirect to the simple dashboard after a short delay
    setTimeout(() => {
      window.location.href = '/simple-dashboard';
    }, 2000);
  </script>
</body>
</html>
