# Parcel Management System Changes

## Database Schema Changes

### SQL Script to Add New Fields

```sql
-- Add collector_details and recipient_id_details columns to parcels table
ALTER TABLE parcels 
ADD COLUMN collector_details JSONB DEFAULT NULL,
ADD COLUMN recipient_id_details JSONB DEFAULT NULL;

-- Comment on columns
COMMENT ON COLUMN parcels.collector_details IS 'JSON object containing details of the person who collected the parcel (name, phone, ID proof type, ID number)';
COMMENT ON COLUMN parcels.recipient_id_details IS 'JSON object containing ID proof details of the recipient (ID type, ID number)';
```

### TypeScript Type Definitions Update

Update the `database.types.ts` file to include the new fields in all three type definitions:

```typescript
// Add to the Row type
parcels: {
  Row: {
    // existing fields...
    collector_details: J<PERSON> | null;
    recipient_id_details: J<PERSON> | null;
  };
  
  // Add to the Insert type
  Insert: {
    // existing fields...
    collector_details?: <PERSON><PERSON> | null;
    recipient_id_details?: <PERSON><PERSON> | null;
  };
  
  // Add to the Update type
  Update: {
    // existing fields...
    collector_details?: <PERSON><PERSON> | null;
    recipient_id_details?: J<PERSON> | null;
  };
}
```

## Component Changes

### 1. ParcelStatusDialog Schema Update

```typescript
const statusUpdateSchema = z.object({
  newStatus: z.string().min(1, "Please select a new status"),
  notes: z.string().optional(),
  // Original recipient details (non-editable, for reference)
  originalRecipientDetails: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  // Collector details (person actually receiving the parcel)
  collectorDetails: z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
    idProof: z.string().optional(),
    idNumber: z.string().optional(),
    signature: z.string().optional(),
  }).optional(),
  paymentDetails: z.object({
    amount: z.string().min(1, "Amount is required"),
    method: z.string().min(1, "Payment method is required"),
    transactionId: z.string().optional(), // For online payments
  }).optional().refine(data => {
    // If payment details are provided, both amount and method must be present
    if (data) {
      return data.amount && data.method
    }
    return true
  }, {
    message: "Both amount and payment method are required"
  })
})
```

### 2. Form Initialization Update

```typescript
const form = useForm<z.infer<typeof statusUpdateSchema>>({
  resolver: zodResolver(statusUpdateSchema),
  defaultValues: {
    newStatus: "",
    notes: "",
    originalRecipientDetails: {
      name: parcel.recipientName || "",
      phone: parcel.recipientPhone || "",
    },
    collectorDetails: {
      name: "",
      phone: "",
      idProof: "",
      idNumber: "",
      signature: "",
    },
    paymentDetails: undefined
  }
})
```

### 3. Step-by-Step Approach Implementation

```typescript
// Step-by-step approach
const [step, setStep] = useState(1)
// Add a confirmation step at the end
const totalSteps = showPayment ? 4 : showRecipientDetails ? 3 : 2

// Reset step when dialog opens
useEffect(() => {
  if (open) {
    setStep(1)
  }
}, [open])
```

### 4. API Update Logic for Collector Details

```typescript
// Add collector details if provided
if (statusData.collectorDetails && statusData.newStatus === "Delivered") {
  // Store collector details in a JSON field
  updateData.collector_details = JSON.stringify({
    name: statusData.collectorDetails.name,
    phone: statusData.collectorDetails.phone,
    id_type: statusData.collectorDetails.idProof || null,
    id_number: statusData.collectorDetails.idNumber || null
  })

  // Set actual delivery date
  updateData.actual_delivery_date = new Date().toISOString()
}
```

### 5. UI Changes for Collector Details

The UI now shows:
1. Original recipient details (non-editable)
2. Collector details form (editable)
3. Payment details (for "to pay" parcels)
4. Confirmation step before finalizing

## Key UI Features

1. **Wider Dialog**: Increased from 425px to 600px
2. **Step-by-Step Workflow**: 
   - Step 1: Status selection
   - Step 2: Collector details (when marking as Delivered)
   - Step 3: Payment collection (for "To Pay" parcels)
   - Step 4: Confirmation

3. **Original Recipient Details Display**:
```jsx
<div className="bg-gray-50 p-4 rounded-lg">
  <h3 className="font-medium">Original Recipient Details</h3>
  <p className="text-sm text-muted-foreground">These are the details provided during booking</p>
  
  <div className="grid grid-cols-2 gap-4 mt-4">
    <div>
      <p className="text-sm text-gray-500">Recipient Name</p>
      <p className="font-medium">{parcel.recipientName || "Not specified"}</p>
    </div>
    <div>
      <p className="text-sm text-gray-500">Recipient Phone</p>
      <p className="font-medium">{parcel.recipientPhone || "Not specified"}</p>
    </div>
  </div>
</div>
```

4. **Collector Details Form**:
```jsx
<div className="bg-blue-50 p-4 rounded-lg">
  <h3 className="font-medium">Collector Details</h3>
  <p className="text-sm text-muted-foreground">Enter details of the person collecting the parcel</p>

  <div className="grid grid-cols-2 gap-4 mt-4">
    <FormField
      control={form.control}
      name="collectorDetails.name"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Collector Name</FormLabel>
          <FormControl>
            <Input
              type="text"
              placeholder="Enter collector name"
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />

    <FormField
      control={form.control}
      name="collectorDetails.phone"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Collector Phone</FormLabel>
          <FormControl>
            <Input
              type="tel"
              placeholder="Enter collector phone"
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  </div>
</div>
```

5. **Confirmation Step**:
```jsx
<div className="bg-green-50 p-4 rounded-lg">
  <h3 className="font-medium text-green-800">Confirmation</h3>
  <p className="text-sm text-green-700 mb-4">Please review the information before finalizing</p>
  
  <div className="space-y-4">
    <div className="bg-white rounded p-3 border">
      <h4 className="font-medium text-sm">Status Update</h4>
      <p className="text-sm">New Status: <span className="font-medium">{selectedStatus}</span></p>
      {form.getValues("notes") && (
        <p className="text-sm mt-1">Notes: {form.getValues("notes")}</p>
      )}
    </div>
    
    {/* Collector Details Summary */}
    {/* Payment Details Summary */}
  </div>
</div>
```

## Implementation Notes

1. The collector details are stored separately from the recipient details
2. The UI shows both the original recipient details and the collector details
3. The confirmation step provides a clear overview before finalizing
4. For online payments, a transaction ID field is now required
5. The dialog is wider and scrollable to accommodate all content
