-- Cleanup Migration for Old Tracking Tables
-- This migration removes duplicate tracking tables after data migration to parcel_actions

-- Step 1: Verify data migration completed successfully
DO $$
DECLARE
  v_history_count INT;
  v_actions_count INT;
BEGIN
  -- Count records in old table
  SELECT COUNT(*) INTO v_history_count FROM parcel_status_history;
  
  -- Count migrated records in new table
  SELECT COUNT(*) INTO v_actions_count FROM parcel_actions;
  
  -- Log the counts for verification
  RAISE NOTICE 'parcel_status_history records: %, parcel_actions records: %', v_history_count, v_actions_count;
  
  -- Only proceed if we have data in the new table
  IF v_actions_count = 0 AND v_history_count > 0 THEN
    RAISE EXCEPTION 'Data migration appears incomplete. parcel_actions table is empty but parcel_status_history has % records', v_history_count;
  END IF;
END$$;

-- Step 2: Create backup views before dropping tables (for safety)
CREATE OR REPLACE VIEW parcel_status_history_backup AS
SELECT * FROM parcel_status_history;

-- Step 3: Remove old triggers that are no longer needed
DROP TRIGGER IF EXISTS parcel_status_change_trigger ON parcels;
DROP FUNCTION IF EXISTS record_parcel_status_change();

-- Step 4: Remove old operations trigger (will be replaced by new parcel actions system)
DROP TRIGGER IF EXISTS update_parcel_status_on_loading_trigger ON loading_chart_items;
DROP FUNCTION IF EXISTS update_parcel_status_on_loading();

-- Step 5: Update old enum values to new ones in existing parcels
-- Map old statuses to new statuses
UPDATE parcels 
SET current_status = CASE 
  WHEN current_status = 'To Be Received' THEN 'Loaded'
  WHEN current_status = 'To Be Delivered' THEN 'Received'
  ELSE current_status
END
WHERE current_status IN ('To Be Received', 'To Be Delivered');

-- Step 6: Remove old enum values (this requires careful handling)
-- Note: We cannot directly remove enum values in PostgreSQL, so we'll create a new enum and migrate

-- Create new enum with only the values we want
CREATE TYPE parcel_status_new AS ENUM ('Booked', 'Loaded', 'Received', 'Delivered');

-- Update parcels table to use new enum
ALTER TABLE parcels 
ALTER COLUMN current_status TYPE parcel_status_new 
USING current_status::text::parcel_status_new;

-- Update parcel_actions table to use new enum for action_type where applicable
-- (action_type is VARCHAR, so no direct change needed, but we should validate values)

-- Drop old enum and rename new one
DROP TYPE parcel_status;
ALTER TYPE parcel_status_new RENAME TO parcel_status;

-- Step 7: Create a function to validate parcel action types
CREATE OR REPLACE FUNCTION validate_parcel_action_type()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.action_type NOT IN ('Booked', 'Loaded', 'Received', 'Delivered') THEN
    RAISE EXCEPTION 'Invalid action_type: %. Must be one of: Booked, Loaded, Received, Delivered', NEW.action_type;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate action types
CREATE TRIGGER validate_parcel_action_type_trigger
  BEFORE INSERT OR UPDATE ON parcel_actions
  FOR EACH ROW
  EXECUTE FUNCTION validate_parcel_action_type();

-- Step 8: After confirming everything works, drop the old table
-- IMPORTANT: Only run this after thorough testing
-- DROP TABLE IF EXISTS parcel_status_history CASCADE;

-- Step 9: Update operations table to work with new system
-- The operations table can remain but should be updated to reference parcel_actions
ALTER TABLE operations 
ADD COLUMN IF NOT EXISTS parcel_action_id INT REFERENCES parcel_actions(action_id) ON DELETE SET NULL;

-- Step 10: Create indexes for performance
CREATE INDEX IF NOT EXISTS parcel_actions_parcel_action_type_idx ON parcel_actions(parcel_id, action_type);
CREATE INDEX IF NOT EXISTS parcel_actions_branch_timestamp_idx ON parcel_actions(branch_id, action_timestamp);

-- Step 11: Create view for backward compatibility with old status history queries
CREATE OR REPLACE VIEW parcel_status_timeline AS
SELECT 
  pa.action_id as id,
  pa.parcel_id,
  pa.action_type::parcel_status as status,
  pa.action_timestamp as timestamp,
  pa.location_name as location,
  pa.remarks,
  pa.created_by as updated_by,
  pa.branch_id,
  pa.vehicle_id
FROM parcel_actions pa
WHERE pa.action_type IN ('Booked', 'Loaded', 'Received', 'Delivered')
ORDER BY pa.parcel_id, pa.action_timestamp;

-- Step 12: Add helpful comments
COMMENT ON VIEW parcel_status_timeline IS 'Backward compatibility view that mimics the old parcel_status_history table structure';
COMMENT ON FUNCTION validate_parcel_action_type() IS 'Ensures only valid parcel status values are used in action_type';

-- Step 13: Create summary view for parcel tracking
CREATE OR REPLACE VIEW parcel_tracking_summary AS
SELECT 
  p.parcel_id,
  p.lr_number,
  p.current_status,
  p.sender_branch_id,
  p.delivery_branch_id,
  sb.name as sender_branch_name,
  db.name as delivery_branch_name,
  
  -- Latest action details
  latest.action_timestamp as last_action_time,
  latest.location_name as current_location,
  latest.branch_id as current_branch_id,
  cb.name as current_branch_name,
  
  -- Booking details
  booking.action_timestamp as booking_time,
  booking.branch_id as booking_branch_id,
  
  -- Loading details (latest loading action)
  loading.action_timestamp as last_loaded_time,
  loading.destination_branch_id as loaded_to_branch_id,
  loading.loading_type,
  ldb.name as loaded_to_branch_name,
  
  -- Delivery details
  delivery.action_timestamp as delivery_time,
  delivery.branch_id as delivery_branch_id
  
FROM parcels p
LEFT JOIN branches sb ON p.sender_branch_id = sb.branch_id
LEFT JOIN branches db ON p.delivery_branch_id = db.branch_id

-- Latest action
LEFT JOIN LATERAL (
  SELECT * FROM parcel_actions pa 
  WHERE pa.parcel_id = p.parcel_id 
  ORDER BY pa.action_timestamp DESC 
  LIMIT 1
) latest ON true
LEFT JOIN branches cb ON latest.branch_id = cb.branch_id

-- Booking action
LEFT JOIN LATERAL (
  SELECT * FROM parcel_actions pa 
  WHERE pa.parcel_id = p.parcel_id AND pa.action_type = 'Booked'
  ORDER BY pa.action_timestamp DESC 
  LIMIT 1
) booking ON true

-- Latest loading action
LEFT JOIN LATERAL (
  SELECT * FROM parcel_actions pa 
  WHERE pa.parcel_id = p.parcel_id AND pa.action_type = 'Loaded'
  ORDER BY pa.action_timestamp DESC 
  LIMIT 1
) loading ON true
LEFT JOIN branches ldb ON loading.destination_branch_id = ldb.branch_id

-- Delivery action
LEFT JOIN LATERAL (
  SELECT * FROM parcel_actions pa 
  WHERE pa.parcel_id = p.parcel_id AND pa.action_type = 'Delivered'
  ORDER BY pa.action_timestamp DESC 
  LIMIT 1
) delivery ON true;

COMMENT ON VIEW parcel_tracking_summary IS 'Comprehensive view showing current parcel status and key action details for tracking purposes';
