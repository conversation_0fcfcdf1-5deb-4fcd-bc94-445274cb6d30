"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Download, Printer, Share2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface ReceiptPreviewProps {
  bookingDetails: {
    id: string
    date: string
    amount: string
    customerName: string
    customerPhone: string
    recipientName: string
    recipientPhone: string
    items: Array<{
      name: string
      quantity: number
      weight?: string
    }>
    totalWeight?: string
    paymentMode: string
    deliveryType: string
  }
}

export function ReceiptPreview({ bookingDetails }: ReceiptPreviewProps) {
  const { toast } = useToast()

  const handleDownload = () => {
    // In a real app, this would generate and download a PDF
    toast({
      title: "Receipt Downloaded",
      description: "Your receipt has been downloaded successfully.",
    })
  }

  const handlePrint = () => {
    // In a real app, this would open the print dialog
    window.print()
    toast({
      title: "Print Initiated",
      description: "Sending receipt to printer.",
    })
  }

  const handleShare = () => {
    // In a real app, this would open sharing options
    toast({
      title: "Share Options",
      description: "Sharing options would appear here.",
    })
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 border-dashed print:border-none" id="receipt-content">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold">KPN Logistics</h2>
          <p className="text-sm text-muted-foreground">Receipt</p>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between">
            <div>
              <h3 className="font-semibold">LR Number</h3>
              <p>{bookingDetails.id}</p>
            </div>
            <div className="text-right">
              <h3 className="font-semibold">Date</h3>
              <p>{bookingDetails.date}</p>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold">Sender</h3>
              <p>{bookingDetails.customerName}</p>
              <p>{bookingDetails.customerPhone}</p>
            </div>
            <div>
              <h3 className="font-semibold">Recipient</h3>
              <p>{bookingDetails.recipientName}</p>
              <p>{bookingDetails.recipientPhone}</p>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="font-semibold mb-2">Items</h3>
            <table className="w-full text-sm">
              <thead className="text-left">
                <tr className="border-b">
                  <th className="pb-2">Item</th>
                  <th className="pb-2">Quantity</th>
                  <th className="pb-2">Weight</th>
                </tr>
              </thead>
              <tbody>
                {bookingDetails.items.map((item, index) => (
                  <tr key={index} className="border-b border-dashed">
                    <td className="py-2">{item.name}</td>
                    <td className="py-2">{item.quantity}</td>
                    <td className="py-2">{item.weight || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {bookingDetails.totalWeight && (
              <p className="text-sm mt-2">
                <span className="font-medium">Total Weight:</span> {bookingDetails.totalWeight}
              </p>
            )}
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p><span className="font-medium">Payment Mode:</span> {bookingDetails.paymentMode}</p>
              <p><span className="font-medium">Delivery Type:</span> {bookingDetails.deliveryType}</p>
            </div>
            <div className="text-right">
              <p className="font-medium">Total Amount</p>
              <p className="text-xl font-bold">₹{bookingDetails.amount}</p>
            </div>
          </div>

          <div className="text-center text-xs text-muted-foreground mt-6">
            <p>Thank you for choosing KPN Logistics!</p>
            <p>For any queries, please contact our customer support at 1800-123-4567</p>
          </div>
        </div>
      </Card>

      <div className="flex justify-center gap-4 print:hidden">
        <Button variant="outline" onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" />
          Download
        </Button>
        <Button variant="outline" onClick={handlePrint}>
          <Printer className="mr-2 h-4 w-4" />
          Print
        </Button>
        <Button variant="outline" onClick={handleShare}>
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>
  )
}
