import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/driver-validation
export async function POST(request: NextRequest) {
  console.log("=== DRIVER VALIDATION START ===");
  try {
    console.log("Step 1: Parsing request body");
    const body = await request.json();
    console.log("Request body:", body);

    // Validate required fields
    if (!body.driver_id && !body.driver_number) {
      console.log("Error: Missing required fields");
      return NextResponse.json(
        { error: "Missing required field: driver_id or driver_number" },
        { status: 400 },
      );
    }
    console.log("Step 2: Required fields validated");

    // Log the search parameters
    console.log("Driver validation search parameters:", {
      driver_id: body.driver_id,
      driver_number: body.driver_number,
    });

    // Query to get driver details
    let query = supabase.from("drivers").select("*");
    let driver: any = null;
    let searchError: any = null;

    if (body.driver_id) {
      query = query.eq("driver_id", body.driver_id);
      const result = await query.single();
      driver = result.data;
      searchError = result.error;
    } else if (body.driver_number) {
      // Try to find by driver_number (ID) or phone_number (phone)
      console.log(
        `Searching for driver with driver_number or phone_number: ${body.driver_number}`,
      );

      // First, let's try to get all drivers to see what's available
      const { data: allDrivers } = await supabase
        .from("drivers")
        .select("driver_id, name, driver_number, phone_number, contact_number")
        .limit(10);

      console.log("Available drivers:", allDrivers);

      // Try direct searches first - check both phone_number and contact_number
      const { data: phoneSearch } = await supabase
        .from("drivers")
        .select("*")
        .eq("phone_number", body.driver_number);

      console.log("Direct phone_number search:", {
        found: phoneSearch && phoneSearch.length > 0,
        count: phoneSearch?.length || 0,
      });

      const { data: contactSearch } = await supabase
        .from("drivers")
        .select("*")
        .eq("contact_number", body.driver_number);

      console.log("Direct contact_number search:", {
        found: contactSearch && contactSearch.length > 0,
        count: contactSearch?.length || 0,
      });

      const { data: driverNumSearch } = await supabase
        .from("drivers")
        .select("*")
        .eq("driver_number", body.driver_number);

      console.log("Direct driver_number search:", {
        found: driverNumSearch && driverNumSearch.length > 0,
        count: driverNumSearch?.length || 0,
      });

      // If we found a driver with direct search, use that
      if (phoneSearch && phoneSearch.length > 0) {
        driver = phoneSearch[0];
        console.log("Found driver via phone_number search:", driver.name);
      } else if (contactSearch && contactSearch.length > 0) {
        driver = contactSearch[0];
        console.log("Found driver via contact_number search:", driver.name);
      } else if (driverNumSearch && driverNumSearch.length > 0) {
        driver = driverNumSearch[0];
        console.log("Found driver via driver_number search:", driver.name);
      } else {
        // Otherwise try the OR query
        try {
          const { data, error } = await supabase
            .from("drivers")
            .select("*")
            .or(
              `driver_number.eq.${body.driver_number},phone_number.eq.${body.driver_number},contact_number.eq.${body.driver_number}`,
            )
            .limit(1);

          if (data && data.length > 0) {
            driver = data[0];
            console.log("Found driver via OR query:", driver.name);
          } else {
            searchError = error ||
              { message: "No driver found with the provided number" };
          }
        } catch (err: any) {
          console.error("Error in OR query:", err);
          searchError = { message: "Error executing OR query" };
        }
      }
    }

    // Log the result
    console.log("Driver search result:", {
      found: !!driver,
      error: searchError?.message,
      driverInfo: driver
        ? {
          id: driver.driver_id,
          name: driver.name,
          phone: driver.phone_number || driver.contact_number,
          driverNumber: driver.driver_number,
        }
        : null,
    });

    if (searchError || !driver) {
      // Provide more specific error information
      const searchValue = body.driver_id || body.driver_number;

      console.log(
        `Driver search failed: value = ${searchValue}`,
        searchError,
      );

      return NextResponse.json({
        error: `Driver not found with ID or phone number: ${searchValue}`,
        searchDetails: {
          field: "driver_number, phone_number, or contact_number",
          value: searchValue,
        },
      }, { status: 404 });
    }

    // Log driver details
    console.log("Step 3: Driver found:", {
      driver_id: driver.driver_id,
      name: driver.name,
      status: driver.status,
      dl_category: driver.dl_category,
      license_type: driver.license_type,
      dl_expiry_date: driver.dl_expiry_date,
      license_expiry_date: driver.license_expiry_date,
    });

    // Check if driver is active
    console.log("Step 4: Checking if driver is active");
    if (driver.status !== "Active") {
      console.log("Driver is not active, status:", driver.status);
      return NextResponse.json({
        valid: false,
        driver_id: driver.driver_id,
        validation: {
          status_valid: false,
          dl_valid: false,
          eligibility_valid: false,
        },
        message: `Driver is not active. Current status: ${driver.status}`,
      });
    }
    console.log("Driver is active");

    // Check if driver is already assigned to an active memo
    console.log("Step 5: Checking if driver is assigned to active memos");
    let activeMemos: Array<
      { memo_id: number; memo_number: string; status: string }
    > = [];
    let isAssignedToActiveMemo = false;

    try {
      const { data: memosData, error: memosError } = await supabase
        .from("memos")
        .select("memo_id, memo_number, status")
        .eq("status", "Created")
        .contains("driver_ids", [driver.driver_id]);

      console.log("Active memos query result:", {
        success: !memosError,
        error: memosError?.message,
        count: memosData?.length || 0,
      });

      if (memosError) {
        console.error("Error checking active memos:", memosError);
        console.log("Continuing validation despite memos error");
      } else {
        activeMemos = memosData || [];
        isAssignedToActiveMemo = activeMemos.length > 0;
        console.log(
          "Is driver assigned to active memo:",
          isAssignedToActiveMemo,
        );
      }
    } catch (memosCheckError: any) {
      console.error("Exception in active memos check:", memosCheckError);
      // Continue with validation despite the error
      console.log("Continuing validation despite memos check error");
    }

    // Check DL validity if expiry date is available (check both possible field names)
    console.log("Step 6: Checking driver's license validity");
    let dlValid = true;

    try {
      const currentDate = new Date();
      console.log("Current date:", currentDate.toISOString());

      // Check both possible field names for license expiry date
      const expiryDateStr = driver.dl_expiry_date || driver.license_expiry_date;
      console.log("Driver's license expiry date:", expiryDateStr);

      if (expiryDateStr) {
        const expiryDate = new Date(expiryDateStr);
        console.log("Parsed expiry date:", expiryDate.toISOString());
        dlValid = expiryDate > currentDate;
      } else {
        console.log("No expiry date found, assuming valid");
      }

      console.log("Driver's license valid:", dlValid);
    } catch (dlCheckError: any) {
      console.error("Exception in DL validity check:", dlCheckError);
      // Continue with validation despite the error
      console.log("Continuing validation despite DL check error");
      // Default to true if there was an error
      dlValid = true;
    }

    // Check if driver's license type matches the vehicle type (if vehicle_id is provided)
    let vehicleTypeValid = true;
    let vehicleType = null;

    try {
      // Get the driver's license type - check multiple possible field names
      const driverLicenseType = driver.dl_category || driver.license_type ||
        null;
      console.log("Driver license type:", driverLicenseType);

      if (body.vehicle_id) {
        console.log(
          "Checking vehicle compatibility with vehicle_id:",
          body.vehicle_id,
        );

        const { data: vehicle, error: vehicleError } = await supabase
          .from("vehicles")
          .select("vehicle_type, vehicle_id, license_category")
          .eq("vehicle_id", body.vehicle_id)
          .single();

        if (vehicleError) {
          console.error("Error fetching vehicle:", vehicleError);
          // Don't fail the validation, just log the error and continue
          console.log("Continuing with validation despite vehicle error");
        } else if (vehicle) {
          vehicleType = vehicle.vehicle_type;
          const vehicleLicenseCategory = vehicle.license_category;

          console.log("Vehicle found:", {
            vehicle_id: vehicle.vehicle_id,
            vehicle_type: vehicleType,
            license_category: vehicleLicenseCategory,
          });

          // Check if driver's license type is compatible with vehicle license category
          // Prioritize dl_category as specified
          const driverDlCategory = driver.dl_category;

          if (driverDlCategory && vehicleLicenseCategory) {
            console.log("Checking compatibility between:", {
              driver_dl_category: driverDlCategory,
              vehicle_license_category: vehicleLicenseCategory,
            });

            // Normalize license categories to uppercase for comparison
            const driverLicenseCategoryUpper = driverDlCategory.toUpperCase();
            const vehicleLicenseCategoryUpper = vehicleLicenseCategory
              .toUpperCase();

            // Compatibility check based on license categories:
            // If vehicle requires HEAVY license, driver must have HEAVY license
            // If vehicle requires MEDIUM license, driver must have MEDIUM or HEAVY license
            // If vehicle requires LIGHT license, any license type is valid

            let isCompatible = true;

            if (
              vehicleLicenseCategoryUpper === "HEAVY" &&
              driverLicenseCategoryUpper !== "HEAVY"
            ) {
              isCompatible = false;
              console.log("Incompatible: Heavy vehicle requires Heavy license");
            } else if (
              vehicleLicenseCategoryUpper === "MEDIUM" &&
              !(driverLicenseCategoryUpper === "MEDIUM" ||
                driverLicenseCategoryUpper === "HEAVY")
            ) {
              isCompatible = false;
              console.log(
                "Incompatible: Medium vehicle requires Medium or Heavy license",
              );
            }

            vehicleTypeValid = isCompatible;

            if (isCompatible) {
              console.log("Compatible license categories");
            }
          } else {
            console.log(
              "Missing license category information for compatibility check:",
              {
                driver_dl_category: driverDlCategory,
                vehicle_license_category: vehicleLicenseCategory,
              },
            );
            // If we don't have license category information, assume it's valid
            // to prevent blocking the workflow
            vehicleTypeValid = true;
            console.log(
              "Assuming compatibility due to missing license category information",
            );
          }
        } else {
          console.log("No vehicle found with ID:", body.vehicle_id);
        }
      } else {
        console.log("No vehicle_id provided, skipping compatibility check");
      }
    } catch (compatibilityError: any) {
      console.error(
        "Error in vehicle compatibility check:",
        compatibilityError,
      );
      // Don't fail the validation, just log the error and continue
      console.log("Continuing with validation despite compatibility error");
    }

    // Eligibility is based on DL validity, vehicle compatibility, and not being in another active memo
    console.log("Step 8: Calculating overall eligibility");
    try {
      const eligibilityValid = dlValid && vehicleTypeValid &&
        !isAssignedToActiveMemo;
      console.log("Eligibility factors:", {
        dlValid,
        vehicleTypeValid,
        isAssignedToActiveMemo,
        eligibilityValid,
      });

      const validation = {
        status_valid: true,
        dl_valid: dlValid,
        vehicle_type_valid: vehicleTypeValid,
        eligibility_valid: eligibilityValid,
      };

      const allValid = Object.values(validation).every((value) =>
        value === true
      );
      console.log("All validation checks passed:", allValid);
    } catch (validationError: any) {
      console.error("Exception in eligibility calculation:", validationError);
      // Continue with validation despite the error
      console.log(
        "Continuing validation despite eligibility calculation error",
      );
    }

    // Default values if there was an error
    const eligibilityValid = dlValid && vehicleTypeValid &&
      !isAssignedToActiveMemo;
    const validation = {
      status_valid: true,
      dl_valid: dlValid,
      vehicle_type_valid: vehicleTypeValid,
      eligibility_valid: eligibilityValid,
    };
    const allValid = Object.values(validation).every((value) => value === true);

    // Prepare detailed message
    let message = "";
    if (allValid) {
      message = "Driver validation successful";
    } else {
      if (!dlValid) {
        message += "Driver's license has expired. ";
      }
      if (!vehicleTypeValid) {
        const driverCategory = driver.dl_category || "Unknown";
        const vehicleCategory = vehicleType || "Unknown";
        message +=
          `Driver's license category (${driverCategory}) is not compatible with the vehicle license category (${vehicleCategory}). `;
      }
      if (isAssignedToActiveMemo) {
        try {
          message += `Driver is already assigned to memo(s): ${
            activeMemos.map((m) => m.memo_number || "Unknown").join(", ")
          }. `;
        } catch (error: any) {
          console.error("Error formatting active memos:", error);
          message += "Driver is already assigned to other active memos. ";
        }
      }
      if (!message) {
        message =
          "Driver validation failed. Please check the validation details.";
      }
    }

    console.log("Step 9: Preparing response");
    try {
      // Prepare the response object
      const responseObj = {
        valid: allValid,
        driver_id: driver.driver_id,
        validation,
        driver: {
          name: driver.name, // Using name field instead of driver_name
          contact_number: driver.contact_number || driver.phone_number, // Check both possible field names
          driver_number: driver.driver_number,
          dl_category: driver.dl_category, // Using dl_category as specified
          dl_expiry_date: driver.dl_expiry_date, // Using dl_expiry_date for consistency
        },
        active_memos: activeMemos, // Use the actual memos array
        message: message.trim(),
      };

      console.log("Response object prepared successfully");
      console.log("=== DRIVER VALIDATION COMPLETE ===");

      return NextResponse.json(responseObj);
    } catch (responseError: any) {
      console.error("Exception preparing response:", responseError);

      // Return a simplified response if there was an error
      console.log("Returning simplified response due to error");
      console.log("=== DRIVER VALIDATION COMPLETE WITH ERRORS ===");

      return NextResponse.json({
        valid: false,
        driver_id: driver.driver_id,
        message:
          "Driver validation completed with errors. Please check logs for details.",
      });
    }
  } catch (error: any) {
    console.error("=== DRIVER VALIDATION FAILED WITH ERROR ===");
    console.error("Error in POST /api/driver-validation:", error);

    // Try to get more details about the error
    let errorMessage = "Internal server error";
    let errorDetails = null;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack;
      console.error("Error stack:", error.stack);
    }

    // Try to identify where the error occurred
    try {
      const errorLocation = new Error().stack?.split("\n")[1] ||
        "Unknown location";
      console.error("Error location:", errorLocation);
    } catch (stackError: any) {
      console.error("Could not determine error location");
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      time: new Date().toISOString(),
    }, {
      status: 500,
    });
  }
}
