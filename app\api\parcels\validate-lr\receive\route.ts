import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/validate-lr/receive
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number");
    const memo_id = url.searchParams.get("memo_id");
    const user_branch_id = url.searchParams.get("user_branch_id");

    if (!lr_number) {
      return NextResponse.json(
        { valid: false, message: "LR number is required" },
        { status: 400 },
      );
    }

    if (!memo_id) {
      return NextResponse.json(
        { valid: false, message: "Memo ID is required" },
        { status: 400 },
      );
    }

    if (!user_branch_id) {
      return NextResponse.json(
        { valid: false, message: "User branch ID is required" },
        { status: 400 },
      );
    }

    // Get the memo to verify destination branch
    const { data: memo, error: memoError } = await supabase
      .from("memos")
      .select(`
        *,
        to_branch_id
      `)
      .eq("memo_id", memo_id)
      .single();

    if (memoError || !memo) {
      console.error("Error finding memo:", memoError);
      return NextResponse.json(
        { valid: false, message: "Memo not found" },
        { status: 404 },
      );
    }

    // Verify that the user's branch is the destination branch
    if (memo.to_branch_id.toString() !== user_branch_id) {
      return NextResponse.json(
        { 
          valid: false, 
          message: "This memo's destination is not your branch" 
        },
        { status: 403 },
      );
    }

    // Get the loading charts for this memo
    const { data: loadingCharts, error: chartsError } = await supabase
      .from("loading_charts")
      .select(`
        chart_id,
        memo_id,
        destination_branch_id
      `)
      .eq("memo_id", memo_id)
      .eq("destination_branch_id", user_branch_id);

    if (chartsError || !loadingCharts || loadingCharts.length === 0) {
      return NextResponse.json(
        { 
          valid: false, 
          message: "No loading charts found for this memo at your branch" 
        },
        { status: 404 },
      );
    }

    // Get the chart IDs
    const chartIds = loadingCharts.map(chart => chart.chart_id);

    // Check if the LR is in any of the loading charts
    const { data: loadingItems, error: itemsError } = await supabase
      .from("loading_chart_items")
      .select(`
        *,
        chart:loading_charts(*)
      `)
      .in("chart_id", chartIds)
      .eq("lr_number", lr_number)
      .eq("status", "Pending");

    if (itemsError) {
      console.error("Error checking loading items:", itemsError);
      return NextResponse.json(
        { 
          valid: false, 
          message: "Failed to check loading items" 
        },
        { status: 500 },
      );
    }

    if (!loadingItems || loadingItems.length === 0) {
      return NextResponse.json(
        { 
          valid: false, 
          message: "LR number not found in any pending loading charts for this vehicle" 
        },
        { status: 404 },
      );
    }

    // Get the parcel details
    const { data: parcel, error: parcelError } = await supabase
      .from("parcels")
      .select(`
        *,
        sender_branch:branches!sender_branch_id(name, code),
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    if (parcelError || !parcel) {
      console.error("Error finding parcel:", parcelError);
      return NextResponse.json(
        { 
          valid: false, 
          message: "Parcel not found" 
        },
        { status: 404 },
      );
    }

    // Return the parcel with loading chart information
    const loadingItem = loadingItems[0];
    return NextResponse.json({
      valid: true,
      parcel: {
        ...parcel,
        chart_id: loadingItem.chart_id,
        item_id: loadingItem.item_id,
        loaded_quantity: loadingItem.quantity
      }
    });
  } catch (error: any) {
    console.error("Error in GET /api/parcels/validate-lr/receive:", error);
    return NextResponse.json(
      { valid: false, message: "Internal server error" },
      { status: 500 },
    );
  }
}
