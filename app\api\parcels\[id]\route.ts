import { NextRequest, NextResponse } from "next/server";
import { deleteParcel, getParcelById, updateParcel } from "@/lib/db-helpers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/parcels/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid parcel ID" }, { status: 400 });
    }

    const parcel = await getParcelById(id);

    if (!parcel) {
      return NextResponse.json({ error: "Parcel not found" }, { status: 404 });
    }

    // Get status history for the parcel
    const { data: statusHistory, error: historyError } = await supabase
      .from("parcel_status_history")
      .select("*")
      .eq("parcel_id", id)
      .order("timestamp", { ascending: true });

    if (historyError) {
      console.error("Error fetching status history:", historyError);
      // Return the parcel without history if there's an error
      return NextResponse.json(parcel);
    }

    console.log(`Status history for parcel ${id}:`, statusHistory); // Debug log

    // If no status history found, create a default "Booked" entry
    if (!statusHistory || statusHistory.length === 0) {
      console.log(
        `No status history found for parcel ${id}, creating default entry`,
      ); // Debug log

      // Create a default status history with at least the booking status
      const defaultHistory = [{
        status: parcel.current_status === "Booked" ? "Booked" : "Unknown",
        timestamp: parcel.booking_datetime,
        location: "Initial booking",
        remarks: "Parcel booked",
      }];

      // Return the parcel with default history
      return NextResponse.json({
        ...parcel,
        status_history: defaultHistory,
      });
    }

    // Return the parcel with its status history
    return NextResponse.json({
      ...parcel,
      status_history: statusHistory,
    });
  } catch (error: any) {
    console.error(`Error in GET /api/parcels/${params.id}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// PUT /api/parcels/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid parcel ID" }, { status: 400 });
    }

    const body = await request.json();

    // We'll let the database trigger handle status history updates
    // This avoids duplication since the trigger will automatically add entries when status changes
    // We only need to pass the status_history data if there are additional details to include

    // Check if there's a status history entry to add with custom details
    if (body.status_history && body.current_status) {
      try {
        // Get the current parcel to check if status has changed
        const { data: currentParcel } = await supabase
          .from("parcels")
          .select("current_status, parcel_id")
          .eq("parcel_id", id)
          .single();

        if (
          currentParcel &&
          currentParcel.current_status !== body.current_status &&
          (body.status_history.remarks || body.status_history.location ||
            body.status_history.branch_id || body.status_history.vehicle_id)
        ) {
          // Status has changed AND we have custom details to add
          // The trigger will handle the basic status change, but we'll add a custom entry with details
          const { error: historyError } = await supabase
            .from("parcel_status_history")
            .insert({
              parcel_id: id,
              status: body.current_status, // This is already a parcel_status enum value
              timestamp: new Date().toISOString(),
              location: body.status_history.location || null,
              remarks: body.status_history.remarks || null,
              updated_by: session.user.id,
              branch_id: body.status_history.branch_id || null,
              vehicle_id: body.status_history.vehicle_id || null,
            });

          if (historyError) {
            console.error(
              "Error adding status history with custom details:",
              historyError,
            );
            // Continue with the update even if history fails
          }
        }
      } catch (historyError) {
        console.error("Error processing status history:", historyError);
        // Continue with the update even if history processing fails
      }
    }

    // Remove status_history from body before updating parcel
    const { status_history, ...updateData } = body;

    // Update parcel
    const updatedParcel = await updateParcel(id, updateData);

    if (!updatedParcel) {
      return NextResponse.json({ error: "Failed to update parcel" }, {
        status: 500,
      });
    }

    // Get status history for the parcel
    const { data: statusHistory, error: historyError } = await supabase
      .from("parcel_status_history")
      .select("*")
      .eq("parcel_id", id)
      .order("timestamp", { ascending: true });

    if (historyError) {
      console.error("Error fetching status history:", historyError);
      // Return the parcel without history if there's an error
      return NextResponse.json(updatedParcel);
    }

    console.log(`Status history after update for parcel ${id}:`, statusHistory); // Debug log

    // If no status history found after update, create a default entry
    if (!statusHistory || statusHistory.length === 0) {
      console.log(
        `No status history found after update for parcel ${id}, creating default entry`,
      ); // Debug log

      // Create a default status history with the current status
      const defaultHistory = [{
        status: updatedParcel.current_status,
        timestamp: new Date().toISOString(),
        location: "System update",
        remarks: `Status updated to ${updatedParcel.current_status}`,
      }];

      // Return the parcel with default history
      return NextResponse.json({
        ...updatedParcel,
        status_history: defaultHistory,
      });
    }

    // Return the updated parcel with its status history
    return NextResponse.json({
      ...updatedParcel,
      status_history: statusHistory,
    });
  } catch (error: any) {
    console.error(`Error in PUT /api/parcels/${params.id}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// DELETE /api/parcels/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid parcel ID" }, { status: 400 });
    }

    // Delete parcel
    const success = await deleteParcel(id);

    if (!success) {
      return NextResponse.json({ error: "Failed to delete parcel" }, {
        status: 500,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error in DELETE /api/parcels/${params.id}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
