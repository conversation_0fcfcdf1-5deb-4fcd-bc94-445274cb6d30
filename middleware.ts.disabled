import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// List of paths that don't require authentication
const publicPaths = [
  "/login",
  "/forgot-password",
  "/reset-password",
  "/simple-login",
];

// List of paths to completely skip middleware processing
const skipPaths = ["/simple-login", "/simple-dashboard"];

export async function middleware(req: NextRequest) {
  // Skip middleware for static files, API routes, and specific paths
  const path = req.nextUrl.pathname;

  // Skip middleware for simple login/dashboard pages
  if (skipPaths.some((skipPath) => path.startsWith(skipPath))) {
    console.log(`Skipping middleware for path: ${path}`);
    return NextResponse.next();
  }

  // Skip middleware for static files and API routes
  if (
    path.includes("/_next") ||
    path.includes("/api") ||
    path.endsWith(".ico") ||
    path.endsWith(".svg") ||
    path.endsWith(".png") ||
    path.endsWith(".jpg") ||
    path.endsWith(".jpeg") ||
    path.endsWith(".gif") ||
    path.endsWith(".webp") ||
    path.endsWith(".js") ||
    path.endsWith(".css") ||
    path.includes("/test-login.html")
  ) {
    return NextResponse.next();
  }

  const res = NextResponse.next();

  // Create a Supabase client configured to use cookies
  const supabase = createMiddlewareClient({ req, res });

  try {
    // Refresh session if expired - required for Server Components
    const { data: { session } } = await supabase.auth.getSession();

    // Check if the path requires authentication
    const isPublicPath = publicPaths.some((publicPath) =>
      path.startsWith(publicPath)
    );

    console.log(
      `Middleware: Path=${path}, HasSession=${!!session}, IsPublicPath=${isPublicPath}`,
    );

    // If the path is not public and the user is not authenticated, redirect to login
    if (!isPublicPath && !session) {
      console.log("Redirecting to login: Not authenticated");
      return NextResponse.redirect(new URL("/login", req.url));
    }

    // If the user is authenticated and trying to access login, redirect to home
    if (path === "/login" && session) {
      console.log("Redirecting to home: Already authenticated");

      // Use a direct response with a refresh header for more reliable redirection
      const response = NextResponse.redirect(new URL("/", req.url));
      response.headers.set("Cache-Control", "no-store, must-revalidate");
      return response;
    }

    return res;
  } catch (error) {
    console.error("Error in middleware:", error);
    return res;
  }
}

// Specify which paths this middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
