"use client"

import { useState } from "react"
import { MobileNav } from "@/components/mobile-nav"
import { ProfileManagement } from "@/components/profile-management"
import { AppHeader } from "@/components/app-header"
import { PageLayout } from "@/components/page-layout"

export default function ProfilePage() {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)

  return (
    <PageLayout title="My Profile" subtitle="Manage your account settings and preferences" showActions={false}>
      <ProfileManagement />
    </PageLayout>
  )
}