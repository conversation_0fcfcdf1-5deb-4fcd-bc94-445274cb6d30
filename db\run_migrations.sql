-- Run all migrations in the correct order

-- 1. Add auth_id column to users table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'auth_id'
  ) THEN
    -- Add the auth_id column
    ALTER TABLE public.users ADD COLUMN auth_id UUID UNIQUE;
    
    -- Create an index for faster lookups
    CREATE INDEX idx_users_auth_id ON public.users(auth_id);
    
    RAISE NOTICE 'Added auth_id column to users table';
  ELSE
    RAISE NOTICE 'auth_id column already exists in users table';
  END IF;
END
$$;

-- 2. Create check_column_exists function
CREATE OR REPLACE FUNCTION check_column_exists(table_name text, column_name text)
RETURNS boolean
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public'
      AND table_name = check_column_exists.table_name
      AND column_name = check_column_exists.column_name
  );
END;
$$;

-- 3. Update existing users to set auth_id based on email
DO $$
DECLARE
  auth_user RECORD;
  public_user RECORD;
BEGIN
  -- Only proceed if the auth_id column exists
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'auth_id'
  ) THEN
    -- For each user in auth.users
    FOR auth_user IN 
      SELECT id, email FROM auth.users
    LOOP
      -- Find matching user in public.users by email
      SELECT user_id INTO public_user
      FROM public.users
      WHERE email = auth_user.email
        AND (auth_id IS NULL OR auth_id = auth_user.id);
      
      -- If found, update the auth_id
      IF FOUND THEN
        UPDATE public.users
        SET auth_id = auth_user.id
        WHERE user_id = public_user.user_id
          AND (auth_id IS NULL OR auth_id != auth_user.id);
        
        RAISE NOTICE 'Updated auth_id for user %: %', public_user.user_id, auth_user.id;
      END IF;
    END LOOP;
  END IF;
END
$$;
