import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/memos/vehicle/receive
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const registration_number = url.searchParams.get("registration_number");
    const user_branch_id = url.searchParams.get("user_branch_id");

    if (!registration_number) {
      return NextResponse.json(
        { error: "Vehicle registration number is required" },
        { status: 400 },
      );
    }

    if (!user_branch_id) {
      return NextResponse.json(
        { error: "User branch ID is required" },
        { status: 400 },
      );
    }

    // First, get the vehicle ID - normalize registration number by removing spaces
    const normalizedRegNumber = registration_number.replace(/\s+/g, "");

    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select("vehicle_id")
      .or(
        `registration_number.ilike.${normalizedRegNumber},registration_number.ilike.%${registration_number}%`,
      )
      .single();

    if (vehicleError) {
      console.error("Error finding vehicle:", vehicleError);
      return NextResponse.json(
        { available: false, error: "Vehicle not found" },
        { status: 404 },
      );
    }

    // Find active memo for the vehicle where the user's branch is the destination branch
    const { data: memos, error: memoError } = await supabase
      .from("memos")
      .select(`
        *,
        vehicle:vehicles(*),
        from_branch:branches!from_branch_id(*),
        to_branch:branches!to_branch_id(*)
      `)
      .eq("vehicle_id", vehicle.vehicle_id)
      .eq("to_branch_id", user_branch_id) // Important: Look for memos where user's branch is the destination
      .eq("status", "Created") // Only look for memos with "Created" status
      .order("created_at", { ascending: false });

    if (memoError) {
      console.error("Error finding memo:", memoError);
      return NextResponse.json(
        { available: false, error: "Failed to find memo for this vehicle" },
        { status: 500 },
      );
    }

    if (!memos || memos.length === 0) {
      // Check if there are any memos for this vehicle regardless of destination
      const { data: allMemos, error: allMemosError } = await supabase
        .from("memos")
        .select(`
          *,
          vehicle:vehicles(*),
          from_branch:branches!from_branch_id(*),
          to_branch:branches!to_branch_id(*)
        `)
        .eq("vehicle_id", vehicle.vehicle_id)
        .eq("status", "Created") // Only look for memos with "Created" status
        .order("created_at", { ascending: false });

      if (allMemosError) {
        console.error("Error finding all memos:", allMemosError);
        return NextResponse.json(
          { available: false, error: "No active memo found for this vehicle" },
          { status: 404 },
        );
      }

      if (allMemos && allMemos.length > 0) {
        // There are memos, but not with the user's branch as destination
        return NextResponse.json(
          {
            available: false,
            error:
              "This vehicle has an active memo with 'Created' status, but your branch is not the destination",
            wrong_branch: true,
          },
          { status: 403 },
        );
      } else {
        return NextResponse.json(
          { available: false, error: "No active memo found for this vehicle" },
          { status: 404 },
        );
      }
    }

    // Use the most recent memo
    const memo = memos[0];

    // Check if there are loading charts with parcels to unload
    const { data: loadingCharts, error: chartsError } = await supabase
      .from("loading_charts")
      .select(`
        *,
        items:loading_chart_items(*)
      `)
      .eq("memo_id", memo.memo_id)
      .eq("destination_branch_id", user_branch_id)
      .eq("status", "Created");

    if (chartsError || !loadingCharts || loadingCharts.length === 0) {
      return NextResponse.json(
        {
          available: false,
          error: "No parcels to unload for this vehicle at your branch",
        },
        { status: 404 },
      );
    }

    // Check if there are any items to unload
    const hasItemsToUnload = loadingCharts.some(
      (chart) =>
        chart.items &&
        chart.items.some((item) => item.status === "Pending"),
    );

    if (!hasItemsToUnload) {
      return NextResponse.json(
        {
          available: false,
          error: "All parcels from this vehicle have already been received",
        },
        { status: 404 },
      );
    }

    // We no longer fetch all parcels here - we'll fetch them one by one when the user enters an LR number
    return NextResponse.json({
      available: true,
      memo,
      loadingCharts,
    });
  } catch (error: any) {
    console.error("Error in GET /api/memos/vehicle/receive:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
