-- Add auth_id column to users table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'auth_id'
  ) THEN
    -- Add the auth_id column
    ALTER TABLE public.users ADD COLUMN auth_id UUID UNIQUE;
    
    -- Create an index for faster lookups
    CREATE INDEX idx_users_auth_id ON public.users(auth_id);
    
    RAISE NOTICE 'Added auth_id column to users table';
  ELSE
    RAISE NOTICE 'auth_id column already exists in users table';
  END IF;
END
$$;
