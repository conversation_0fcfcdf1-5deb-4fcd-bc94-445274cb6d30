      {/* View Memo Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Memo Details</DialogTitle>
            <DialogDescription>
              Complete information about the selected memo
            </DialogDescription>
          </DialogHeader>
          {selectedMemo && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p className="text-sm font-medium">Memo Number</p>
                  <p className="text-sm">{selectedMemo.memo_number}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge variant={
                    selectedMemo.status === 'Completed' ? 'default' :
                    selectedMemo.status === 'Received' ? 'secondary' :
                    selectedMemo.status === 'Created' ? 'destructive' : 'default'
                  }>
                    {selectedMemo.status}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Vehicle</p>
                  <p className="text-sm">{selectedMemo.vehicle?.registration_number || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Vehicle Type</p>
                  <p className="text-sm">{selectedMemo.vehicle?.vehicle_type || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-sm font-medium">Drivers</p>
                  <p className="text-sm">
                    {selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids)
                      ? selectedMemo.driver_ids.map((id: any, index: number) => {
                          const driverNumber = selectedMemo.driver_numbers?.[index];
                          return `Driver ${index + 1}: ${driverNumber ? `ID ${driverNumber}` : `ID ${id}`}`;
                        }).join(', ')
                      : 'N/A'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">From Branch</p>
                  <p className="text-sm">{selectedMemo.from_branch?.name || 'N/A'} ({selectedMemo.from_branch?.code || 'N/A'})</p>
                </div>
                <div>
                  <p className="text-sm font-medium">To Branch</p>
                  <p className="text-sm">{selectedMemo.to_branch?.name || 'N/A'} ({selectedMemo.to_branch?.code || 'N/A'})</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Created At</p>
                  <p className="text-sm">{selectedMemo.created_at ? format(new Date(selectedMemo.created_at), 'dd MMM yyyy, HH:mm') : 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Created By</p>
                  <p className="text-sm">{selectedMemo.creator?.name || 'N/A'}</p>
                </div>

                {selectedMemo.status === 'Received' || selectedMemo.status === 'Completed' ? (
                  <>
                    <div>
                      <p className="text-sm font-medium">Received At</p>
                      <p className="text-sm">{selectedMemo.received_at ? format(new Date(selectedMemo.received_at), 'dd MMM yyyy, HH:mm') : 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Fuel Consumed</p>
                      <p className="text-sm">{selectedMemo.fuel_consumed || 0} L</p>
                    </div>
                  </>
                ) : null}

                {selectedMemo.status === 'Completed' ? (
                  <>
                    <div>
                      <p className="text-sm font-medium">BATA Amount</p>
                      <p className="text-sm">₹{selectedMemo.bata_amount || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Salary Amount</p>
                      <p className="text-sm">₹{selectedMemo.salary_amount || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Total Expense</p>
                      <p className="text-sm">₹{selectedMemo.total_expense || 0}</p>
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          )}
          <DialogFooter>
            {selectedMemo && selectedMemo.status === 'Completed' && (
              <Button
                variant="outline"
                onClick={() => {
                  // Format the WhatsApp message
                  const driverCount = selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids)
                    ? selectedMemo.driver_ids.length
                    : 1;

                  let message = `Memo ${selectedMemo.memo_number} has been completed.\n` +
                    `From: ${selectedMemo.from_branch?.name || 'N/A'} → To: ${selectedMemo.to_branch?.name || 'N/A'}\n` +
                    `Vehicle: ${selectedMemo.vehicle?.registration_number || 'N/A'}\n` +
                    `Drivers: ${driverCount}\n` +
                    `Fuel Consumed: ${selectedMemo.fuel_consumed || 0} L\n` +
                    `BATA Amount: ₹${selectedMemo.bata_amount || 0}\n` +
                    `Salary Amount: ₹${selectedMemo.salary_amount || 0}\n` +
                    `Total Expense: ₹${selectedMemo.total_expense || 0}`;

                  // Add driver expense details if available
                  if (selectedMemo.driver_expenses && Array.isArray(selectedMemo.driver_expenses) && selectedMemo.driver_expenses.length > 0) {
                    message += "\n\nDriver Details:";
                    selectedMemo.driver_expenses.forEach((driver: any, index: number) => {
                      const driverNumber = driver.driver_number || selectedMemo.driver_numbers?.[index];
                      message += `\nDriver ${index + 1}${driverNumber ? ` (ID: ${driverNumber})` : ` (ID: ${driver.driver_id})`}:\n` +
                        `  BATA: ₹${driver.bata_amount || 0}\n` +
                        `  Salary: ₹${driver.salary_amount || 0}`;
                    });
                  }

                  // Encode the message for WhatsApp
                  const encodedMessage = encodeURIComponent(message);

                  // Open WhatsApp with the pre-filled message
                  window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                }}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Send WhatsApp
              </Button>
            )}
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Receive Memo Dialog */}
      <Dialog open={isReceiveDialogOpen} onOpenChange={setIsReceiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Receive Memo</DialogTitle>
            <DialogDescription>
              Enter fuel consumption details to receive memo {selectedMemo?.memo_number}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fuel">Fuel Used (Litres)</Label>
              <Input
                id="fuel"
                placeholder="Enter fuel used in litres"
                value={fuelLitres}
                onChange={(e) => setFuelLitres(e.target.value)}
                type="number"
              />
            </div>
            <div className="pt-4 border-t mt-4">
              <p className="text-sm text-muted-foreground mb-2">Driver information:</p>

              {/* Driver information */}
              {selectedMemo && selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && (
                <div className="mb-4 p-3 bg-muted rounded-md">
                  <p className="font-medium mb-2">Driver Information:</p>
                  {selectedMemo.driver_ids.map((driverId: any, index: number) => {
                    const driverNumber = selectedMemo.driver_numbers?.[index];
                    return (
                      <p key={driverId} className="text-sm">
                        Driver {index + 1}: {driverNumber ? `ID ${driverNumber}` : `ID ${driverId}`}
                      </p>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReceiveDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleReceive}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Receive Memo"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
