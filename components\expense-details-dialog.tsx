"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogDescription, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { FileText } from "lucide-react"
import { useUser } from "@/hooks/use-user"

interface Expense {
  id: number;
  category: string;
  amount: number;
  submitted_at: string;
  description: string;
  approval_status: string;
  vendor_name: string;
  invoice_number: string;
  payment_method: string;
  approved_by?: number;
  approved_at?: string;
  rejected_by?: number;
  rejected_at?: string;
  rejection_reason?: string;
  receipt_url?: string;
  memo_number?: string;
  branch_id: number;
  branch?: {
    name: string;
    code: string;
  };
  submitter?: {
    name: string;
  };
  approver?: {
    name: string;
  };
  rejecter?: {
    name: string;
  };
}

interface ExpenseDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  expense: Expense
}

const statusColors = {
  pending: "bg-yellow-500",
  approved: "bg-green-500",
  rejected: "bg-red-500"
}

const statusLabels = {
  pending: "Pending",
  approved: "Approved",
  rejected: "Rejected"
}

const categoryLabels = {
  fuel: "Fuel",
  maintenance: "Vehicle Maintenance",
  supplies: "Office Supplies",
  utilities: "Utilities",
  driver_allowance: "Driver Allowance",
  equipment: "Equipment",
  repairs: "Repairs",
  other: "Other"
}

const paymentMethods = [
  { value: "cash", label: "Cash" },
  { value: "corporate_card", label: "Corporate Card" },
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "upi", label: "UPI Payment" },
]

export function ExpenseDetailsDialog({ open, onOpenChange, expense }: ExpenseDetailsDialogProps) {
  // Simplified component without approval/rejection functionality
  // This functionality is now only available in the admin app
  const { userDetails } = useUser()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Expense Details - {expense.id}</span>
            <Badge className={statusColors[expense.approval_status.toLowerCase() as keyof typeof statusColors]}>
              {statusLabels[expense.approval_status.toLowerCase() as keyof typeof statusLabels]}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Submitted on {new Date(expense.submitted_at).toLocaleDateString()}
            {expense.submitter?.name && ` by ${expense.submitter.name}`}
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-6 p-4">
            <div>
              <h3 className="text-lg font-semibold">Basic Information</h3>
              <div className="mt-4 grid gap-4 md:grid-cols-2">
                <div>
                  <p className="font-medium">Category</p>
                  <p className="text-muted-foreground">
                    {categoryLabels[expense.category as keyof typeof categoryLabels] || expense.category}
                  </p>
                </div>
                <div>
                  <p className="font-medium">Amount</p>
                  <p className="text-muted-foreground">₹{expense.amount.toLocaleString()}</p>
                </div>
                {expense.branch && (
                  <div>
                    <p className="font-medium">Branch</p>
                    <p className="text-muted-foreground">{expense.branch.name} ({expense.branch.code})</p>
                  </div>
                )}
                {expense.memo_number && (
                  <div>
                    <p className="font-medium">Memo Number</p>
                    <p className="text-muted-foreground">{expense.memo_number}</p>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Vendor Information</h3>
              <div className="mt-2 space-y-2">
                <p><span className="font-medium">Name:</span> {expense.vendor_name}</p>
                <p><span className="font-medium">Invoice Number:</span> {expense.invoice_number}</p>
                <p>
                  <span className="font-medium">Payment Method:</span>{" "}
                  {paymentMethods.find(m => m.value === expense.payment_method)?.label || expense.payment_method}
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Description</h3>
              <p className="mt-2 text-muted-foreground">{expense.description}</p>
            </div>

            {(expense.approval_status.toLowerCase() === "approved" || expense.approval_status.toLowerCase() === "rejected") && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-semibold">Status Information</h3>
                  <div className="mt-2 space-y-2">
                    {expense.approval_status.toLowerCase() === "approved" && (
                      <>
                        <p><span className="font-medium">Approved By:</span> {expense.approver?.name || "System"}</p>
                        {expense.approved_at && (
                          <p>
                            <span className="font-medium">Approved On:</span>{" "}
                            {new Date(expense.approved_at).toLocaleString()}
                          </p>
                        )}
                      </>
                    )}
                    {expense.approval_status.toLowerCase() === "rejected" && (
                      <>
                        <p><span className="font-medium">Rejected By:</span> {expense.rejecter?.name || "System"}</p>
                        {expense.rejected_at && (
                          <p>
                            <span className="font-medium">Rejected On:</span>{" "}
                            {new Date(expense.rejected_at).toLocaleString()}
                          </p>
                        )}
                        <p><span className="font-medium">Reason:</span> {expense.rejection_reason}</p>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}

            {expense.receipt_url && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-semibold">Receipt</h3>
                  <div className="mt-4 space-y-4">
                    {/* Preview for image receipts */}
                    {expense.receipt_url.match(/\.(jpeg|jpg|gif|png)$/i) && (
                      <div className="border rounded-md overflow-hidden">
                        <img
                          src={expense.receipt_url}
                          alt="Receipt"
                          className="w-full h-auto max-h-[300px] object-contain"
                        />
                      </div>
                    )}

                    {/* Button to open receipt in new tab */}
                    <Button variant="outline" className="w-full" onClick={() => window.open(expense.receipt_url)}>
                      <FileText className="mr-2 h-4 w-4" />
                      {expense.receipt_url.match(/\.pdf$/i) ? "View PDF Receipt" : "View Receipt in New Tab"}
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* Rejection form removed */}
          </div>
        </ScrollArea>

        {/* Approval buttons removed - can only be done from admin app */}
        <DialogFooter>
          <div className="w-full text-center text-sm text-muted-foreground mb-2">
            Expense approval can only be done from the admin application
          </div>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}