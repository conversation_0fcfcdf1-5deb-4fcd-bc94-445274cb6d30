"use client"

import { useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

export default function AuthDebugPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [authState, setAuthState] = useState<any>(null)
  const [userProfile, setUserProfile] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString()}: ${message}`])
  }

  useEffect(() => {
    const checkAuth = async () => {
      try {
        addLog("Starting auth debug check...")

        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          addLog(`Session error: ${sessionError.message}`)
          setError(`Session error: ${sessionError.message}`)
          setIsLoading(false)
          return
        }

        if (!session) {
          addLog("No session found")
          setAuthState({ status: "No session" })
          setIsLoading(false)
          return
        }

        addLog(`Session found: ${session.user.id} (${session.user.email})`)
        setAuthState({
          status: "Authenticated",
          user: session.user,
          session: session
        })

        // Try to get user profile
        try {
          addLog("Looking up user profile by email...")
          const { data: profileData, error: profileError } = await supabase
            .from("users")
            .select("*")
            .eq("email", session.user.email)
            .single()

          if (profileError) {
            addLog(`Profile error: ${profileError.message}`)
            setError(`Could not find user profile: ${profileError.message}`)
          } else if (!profileData) {
            addLog("No profile data found")
            setError("User profile not found")
          } else {
            addLog(`Profile found: user_id=${profileData.user_id}, role=${profileData.role}, branch_id=${profileData.branch_id}`)
            setUserProfile(profileData)
          }
        } catch (profileLookupError: any) {
          addLog(`Profile lookup error: ${profileLookupError}`)
          setError(`Error looking up profile: ${profileLookupError}`)
        }
      } catch (error: any) {
        addLog(`Unexpected error: ${error}`)
        setError(`Unexpected error: ${error}`)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      addLog(`Auth state change: ${event}`)
      if (session) {
        addLog(`New session: ${session.user.id} (${session.user.email})`)
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const handleSignOut = async () => {
    try {
      addLog("Signing out...")
      await supabase.auth.signOut()
      addLog("Sign out successful")
      window.location.href = "/login"
    } catch (error: any) {
      addLog(`Sign out error: ${error}`)
      setError(`Sign out error: ${error}`)
    }
  }

  const handleGoToHome = () => {
    addLog("Navigating to home page...")
    window.location.href = "/"
  }

  const handleGoToLogin = () => {
    addLog("Navigating to login page...")
    window.location.href = "/login"
  }

  const handleGoToCheckBranch = () => {
    addLog("Navigating to check-branch page...")
    window.location.href = "/check-branch"
  }

  const handleGoToDirectDashboard = () => {
    addLog("Navigating to direct-dashboard page...")
    window.location.href = "/direct-dashboard"
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Debug</CardTitle>
          <CardDescription>
            This page helps diagnose authentication issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Checking authentication state...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Authentication State</h3>
                <pre className="whitespace-pre-wrap overflow-auto max-h-[200px] text-xs p-2 bg-background rounded">
                  {JSON.stringify(authState, null, 2)}
                </pre>
              </div>

              {userProfile && (
                <div className="p-4 bg-muted rounded-md">
                  <h3 className="font-bold mb-2">User Profile</h3>
                  <pre className="whitespace-pre-wrap overflow-auto max-h-[200px] text-xs p-2 bg-background rounded">
                    {JSON.stringify(userProfile, null, 2)}
                  </pre>
                </div>
              )}

              {error && (
                <div className="p-4 bg-destructive/10 text-destructive rounded-md">
                  <h3 className="font-bold mb-2">Error</h3>
                  <p>{error}</p>
                </div>
              )}

              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Debug Logs</h3>
                <div className="whitespace-pre-wrap overflow-auto max-h-[300px] text-xs p-2 bg-background rounded">
                  {logs.map((log, index) => (
                    <div key={index} className="py-1 border-b border-border last:border-0">
                      {log}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button onClick={handleGoToHome} variant="outline">
            Go to Home
          </Button>
          <Button onClick={handleGoToDirectDashboard} variant="default">
            Direct Dashboard (Try This)
          </Button>
          <Button onClick={handleGoToLogin} variant="outline">
            Go to Login
          </Button>
          <Button onClick={handleGoToCheckBranch} variant="outline">
            Go to Check Branch
          </Button>
          <Button onClick={handleSignOut} variant="destructive">
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
