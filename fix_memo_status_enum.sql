-- First, let's check what values are currently in the enum
DO $$
DECLARE
    enum_values text[];
BEGIN
    -- Get the current values of the memo_status enum
    SELECT array_agg(enumlabel::text) INTO enum_values
    FROM pg_enum
    WHERE enumtypid = 'memo_status'::regtype;
    
    -- Output the current values for debugging
    RAISE NOTICE 'Current memo_status values: %', enum_values;
END $$;

-- Create a new enum type with all the values we want
CREATE TYPE memo_status_new AS ENUM ('Created', 'In Transit', 'Received', 'Completed');

-- Update the table to use the new enum type
-- This will convert existing values to the new type
ALTER TABLE memos
  ALTER COLUMN status TYPE memo_status_new 
  USING (CASE 
           WHEN status::text = 'Loaded' THEN 'In Transit'
           WHEN status::text = 'Created' THEN 'Created'
           WHEN status::text = 'Received' THEN 'Received'
           WHEN status::text = 'Completed' THEN 'Completed'
           ELSE 'Created' -- Default fallback
         END)::memo_status_new;

-- Drop the old enum type
DROP TYPE memo_status;

-- Rename the new enum type to the original name
ALTER TYPE memo_status_new RENAME TO memo_status;

-- Update the default value for the status column
ALTER TABLE memos ALTER COLUMN status SET DEFAULT 'Created';

-- Verify the new enum values
DO $$
DECLARE
    enum_values text[];
BEGIN
    -- Get the updated values of the memo_status enum
    SELECT array_agg(enumlabel::text) INTO enum_values
    FROM pg_enum
    WHERE enumtypid = 'memo_status'::regtype;
    
    -- Output the new values for confirmation
    RAISE NOTICE 'New memo_status values: %', enum_values;
END $$;
