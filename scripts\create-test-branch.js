// Script to create a test branch in the database
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createTestBranch() {
  try {
    console.log("Creating test branch...");

    // Check if a branch with code 'TST' already exists
    const { data: existingBranch, error: checkError } = await supabase
      .from("branches")
      .select("branch_id, name, code")
      .eq("code", "TST")
      .single();

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Error checking for existing branch:", checkError);
      return;
    }

    if (existingBranch) {
      console.log("Test branch already exists:", existingBranch);
      return existingBranch;
    }

    // Create a new test branch
    const testBranch = {
      name: "Test Branch",
      code: "TST",
      address: "123 Test Street, Test City",
      phone: "1234567890",
      email: "<EMAIL>",
      operating_hours: JSON.stringify({
        monday: { open: "09:00", close: "18:00" },
        tuesday: { open: "09:00", close: "18:00" },
        wednesday: { open: "09:00", close: "18:00" },
        thursday: { open: "09:00", close: "18:00" },
        friday: { open: "09:00", close: "18:00" },
        saturday: { open: "10:00", close: "15:00" },
        sunday: { open: "closed", close: "closed" },
      }),
      status: "Active",
      location_coordinates: JSON.stringify({ lat: 12.34, lng: 56.78 }),
      branch_type: "Sub",
      service_area: "Test Area",
    };

    const { data, error } = await supabase
      .from("branches")
      .insert([testBranch])
      .select();

    if (error) {
      console.error("Error creating test branch:", error);
      return;
    }

    console.log("Test branch created successfully:", data[0]);
    return data[0];
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function assignUserToBranch(userId, branchId) {
  try {
    console.log(`Assigning user ${userId} to branch ${branchId}...`);

    // Update the user's branch_id
    const { data, error } = await supabase
      .from("users")
      .update({ branch_id: branchId })
      .eq("user_id", userId)
      .select();

    if (error) {
      console.error("Error assigning user to branch:", error);
      return;
    }

    console.log("User assigned to branch successfully:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function listUsers() {
  try {
    console.log("Listing users...");

    const { data, error } = await supabase
      .from("users")
      .select("*");

    if (error) {
      console.error("Error listing users:", error);
      return;
    }

    console.log("Users:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function main() {
  console.log("Starting script...");

  try {
    // Create a test branch
    console.log("Attempting to create test branch...");
    const branch = await createTestBranch();

    if (!branch) {
      console.log("Failed to create or find test branch");
      return;
    }

    // List users
    console.log("Fetching users...");
    const users = await listUsers();

    if (!users || users.length === 0) {
      console.log("No users found");
      return;
    }

    // Ask which user to assign to the branch
    console.log("\nAvailable users:");
    users.forEach((user, index) => {
      console.log(
        `${index + 1}. ${user.name} (${user.email}) - ID: ${user.user_id}`,
      );
    });

    // For now, just assign the first user to the branch
    if (users.length > 0) {
      console.log(
        `Assigning user ${users[0].user_id} to branch ${branch.branch_id}...`,
      );
      await assignUserToBranch(users[0].user_id, branch.branch_id);
    }

    console.log("Script completed successfully!");
  } catch (error) {
    console.error("Error in main function:", error);
  }
}

main();
