"use client"

import { <PERSON>actNode, useState } from "react"
import { AppHeader } from "@/components/app-header"
import { MobileNav } from "@/components/mobile-nav"
import { AuthGuard } from "@/components/auth-guard"

interface PageLayoutProps {
  children: ReactNode
  title?: string
  subtitle?: string
  showActions?: boolean
}

export function PageLayout({
  children,
  title,
  subtitle,
  showActions = false
}: PageLayoutProps) {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false)

  return (
    <AuthGuard>
      <div className="min-h-screen bg-white">
        <AppHeader
          onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)}
          showActions={showActions}
        />

        <MobileNav isOpen={isMobileNavOpen} onClose={() => setIsMobileNavOpen(false)} />

        <main className="container px-4 md:px-6 py-4 bg-white">
          {title && (
            <div className="mb-4">
              <h1 className="text-3xl font-bold tracking-tight text-primary">{title}</h1>
              {subtitle && <p className="text-muted-foreground">{subtitle}</p>}
            </div>
          )}
          {children}
        </main>
      </div>
    </AuthGuard>
  )
}
