"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Package, User, FileText, Camera } from "lucide-react"
import { ImageUploadField } from "@/components/image-upload-field"

interface DeliveryDialogProps {
  isOpen: boolean
  onClose: () => void
  parcel: {
    parcel_id: number
    lr_number: string
    sender_name: string
    recipient_name: string
    recipient_phone?: string
    number_of_items: number
    total_received_items: number
  } | null
  onDeliveryComplete: () => void
}

const ID_TYPES = [
  "Aadhaar Card",
  "PAN Card", 
  "Driving License",
  "Voter ID",
  "Passport",
  "Other"
]

export function ParcelDeliveryDialog({ 
  isOpen, 
  onClose, 
  parcel, 
  onDeliveryComplete 
}: DeliveryDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    recipient_name: "",
    recipient_id_type: "",
    recipient_id_number: "",
    delivery_notes: "",
    proof_url: ""
  })

  // Reset form when parcel changes
  useState(() => {
    if (parcel) {
      setFormData({
        recipient_name: parcel.recipient_name || "",
        recipient_id_type: "",
        recipient_id_number: "",
        delivery_notes: "",
        proof_url: ""
      })
    }
  }, [parcel])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!parcel) return

    // Validate required fields
    if (!formData.recipient_name.trim()) {
      toast({
        title: "Missing Information",
        description: "Recipient name is required",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/parcels/deliver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parcel_id: parcel.parcel_id,
          recipient_name: formData.recipient_name,
          recipient_id_type: formData.recipient_id_type || null,
          recipient_id_number: formData.recipient_id_number || null,
          delivery_notes: formData.delivery_notes || null,
          proof_url: formData.proof_url || null
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: "Delivery Successful",
          description: `Parcel ${data.lr_number} has been delivered to ${data.delivered_to}`,
        })
        onDeliveryComplete()
        onClose()
        // Reset form
        setFormData({
          recipient_name: "",
          recipient_id_type: "",
          recipient_id_number: "",
          delivery_notes: "",
          proof_url: ""
        })
      } else {
        toast({
          title: "Delivery Failed",
          description: data.error || "Failed to deliver parcel",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error delivering parcel:', error)
      toast({
        title: "Error",
        description: "Failed to deliver parcel. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!parcel) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Deliver Parcel</span>
          </DialogTitle>
          <DialogDescription>
            Confirm delivery of parcel {parcel.lr_number} to the recipient
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 px-1">
          <form onSubmit={handleSubmit} className="space-y-4 pr-3">
          {/* Parcel Information */}
          <div className="p-4 bg-muted rounded-lg space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">LR Number:</span>
              <span>{parcel.lr_number}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Sender:</span>
              <span>{parcel.sender_name}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Items:</span>
              <span>{parcel.total_received_items} of {parcel.number_of_items}</span>
            </div>
          </div>

          {/* Recipient Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-2">
              <User className="h-4 w-4" />
              <Label className="text-base font-medium">Recipient Details</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="recipient_name">Recipient Name *</Label>
              <Input
                id="recipient_name"
                value={formData.recipient_name}
                onChange={(e) => setFormData(prev => ({ ...prev, recipient_name: e.target.value }))}
                placeholder="Enter recipient name"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="recipient_id_type">ID Type</Label>
                <Select 
                  value={formData.recipient_id_type} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, recipient_id_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select ID type" />
                  </SelectTrigger>
                  <SelectContent>
                    {ID_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="recipient_id_number">ID Number</Label>
                <Input
                  id="recipient_id_number"
                  value={formData.recipient_id_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, recipient_id_number: e.target.value }))}
                  placeholder="Enter ID number"
                />
              </div>
            </div>
          </div>

          {/* Delivery Proof */}
          <div className="space-y-4">
            <ImageUploadField
              label="Delivery Proof (Optional)"
              value={formData.proof_url}
              onChange={(url) => setFormData(prev => ({ ...prev, proof_url: url }))}
              bucketName="branch-img"
              maxSizeMB={5}
            />
          </div>

          {/* Delivery Notes */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-2">
              <FileText className="h-4 w-4" />
              <Label className="text-base font-medium">Delivery Notes</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="delivery_notes">Additional Notes</Label>
              <Textarea
                id="delivery_notes"
                value={formData.delivery_notes}
                onChange={(e) => setFormData(prev => ({ ...prev, delivery_notes: e.target.value }))}
                placeholder="Enter any additional delivery notes..."
                rows={3}
              />
            </div>
          </div>

          </form>
        </ScrollArea>

        <DialogFooter className="flex-shrink-0">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            onClick={handleSubmit}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Delivering...
              </>
            ) : (
              "Confirm Delivery"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
