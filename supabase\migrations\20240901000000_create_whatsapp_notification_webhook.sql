-- Create a webhook to trigger the WhatsApp notification edge function
-- when a parcel's status changes

-- This is a SQL comment that Supa<PERSON> will use to create the webhook
-- name:send-whatsapp-notification
-- schema:public
-- table:parcels
-- event:UPDATE
-- filter:current_status
-- url:https://{{PROJECT_REF}}.supabase.co/functions/v1/send-whatsapp-notification
-- method:POST
-- headers:Content-Type:application/json
-- headers:Authorization:Bearer {{SERVICE_ROLE_KEY}}
-- enabled:true

-- Note: The above comment is processed by Supabase migrations to create the webhook.
-- The actual webhook creation happens in the Supabase dashboard or via the API.

-- Create a notification log table to track sent notifications
CREATE TABLE IF NOT EXISTS notification_logs (
  id SERIAL PRIMARY KEY,
  parcel_id INT REFERENCES parcels(parcel_id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL,
  recipient_phone VARCHAR(20) NOT NULL,
  status VARCHAR(50) NOT NULL,
  sent_at TIMESTAMPTZ DEFAULT now(),
  success BOOLEAN NOT NULL,
  error_message TEXT,
  response_data JSONB
);

-- Create an index for faster lookups
CREATE INDEX IF NOT EXISTS notification_logs_parcel_id_idx ON notification_logs(parcel_id);
CREATE INDEX IF NOT EXISTS notification_logs_sent_at_idx ON notification_logs(sent_at);

-- Add a function to log notifications (this will be called from the edge function)
CREATE OR REPLACE FUNCTION log_notification(
  p_parcel_id INT,
  p_notification_type VARCHAR(50),
  p_recipient_phone VARCHAR(20),
  p_status VARCHAR(50),
  p_success BOOLEAN,
  p_error_message TEXT DEFAULT NULL,
  p_response_data JSONB DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  INSERT INTO notification_logs (
    parcel_id,
    notification_type,
    recipient_phone,
    status,
    success,
    error_message,
    response_data
  ) VALUES (
    p_parcel_id,
    p_notification_type,
    p_recipient_phone,
    p_status,
    p_success,
    p_error_message,
    p_response_data
  );
END;
$$ LANGUAGE plpgsql;

-- Grant permissions to the authenticated and service roles
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- Policy for service role (can do everything)
CREATE POLICY service_role_notification_logs_policy ON notification_logs
  FOR ALL
  TO service_role
  USING (true);

-- Policy for authenticated users (can only view logs for parcels in their branch)
CREATE POLICY authenticated_notification_logs_policy ON notification_logs
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM parcels p
      JOIN users u ON (p.sender_branch_id = u.branch_id OR p.delivery_branch_id = u.branch_id)
      WHERE p.parcel_id = notification_logs.parcel_id
      AND u.auth_id = auth.uid()
    )
  );

-- Grant usage permissions
GRANT SELECT, INSERT ON notification_logs TO service_role;
GRANT SELECT ON notification_logs TO authenticated;
GRANT USAGE ON SEQUENCE notification_logs_id_seq TO service_role;
