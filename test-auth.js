// Test script to check if the authentication system is working
const { createClient } = require("@supabase/supabase-js");

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test user credentials
const testEmail = "<EMAIL>";
const testPassword = "Password123!";
const testName = "Test User";

// Function to test signup
async function testSignup() {
  console.log("Testing signup...");

  try {
    // Sign up the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          name: testName,
          full_name: testName,
        },
      },
    });

    if (authError) {
      console.error("Error signing up:", authError);
      return false;
    }

    console.log("User signed up successfully:", authData.user.id);

    // Check if the user was created in the public users table
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("*")
      .eq("id", authData.user.id)
      .single();

    if (userError) {
      console.error("Error getting user from public table:", userError);

      // Try to create the user in the public table
      console.log("Attempting to create user in public table...");

      const { data: insertData, error: insertError } = await supabase
        .from("users")
        .insert([
          {
            id: authData.user.id,
            email: testEmail,
            name: testName,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            role: "user",
            is_active: true,
          },
        ])
        .select();

      if (insertError) {
        console.error("Error creating user in public table:", insertError);
        return false;
      }

      console.log("User created in public table:", insertData);
      return true;
    }

    console.log("User found in public table:", userData);
    return true;
  } catch (error) {
    console.error("Unexpected error during signup test:", error);
    return false;
  }
}

// Function to test signin
async function testSignin() {
  console.log("Testing signin...");

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword,
    });

    if (error) {
      console.error("Error signing in:", error);
      return false;
    }

    console.log("User signed in successfully:", data.user.id);
    return true;
  } catch (error) {
    console.error("Unexpected error during signin test:", error);
    return false;
  }
}

// Run the tests
async function runTests() {
  const signupResult = await testSignup();
  console.log("Signup test result:", signupResult ? "PASSED" : "FAILED");

  const signinResult = await testSignin();
  console.log("Signin test result:", signinResult ? "PASSED" : "FAILED");

  process.exit(0);
}

runTests();
