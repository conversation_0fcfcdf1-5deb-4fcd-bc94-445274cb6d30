import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/auth/test-branch
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ 
        error: "Unauthorized", 
        message: "No session found"
      }, { status: 401 });
    }

    // Get user's branch ID
    const { data: user, error } = await supabase
      .from("users")
      .select("branch_id, id, email")
      .eq("id", session.user.id)
      .single();

    if (error) {
      console.error("Error fetching user branch:", error);
      return NextResponse.json({ 
        error: "Failed to fetch user branch",
        message: error.message,
        details: error
      }, {
        status: 500,
      });
    }

    // Get branch details
    let branchDetails = null;
    if (user?.branch_id) {
      const { data: branch, error: branchError } = await supabase
        .from("branches")
        .select("*")
        .eq("branch_id", user.branch_id)
        .single();
        
      if (!branchError) {
        branchDetails = branch;
      } else {
        console.error("Error fetching branch details:", branchError);
      }
    }

    return NextResponse.json({
      branch_id: user?.branch_id || null,
      user_id: user?.id || null,
      email: user?.email || null,
      branch_details: branchDetails,
      session_user_id: session.user.id,
    });
  } catch (error: any) {
    console.error("Error in GET /api/auth/test-branch:", error);
    return NextResponse.json({ 
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, {
      status: 500,
    });
  }
}
