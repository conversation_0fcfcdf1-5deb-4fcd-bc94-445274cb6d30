import { NextRequest, NextResponse } from 'next/server';
import { getParcelTypes, createParcelType } from '@/lib/db-helpers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// GET /api/parceltypes
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filters: Record<string, any> = {};
    
    // Extract filter parameters
    if (searchParams.has('type_name')) {
      filters.type_name = searchParams.get('type_name');
    }
    
    // Get parcel types with filters
    const parcelTypes = await getParcelTypes(filters);
    
    if (!parcelTypes) {
      return NextResponse.json({ error: 'Failed to fetch parcel types' }, { status: 500 });
    }
    
    return NextResponse.json(parcelTypes);
  } catch (error: any) {
    console.error('Error in GET /api/parceltypes:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/parceltypes
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.type_name) {
      return NextResponse.json(
        { error: 'Missing required field: type_name' },
        { status: 400 }
      );
    }
    
    // Create parcel type
    const parcelType = await createParcelType(body);
    
    if (!parcelType) {
      return NextResponse.json({ error: 'Failed to create parcel type' }, { status: 500 });
    }
    
    return NextResponse.json(parcelType, { status: 201 });
  } catch (error: any) {
    console.error('Error in POST /api/parceltypes:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
