import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// SQL to fix the update_parcel_status_on_loading function
const FIX_LOADING_CHART_TRIGGER = `
CREATE OR REPLACE FUNCTION update_parcel_status_on_loading()
RETURNS TRIGGER AS $$
BEGIN
  -- Update parcel status to 'In Transit' when loaded
  UPDATE parcels
  SET current_status = 'In Transit'
  WHERE lr_number = NEW.lr_number;

  -- Create an operation record
  INSERT INTO operations (
    operation_type,
    vehicle_id,
    parcel_id,
    confirmed_item_count,
    total_item_count,
    operator_id
  )
  SELECT
    'Load'::op_type,
    lc.vehicle_id,
    p.parcel_id,
    NEW.quantity,
    p.number_of_items,
    (SELECT user_id FROM users WHERE auth_id = (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id))
  FROM
    loading_charts lc
    JOIN parcels p ON p.lr_number = NEW.lr_number
  WHERE
    lc.chart_id = NEW.chart_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
`;

// POST /api/migrations/fix-loading-chart-trigger
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is an admin
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("user_id", session.user.id)
      .single();

    if (userError || !user || user.role !== "Admin") {
      return NextResponse.json({ error: "Only admins can run migrations" }, {
        status: 403,
      });
    }

    // Execute the SQL to fix the trigger function
    const { error: fixError } = await supabase.rpc("exec_sql", {
      sql: FIX_LOADING_CHART_TRIGGER,
    });

    if (fixError) {
      console.error("Error fixing loading chart trigger:", fixError);
      return NextResponse.json({
        error: "Failed to fix loading chart trigger",
      }, { status: 500 });
    }

    return NextResponse.json({
      message: "Loading chart trigger fixed successfully",
    });
  } catch (error: any) {
    console.error(
      "Error in POST /api/migrations/fix-loading-chart-trigger:",
      error,
    );
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
