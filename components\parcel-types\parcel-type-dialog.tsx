'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { ParcelType, NewParcelType } from '@/lib/db-helpers';

interface ParcelTypeDialogProps {
  open: boolean;
  onClose: (refresh: boolean) => void;
  parcelType: ParcelType | null;
}

export function ParcelTypeDialog({
  open,
  onClose,
  parcelType,
}: ParcelTypeDialogProps) {
  const [formData, setFormData] = useState<NewParcelType>({
    type_name: '',
    base_price: null,
    per_kg_rate: null,
    weight_restriction: null,
    special_handling_instructions: null,
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Initialize form data when parcel type changes
  useEffect(() => {
    if (parcelType) {
      setFormData({
        type_name: parcelType.type_name,
        base_price: parcelType.base_price,
        per_kg_rate: parcelType.per_kg_rate,
        weight_restriction: parcelType.weight_restriction,
        special_handling_instructions: parcelType.special_handling_instructions,
      });
    } else {
      // Reset form for new parcel type
      setFormData({
        type_name: '',
        base_price: null,
        per_kg_rate: null,
        weight_restriction: null,
        special_handling_instructions: null,
      });
    }
  }, [parcelType]);

  // Handle input change
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    
    // Handle numeric fields
    if (
      name === 'base_price' ||
      name === 'per_kg_rate' ||
      name === 'weight_restriction'
    ) {
      setFormData({
        ...formData,
        [name]: value === '' ? null : parseFloat(value),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type_name) {
      toast({
        title: 'Error',
        description: 'Type name is required',
        variant: 'destructive',
      });
      return;
    }
    
    setLoading(true);
    
    try {
      const url = parcelType
        ? `/api/parceltypes/${parcelType.type_id}`
        : '/api/parceltypes';
      const method = parcelType ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save parcel type');
      }
      
      toast({
        title: 'Success',
        description: `Parcel type ${parcelType ? 'updated' : 'created'} successfully`,
      });
      
      onClose(true); // Close dialog and refresh list
    } catch (error: any) {
      console.error('Error saving parcel type:', error);
      toast({
        title: 'Error',
        description: `Failed to ${parcelType ? 'update' : 'create'} parcel type`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose(false)}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>
              {parcelType ? 'Edit Parcel Type' : 'Add New Parcel Type'}
            </DialogTitle>
            <DialogDescription>
              {parcelType
                ? 'Update the details of this parcel type'
                : 'Create a new parcel type with pricing details'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="type_name">Type Name *</Label>
              <Input
                id="type_name"
                name="type_name"
                value={formData.type_name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="base_price">Base Price (₹)</Label>
                <Input
                  id="base_price"
                  name="base_price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.base_price === null ? '' : formData.base_price}
                  onChange={handleChange}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="per_kg_rate">Per KG Rate (₹)</Label>
                <Input
                  id="per_kg_rate"
                  name="per_kg_rate"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.per_kg_rate === null ? '' : formData.per_kg_rate}
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="weight_restriction">Weight Restriction (kg)</Label>
              <Input
                id="weight_restriction"
                name="weight_restriction"
                type="number"
                step="0.1"
                min="0"
                value={
                  formData.weight_restriction === null
                    ? ''
                    : formData.weight_restriction
                }
                onChange={handleChange}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="special_handling_instructions">
                Special Handling Instructions
              </Label>
              <Textarea
                id="special_handling_instructions"
                name="special_handling_instructions"
                value={formData.special_handling_instructions || ''}
                onChange={handleChange}
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onClose(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {parcelType ? 'Update' : 'Create'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
