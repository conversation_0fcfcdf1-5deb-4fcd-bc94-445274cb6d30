# Supabase Integration for KPN Branch Management

This document provides information on how to use the Supabase integration in the KPN Branch Management application.

## Setup

The Supabase client is already configured with the following:

- URL: `https://nekjeqxlwhfwyekeinnc.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y`

These values are stored in the `.env.local` file and used by the Supabase client in `lib/supabase.ts`.

## File Structure

- `lib/supabase.ts` - Supabase client configuration and helper functions
- `lib/database.types.ts` - TypeScript types for the database tables
- `lib/db-helpers.ts` - Helper functions for common database operations
- `contexts/supabase-provider.tsx` - Supabase context provider for authentication
- `hooks/use-supabase-auth.ts` - Custom hook for Supabase authentication

## Usage

### Authentication

The application uses Supabase for authentication. The `useSupabaseAuth` hook provides the following functions:

```typescript
const { 
  user,          // Current user
  loading,       // Loading state
  signIn,        // Sign in with email and password
  signUp,        // Sign up with email and password
  signOut,       // Sign out
  resetPassword  // Reset password
} = useSupabaseAuth();
```

Example usage:

```typescript
const { signIn } = useSupabaseAuth();

async function handleSignIn(email: string, password: string) {
  const success = await signIn(email, password);
  if (success) {
    // Redirect or perform other actions
  }
}
```

### Database Operations

The `lib/db-helpers.ts` file provides helper functions for common database operations:

```typescript
// Parcels
const parcels = await getParcels();
const parcel = await getParcelById(id);
const newParcel = await createParcel(parcelData);
const updatedParcel = await updateParcel(id, updates);
const deleted = await deleteParcel(id);

// Branches
const branches = await getBranches();
const branch = await getBranchById(id);

// Expenses
const expenses = await getExpenses();
const newExpense = await createExpense(expenseData);
```

### Example Component

The `components/supabase-data-example.tsx` file provides an example of how to use Supabase to fetch and display data.

## Database Schema

The database schema is defined in `lib/database.types.ts` and includes the following tables:

- `parcels` - Parcel information
- `branches` - Branch information
- `expenses` - Expense information

## Next Steps

1. Create additional tables in Supabase as needed
2. Implement additional helper functions for database operations
3. Create components that use Supabase for data fetching and manipulation
4. Implement authentication flows for sign up, password reset, etc.

## Resources

- [Supabase Documentation](https://supabase.io/docs)
- [Supabase JavaScript Client](https://supabase.io/docs/reference/javascript/introduction)
- [Supabase Auth](https://supabase.io/docs/guides/auth)
