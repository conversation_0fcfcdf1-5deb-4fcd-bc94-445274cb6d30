"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PageLayout } from "@/components/page-layout"
import { DashboardShell } from "@/components/dashboard-shell"
import { Loader2, AlertCircle, CheckCircle } from "lucide-react"

export default function FixLoadingChartPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const runMigration = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/migrations/fix-loading-chart-trigger', {
        method: 'POST',
      })

      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || "Loading chart trigger fixed successfully"
        })
      } else {
        setResult({
          success: false,
          message: data.error || "Failed to fix loading chart trigger"
        })
      }
    } catch (error: any) {
      setResult({
        success: false,
        message: "An error occurred while fixing the loading chart trigger"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <PageLayout
      title="Fix Loading Chart Trigger"
      subtitle="Fix the issue with loading chart items"
    >
      <DashboardShell>
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Fix Loading Chart Trigger</CardTitle>
            <CardDescription>
              This will fix the issue with the loading chart items where the column "id" does not exist.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              The loading chart trigger is currently using the column "id" in the users table, but the correct column name is "user_id". 
              This migration will update the trigger function to use the correct column name.
            </p>

            {result && (
              <Alert variant={result.success ? "default" : "destructive"} className="mb-4">
                {result.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
                <AlertDescription>{result.message}</AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            <Button 
              onClick={runMigration} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Fixing...
                </>
              ) : (
                "Fix Loading Chart Trigger"
              )}
            </Button>
          </CardFooter>
        </Card>
      </DashboardShell>
    </PageLayout>
  )
}
