"use client"

import { usePathname, useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Menu, Package, Truck, IndianRupee, FileText } from 'lucide-react'
import { NavigationLink } from '@/components/navigation-link'

const mainItems = [
  {
    title: "Dashboard",
    href: "/",
    icon: Package,
  },
  {
    title: "Parcels",
    href: "/parcels",
    icon: Package,
  },
  {
    title: "Load & Receive",
    href: "/load-receive",
    icon: Truck,
  },
  {
    title: "Memos",
    href: "/memos",
    icon: FileText,
  },
  {
    title: "Accounts",
    href: "/operations",
    icon: IndianRupee,
  }
]

const adminItems = [
  {
    title: "Admin",
    href: "/admin",
    icon: FileText,
  }
]

export function MainNav({ onMobileNavToggle }: { onMobileNavToggle: () => void }) {
  const pathname = usePathname()
  const [userRole, setUserRole] = useState<string | null>(null)

  // Fetch user role on component mount
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const response = await fetch('/api/auth/user-role')
        if (response.ok) {
          const data = await response.json()
          setUserRole(data.role)
        }
      } catch (error) {
        console.error('Error fetching user role:', error)
      }
    }

    fetchUserRole()
  }, [])

  const isAdmin = userRole === 'Admin' || userRole === 'Super Admin'

  return (
    <div className="flex gap-6 md:gap-10">
      <Button
        variant="ghost"
        className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden text-primary"
        onClick={onMobileNavToggle}
      >
        <Menu className="h-6 w-6" />
        <span className="sr-only">Toggle Menu</span>
      </Button>
      <NavigationLink href="/" className="hidden items-center space-x-2 md:flex text-primary">
        <Package className="h-6 w-6" />
        <span className="hidden font-bold sm:inline-block">
          Branch
        </span>
      </NavigationLink>
      <nav className="hidden gap-6 md:flex">
        {mainItems.map((item) => {
          const Icon = item.icon
          return (
            <NavigationLink
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center text-lg font-medium transition-colors sm:text-sm",
                pathname === item.href
                  ? "text-primary font-semibold"
                  : "text-primary/70 hover:text-primary"
              )}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.title}
            </NavigationLink>
          )
        })}

        {/* Only show admin items for admin users */}
        {isAdmin && adminItems.map((item) => {
          const Icon = item.icon
          return (
            <NavigationLink
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center text-lg font-medium transition-colors sm:text-sm",
                pathname === item.href
                  ? "text-primary font-semibold"
                  : "text-primary/70 hover:text-primary"
              )}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.title}
            </NavigationLink>
          )
        })}
      </nav>
    </div>
  )
}
