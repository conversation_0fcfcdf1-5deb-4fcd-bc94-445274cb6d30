# Login Page with Hidden Signup Feature

This document provides information on how to use the login page with the hidden signup feature.

## Features

- **Login Form**: Allows users to sign in with their email and password
- **Signup Form**: Hidden by default, can be enabled if needed
- **Password Reset**: Link to reset password

## How to Show the Signup Feature

The signup feature is hidden by default. It can be temporarily enabled by adding a query parameter to the login page URL:

```
/login?showSignup=true
```

When this parameter is present, the signup tab and form will be visible, allowing users to create new accounts.

## Implementation Details

The login page uses Supabase for authentication. The signup feature is implemented as a toggle that is disabled by default.

### Files

- `app/login/page.tsx` - The login page component
- `hooks/use-supabase-auth.ts` - Custom hook for Supabase authentication
- `contexts/supabase-provider.tsx` - Supabase context provider

### How It Works

1. The login page has the signup feature disabled by default
2. The login page checks for the `showSignup` query parameter on load
3. If `showSignup=true`, the signup feature is temporarily enabled
4. Users can toggle between login and signup forms (if signup is enabled)
5. On successful login, users are redirected to the dashboard
6. On successful signup, users are shown a success message and prompted to check their email

## Usage

### For Normal Operation

For normal operation, simply access the login page without any query parameters:

```
/login
```

Only the sign-in form will be visible.

### For User Creation

If you need to create new users, temporarily enable the signup feature by adding the query parameter:

```
/login?showSignup=true
```

This will show both the sign-in and sign-up tabs, allowing you to create new accounts.
