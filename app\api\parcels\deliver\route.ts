import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// POST /api/parcels/deliver
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.parcel_id || !body.recipient_name) {
      return NextResponse.json(
        { error: "Missing required fields: parcel_id, recipient_name" },
        { status: 400 }
      );
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id, name")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Call the delivery function
    const { data: deliveryResult, error: deliveryError } = await supabase
      .rpc("process_parcel_delivery", {
        p_parcel_id: body.parcel_id,
        p_delivery_branch_id: userData.branch_id,
        p_recipient_name: body.recipient_name,
        p_recipient_id_type: body.recipient_id_type || null,
        p_recipient_id_number: body.recipient_id_number || null,
        p_delivery_notes: body.delivery_notes || null,
        p_proof_url: body.proof_url || null,
        p_delivered_by: user?.id
      });

    if (deliveryError) {
      console.error("Error calling delivery function:", deliveryError);
      return NextResponse.json({ error: "Failed to process delivery" }, { status: 500 });
    }

    // Parse the JSON result from the function
    const result = deliveryResult;

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({
      message: result.message,
      parcel_id: result.parcel_id,
      lr_number: result.lr_number,
      delivered_to: result.delivered_to,
      delivery_timestamp: result.delivery_timestamp,
      delivered_by: userData.name
    });

  } catch (error: any) {
    console.error("Error in POST /api/parcels/deliver:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
