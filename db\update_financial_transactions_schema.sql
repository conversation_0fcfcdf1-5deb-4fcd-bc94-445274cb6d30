-- SQL script to update the financial_transactions table schema

-- Check if transaction_date column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'transaction_date'
    ) THEN
        ALTER TABLE financial_transactions ADD COLUMN transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- Check if payment_method column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'payment_method'
    ) THEN
        ALTER TABLE financial_transactions ADD COLUMN payment_method TEXT;

        -- If payment_mode exists, copy data from payment_mode to payment_method
        IF EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'financial_transactions'
            AND column_name = 'payment_mode'
        ) THEN
            UPDATE financial_transactions SET payment_method = payment_mode;
        END IF;
    END IF;
END $$;

-- Check if status column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'status'
    ) THEN
        ALTER TABLE financial_transactions ADD COLUMN status TEXT DEFAULT 'Pending';

        -- If approval_status exists, copy data from approval_status to status
        IF EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'financial_transactions'
            AND column_name = 'approval_status'
        ) THEN
            UPDATE financial_transactions SET status = approval_status;
        END IF;
    END IF;
END $$;

-- Check if description column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'description'
    ) THEN
        ALTER TABLE financial_transactions ADD COLUMN description TEXT;
    END IF;
END $$;

-- Check if attachment_url column exists, if not create it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'attachment_url'
    ) THEN
        ALTER TABLE financial_transactions ADD COLUMN attachment_url TEXT;
    END IF;
END $$;

-- Update existing data to ensure transaction_date is set
UPDATE financial_transactions
SET transaction_date = COALESCE(
    transaction_date,
    NOW() -- Default to current time if no date exists
)
WHERE transaction_date IS NULL;

-- Create an API route to run this script
-- POST /api/migrations/update-financial-transactions-schema
