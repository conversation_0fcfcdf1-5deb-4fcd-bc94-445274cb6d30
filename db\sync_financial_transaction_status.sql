-- SQL script to sync approval_status to status in financial_transactions table

-- First check if both columns exist
DO $outer$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'status'
    ) AND EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'financial_transactions'
        AND column_name = 'approval_status'
    ) THEN
        -- Create a function to sync approval_status to status
        CREATE OR REPLACE FUNCTION sync_approval_status_to_status()
        RETURNS TRIGGER AS $func$
        BEGIN
          NEW.status = NEW.approval_status;
          RETURN NEW;
        END;
        $func$ LANGUAGE plpgsql;

        -- Create a trigger to automatically sync the columns
        DROP TRIGGER IF EXISTS sync_status_trigger ON financial_transactions;
        CREATE TRIGGER sync_status_trigger
        BEFORE INSERT OR UPDATE ON financial_transactions
        FOR EACH ROW
        EXECUTE FUNCTION sync_approval_status_to_status();

        -- Update existing records to sync the columns
        UPDATE financial_transactions
        SET status = approval_status
        WHERE status IS NULL OR status != approval_status;

        RAISE NOTICE 'Created trigger to sync approval_status to status in financial_transactions table';
    ELSE
        RAISE NOTICE 'One or both columns (status, approval_status) do not exist in financial_transactions table';
    END IF;
END $outer$;
