import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";
import fs from "fs";
import path from "path";

// POST /api/migrations/sync-transaction-status
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user role to check if they're an admin
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("auth_id", session.user.id)
      .single();

    if (userError) {
      console.error("Error fetching user data:", userError);
      return NextResponse.json({ error: "Failed to fetch user data" }, {
        status: 500,
      });
    }

    if (userData.role !== "Super Admin" && userData.role !== "Admin") {
      return NextResponse.json({
        error: "Unauthorized. Admin access required.",
      }, { status: 403 });
    }

    // Read the SQL file
    const sqlFilePath = path.join(
      process.cwd(),
      "db",
      "sync_financial_transaction_status.sql",
    );
    const sqlContent = fs.readFileSync(sqlFilePath, "utf8");

    // Execute the SQL
    const { error: migrationError } = await supabase.rpc("exec_sql", {
      sql: sqlContent,
    });

    if (migrationError) {
      console.error("Error running schema update script:", migrationError);
      return NextResponse.json({ error: "Failed to execute migration" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      success: true,
      message:
        "Successfully synced approval_status to status in financial_transactions table",
    });
  } catch (error: any) {
    console.error("Error in migration:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
