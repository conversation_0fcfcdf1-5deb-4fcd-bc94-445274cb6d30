"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { CheckCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useUser } from "@/hooks/use-user"
import { useSupabase } from "@/contexts/supabase-provider"

const formSchema = z.object({
  category: z.string().min(1, "Please select an expense category"),
  amount: z.string().min(1, "Amount is required"),
  date: z.string().min(1, "Date is required"),
  description: z.string().min(5, "Description must be at least 5 characters"),
  vendorName: z.string().min(2, "Vendor name must be at least 2 characters").optional(),
  invoiceNumber: z.string().min(1, "Invoice number is required").optional(),
  paymentMethod: z.string().min(1, "Please select a payment method"),
  memoNumber: z.string().optional(), // Field for BATA expenses
  branch_id: z.number().optional(), // Will be set automatically from user's branch
})

const expenseCategories = [
  { value: "fuel", label: "Fuel" },
  { value: "maintenance", label: "Vehicle Maintenance" },
  { value: "supplies", label: "Office Supplies" },
  { value: "utilities", label: "Utilities" },
  { value: "bata", label: "BATA (Board and Travel Allowance)" },
  { value: "equipment", label: "Equipment" },
  { value: "repairs", label: "Repairs" },
  { value: "other", label: "Other" },
]

const paymentMethods = [
  { value: "cash", label: "Cash" },
  { value: "corporate_card", label: "Corporate Card" },
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "upi", label: "UPI Payment" },
]

export function NewExpense() {
  const { toast } = useToast()
  const { user, userDetails } = useUser()
  const { supabase } = useSupabase()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showMemoField, setShowMemoField] = useState(false)
  const [memoData, setMemoData] = useState<any>(null)
  const [isVerifyingMemo, setIsVerifyingMemo] = useState(false)

  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [submittedExpense, setSubmittedExpense] = useState<any>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: "",
      amount: "",
      date: new Date().toISOString().split('T')[0],
      description: "",
      vendorName: "",
      invoiceNumber: "",
      paymentMethod: "",
      memoNumber: "",
      branch_id: 0,
    },
  })

  // Set branch_id from user details when available
  useEffect(() => {
    if (userDetails?.branch_id) {
      form.setValue("branch_id", userDetails.branch_id)
    }
  }, [userDetails, form])

  // Function to verify memo and get BATA amount
  const verifyMemoAndGetBata = async (memoNumber: string) => {
    if (!memoNumber) {
      toast({
        title: "Missing Memo Number",
        description: "Please enter a memo number",
        variant: "destructive",
      })
      return false
    }

    setIsVerifyingMemo(true)

    try {
      const response = await fetch('/api/expenses/verify-memo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ memo_number: memoNumber }),
      })

      const data = await response.json()

      if (!response.ok) {
        toast({
          title: "Verification Failed",
          description: data.error || "Failed to verify memo",
          variant: "destructive",
        })
        setIsVerifyingMemo(false)
        return false
      }

      // Store memo data for later use
      setMemoData(data.memo)

      // Set form values based on memo data
      const totalAmount = (parseFloat(data.memo.bata_amount) || 0) +
                          (parseFloat(data.memo.salary_amount) || 0)

      form.setValue("amount", totalAmount.toString())
      form.setValue("description", `BATA and salary payment for memo ${memoNumber}`)
      form.setValue("vendorName", "Internal - Drivers")
      form.setValue("invoiceNumber", `MEMO-${memoNumber}`)

      toast({
        title: "Memo Verified",
        description: `Amount of ₹${totalAmount.toFixed(2)} fetched from memo ${memoNumber}`,
      })

      setIsVerifyingMemo(false)
      return true
    } catch (error: any) {
      console.error("Error verifying memo:", error)
      toast({
        title: "Verification Error",
        description: "An error occurred while verifying the memo",
        variant: "destructive",
      })
      setIsVerifyingMemo(false)
      return false
    }
  }

  // Watch for category changes
  useEffect(() => {
    const category = form.watch("category")
    setShowMemoField(category === "bata")

    if (category !== "bata") {
      form.setValue("memoNumber", "")
      setMemoData(null)
    }
  }, [form.watch("category")])

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      const file = files[0]
      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Please upload a file smaller than 5MB",
          variant: "destructive",
        })
        return
      }
      setReceiptFile(file)
    }
  }

  // Upload file to Supabase Storage
  const uploadReceipt = async (expenseId: number): Promise<string | null> => {
    if (!receiptFile) return null

    setIsUploading(true)
    setUploadProgress(0)

    try {
      const fileExt = receiptFile.name.split('.').pop()
      const fileName = `${expenseId}_${Date.now()}.${fileExt}`
      const filePath = `expenses/${fileName}`

      // Upload file to billsandreceipts bucket
      const { data, error } = await supabase.storage
        .from('billsandreceipts')
        .upload(filePath, receiptFile, {
          cacheControl: '3600',
          upsert: false,
          onUploadProgress: (progress) => {
            const percent = Math.round((progress.loaded / progress.total) * 100)
            setUploadProgress(percent)
          }
        })

      if (error) {
        throw error
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('billsandreceipts')
        .getPublicUrl(filePath)

      return urlData.publicUrl
    } catch (error: any) {
      console.error("Error uploading receipt:", error)
      toast({
        title: "Upload Error",
        description: "Failed to upload receipt. The expense was submitted without a receipt.",
        variant: "destructive",
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)

    try {
      // Prepare data for API
      const expenseData = {
        category: values.category,
        amount: parseFloat(values.amount),
        branch_id: values.branch_id || userDetails?.branch_id,
        description: values.description,
        vendor_name: values.vendorName,
        invoice_number: values.invoiceNumber,
        payment_method: values.paymentMethod,
        memo_number: values.memoNumber || null,
        submitted_at: new Date().toISOString()
      }

      // Submit to API
      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(expenseData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to submit expense")
      }

      // If we have a receipt file, upload it and update the expense
      if (receiptFile && data.expense?.id) {
        const receiptUrl = await uploadReceipt(data.expense.id)

        if (receiptUrl) {
          // Update the expense with the receipt URL
          const updateResponse = await fetch(`/api/expenses/${data.expense.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              receipt_url: receiptUrl
            }),
          })

          if (!updateResponse.ok) {
            console.error("Failed to update expense with receipt URL")
          }
        }
      }

      // Store the submitted expense for the success dialog
      setSubmittedExpense(data.expense)

      // Show success dialog instead of toast
      setShowSuccessDialog(true)

      // Reset form with default values
      form.reset({
        category: "",
        amount: "",
        date: new Date().toISOString().split('T')[0],
        description: "",
        vendorName: "",
        invoiceNumber: "",
        paymentMethod: "",
        memoNumber: "",
        branch_id: userDetails?.branch_id || 0,
      })

      // Reset other state
      setMemoData(null)
      setReceiptFile(null)

      // Reset file input
      const fileInput = document.getElementById('receipt-upload') as HTMLInputElement
      if (fileInput) fileInput.value = ''
    } catch (error: any) {
      console.error("Error submitting expense:", error)
      toast({
        title: "Submission Error",
        description: error instanceof Error ? error.message : "An error occurred while submitting the expense",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>New Expense</CardTitle>
              <CardDescription>Submit a new expense for approval</CardDescription>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expense Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {expenseCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {showMemoField && (
                <FormField
                  control={form.control}
                  name="memoNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Memo Number</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input
                            placeholder="Enter memo number"
                            {...field}
                          />
                        </FormControl>
                        <Button
                          type="button"
                          onClick={async () => {
                            if (field.value) {
                              await verifyMemoAndGetBata(field.value)
                            }
                          }}
                          disabled={isVerifyingMemo}
                        >
                          {isVerifyingMemo ? "Verifying..." : "Verify"}
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount (₹)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0.00"
                        {...field}
                        disabled={showMemoField} // Disable amount field for BATA expenses
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="vendorName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vendor Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter vendor name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Number</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter invoice number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide details about the expense..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="rounded-lg border border-dashed p-4">
              <FormLabel className="block mb-2">Attach Receipt</FormLabel>
              <Input
                id="receipt-upload"
                type="file"
                accept="image/*,.pdf"
                onChange={handleFileChange}
                disabled={isSubmitting || isUploading}
              />
              {receiptFile && (
                <p className="text-sm text-muted-foreground mt-2">
                  Selected file: {receiptFile.name} ({(receiptFile.size / 1024).toFixed(1)} KB)
                </p>
              )}
              {isUploading && (
                <div className="mt-2">
                  <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-center mt-1">{uploadProgress}% uploaded</p>
                </div>
              )}
              <FormDescription className="mt-1">
                Upload receipt image or PDF (max 5MB)
              </FormDescription>
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : "Submit Expense"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>

    {/* Success Dialog */}
    <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-500" />
            Expense Submitted Successfully
          </DialogTitle>
          <DialogDescription>
            Your expense has been submitted for approval.
          </DialogDescription>
        </DialogHeader>

        {submittedExpense && (
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium">Expense ID</p>
                <p className="text-muted-foreground">#{submittedExpense.id}</p>
              </div>
              <div>
                <p className="font-medium">Amount</p>
                <p className="text-muted-foreground">₹{parseFloat(submittedExpense.amount).toLocaleString()}</p>
              </div>
              <div>
                <p className="font-medium">Category</p>
                <p className="text-muted-foreground">
                  {expenseCategories.find(c => c.value === submittedExpense.category)?.label || submittedExpense.category}
                </p>
              </div>
              <div>
                <p className="font-medium">Status</p>
                <p className="text-muted-foreground">Pending Approval</p>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button onClick={() => setShowSuccessDialog(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  )
}
