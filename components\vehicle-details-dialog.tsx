"use client"

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogDescription, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Download } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface VehicleDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  vehicle: {
    id: string
    type: string
    route: string
    driver: {
      name: string
      phone: string
    }
    status: string
    scheduledArrival: string
    scheduledDeparture: string
    actualArrival?: string
    actualDeparture?: string
    parcels: Array<{
      lrn: string
      status: string
      type: string
      itemCount: number
    }>
    nextStop: string
    capacity: {
      total: string
      used: string
      available: string
    }
  }
}

const statusColors = {
  arrived: "bg-green-500",
  in_transit: "bg-yellow-500",
  departed: "bg-blue-500"
}

const statusLabels = {
  arrived: "Arrived",
  in_transit: "In Transit",
  departed: "Departed"
}

export function VehicleDetailsDialog({ open, onOpenChange, vehicle }: VehicleDetailsDialogProps) {
  const { toast } = useToast()

  const handleExportPDF = () => {
    // Create a new window for PDF content
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Generate the PDF content with all details
    const content = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Vehicle Details - ${vehicle.id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
              max-width: 800px;
              margin: 0 auto;
            }
            .header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
            }
            .status-badge {
              padding: 4px 8px;
              border-radius: 4px;
              color: white;
              font-size: 14px;
              ${vehicle.status === 'arrived' ? 'background: #22c55e;' : 
                vehicle.status === 'in_transit' ? 'background: #eab308;' : 
                'background: #3b82f6;'}
            }
            .section {
              margin-bottom: 24px;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 12px;
            }
            .grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 16px;
              margin-bottom: 16px;
            }
            .info-item {
              margin-bottom: 8px;
            }
            .label {
              color: #666;
              font-size: 14px;
            }
            .value {
              font-weight: 500;
            }
            .separator {
              border-top: 1px solid #e5e7eb;
              margin: 16px 0;
            }
            .parcel-item {
              border: 1px solid #e5e7eb;
              padding: 12px;
              border-radius: 8px;
              margin-bottom: 12px;
            }
            @media print {
              body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Vehicle Details - ${vehicle.id}</h1>
            <span class="status-badge">
              ${statusLabels[vehicle.status as keyof typeof statusLabels]}
            </span>
          </div>

          <div class="section">
            <p>${vehicle.route}</p>
          </div>

          <div class="section">
            <div class="section-title">Driver Information</div>
            <div class="info-item">
              <div class="label">Name</div>
              <div class="value">${vehicle.driver.name}</div>
            </div>
            <div class="info-item">
              <div class="label">Phone</div>
              <div class="value">${vehicle.driver.phone}</div>
            </div>
          </div>

          <div class="separator"></div>

          <div class="section">
            <div class="section-title">Schedule</div>
            <div class="grid">
              <div class="info-item">
                <div class="label">Scheduled Arrival</div>
                <div class="value">${vehicle.scheduledArrival}</div>
              </div>
              <div class="info-item">
                <div class="label">Scheduled Departure</div>
                <div class="value">${vehicle.scheduledDeparture}</div>
              </div>
              ${vehicle.actualArrival ? `
                <div class="info-item">
                  <div class="label">Actual Arrival</div>
                  <div class="value">${vehicle.actualArrival}</div>
                </div>
              ` : ''}
              ${vehicle.actualDeparture ? `
                <div class="info-item">
                  <div class="label">Actual Departure</div>
                  <div class="value">${vehicle.actualDeparture}</div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="separator"></div>

          <div class="section">
            <div class="section-title">Capacity</div>
            <div class="grid">
              <div class="info-item">
                <div class="label">Total</div>
                <div class="value">${vehicle.capacity.total}</div>
              </div>
              <div class="info-item">
                <div class="label">Used</div>
                <div class="value">${vehicle.capacity.used}</div>
              </div>
              <div class="info-item">
                <div class="label">Available</div>
                <div class="value">${vehicle.capacity.available}</div>
              </div>
            </div>
          </div>

          <div class="separator"></div>

          <div class="section">
            <div class="section-title">Parcels</div>
            ${vehicle.parcels.map(parcel => `
              <div class="parcel-item">
                <div class="info-item">
                  <div class="label">LR Number</div>
                  <div class="value">${parcel.lrn}</div>
                </div>
                <div class="info-item">
                  <div class="label">Status</div>
                  <div class="value">${parcel.status === "completed" ? "Completed" : "Pending"}</div>
                </div>
                <div class="info-item">
                  <div class="label">Type</div>
                  <div class="value">${parcel.type === "load" ? "To be loaded" : "To be received"}</div>
                </div>
                <div class="info-item">
                  <div class="label">Item Count</div>
                  <div class="value">${parcel.itemCount} items</div>
                </div>
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(content);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      // Close the window after printing (optional)
      printWindow.onafterprint = () => {
        printWindow.close();
      };
    };

    toast({
      title: "PDF Export Initiated",
      description: `Vehicle ${vehicle.id} details are being prepared for export.`
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl dialog-content">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-4">
              <span>Vehicle Details - {vehicle.id}</span>
              <Badge className={statusColors[vehicle.status as keyof typeof statusColors]}>
                {statusLabels[vehicle.status as keyof typeof statusLabels]}
              </Badge>
            </DialogTitle>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExportPDF}
              className="no-print"
            >
              <Download className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
          </div>
          <DialogDescription>{vehicle.route}</DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[80vh]">
          <div className="space-y-6 p-4">
            <div>
              <h3 className="text-lg font-semibold">Vehicle Information</h3>
              <div className="mt-4 grid gap-4 md:grid-cols-2">
                <div>
                  <p className="font-medium">Type</p>
                  <p className="text-muted-foreground">{vehicle.type}</p>
                </div>
                <div>
                  <p className="font-medium">Next Stop</p>
                  <p className="text-muted-foreground">{vehicle.nextStop}</p>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Driver Information</h3>
              <div className="mt-2 space-y-2">
                <p><span className="font-medium">Name:</span> {vehicle.driver.name}</p>
                <p><span className="font-medium">Phone:</span> {vehicle.driver.phone}</p>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Schedule</h3>
              <div className="mt-4 space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <p className="font-medium">Scheduled Arrival</p>
                    <p className="text-muted-foreground">
                      {new Date(vehicle.scheduledArrival).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Scheduled Departure</p>
                    <p className="text-muted-foreground">
                      {new Date(vehicle.scheduledDeparture).toLocaleString()}
                    </p>
                  </div>
                </div>
                {vehicle.actualArrival && (
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <p className="font-medium">Actual Arrival</p>
                      <p className="text-muted-foreground">
                        {new Date(vehicle.actualArrival).toLocaleString()}
                      </p>
                    </div>
                    {vehicle.actualDeparture && (
                      <div>
                        <p className="font-medium">Actual Departure</p>
                        <p className="text-muted-foreground">
                          {new Date(vehicle.actualDeparture).toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Capacity</h3>
              <div className="mt-4 grid gap-4 md:grid-cols-3">
                <div>
                  <p className="font-medium">Total</p>
                  <p className="text-muted-foreground">{vehicle.capacity.total}</p>
                </div>
                <div>
                  <p className="font-medium">Used</p>
                  <p className="text-muted-foreground">{vehicle.capacity.used}</p>
                </div>
                <div>
                  <p className="font-medium">Available</p>
                  <p className="text-muted-foreground">{vehicle.capacity.available}</p>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-lg font-semibold">Parcels</h3>
              <div className="mt-4 space-y-4">
                {vehicle.parcels.map((parcel) => (
                  <div key={parcel.lrn} className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <p className="font-medium">LR: {parcel.lrn}</p>
                      <p className="text-sm text-muted-foreground">
                        {parcel.type === "load" ? "To be loaded" : "To be received"} • {parcel.itemCount} items
                      </p>
                    </div>
                    <Badge variant={parcel.status === "completed" ? "default" : "secondary"}>
                      {parcel.status === "completed" ? "Completed" : "Pending"}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}
