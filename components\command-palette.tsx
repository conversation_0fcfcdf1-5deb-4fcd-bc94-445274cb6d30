"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Command } from "cmdk"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { 
  Package, 
  Truck, 
  FileText, 
  Activity, 
  BarChart3, 
  Users, 
  Settings, 
  IndianRupee,
  AlertTriangle,
  Search
} from "lucide-react"

const searchItems = [
  {
    heading: "Main Navigation",
    items: [
      {
        title: "Dashboard",
        href: "/",
        icon: Package,
        keywords: "home overview summary stats",
      },
      {
        title: "Parcels",
        href: "/parcels",
        icon: Package,
        keywords: "shipments packages delivery tracking",
      },
      {
        title: "Vehicles",
        href: "/vehicles",
        icon: Truck,
        keywords: "transport trucks fleet logistics",
      },
      {
        title: "COD",
        href: "/cod",
        icon: IndianRupee,
        keywords: "cash on delivery payments collection money",
      },
      {
        title: "Overdue",
        href: "/overdue",
        icon: <PERSON><PERSON><PERSON>rian<PERSON>,
        keywords: "delayed late pending urgent attention",
      },
    ],
  },
  {
    heading: "Accounts",
    items: [
      {
        title: "Expenses",
        href: "/expenses",
        icon: FileText,
        keywords: "costs bills payments finance accounting",
      },
      {
        title: "Reports",
        href: "/reports",
        icon: BarChart3,
        keywords: "analytics statistics data insights",
      },
      {
        title: "Activity Log",
        href: "/activity",
        icon: Activity,
        keywords: "history audit trail events tracking",
      },
    ],
  },
  {
    heading: "Administration",
    items: [
      {
        title: "Users",
        href: "/users",
        icon: Users,
        keywords: "staff employees accounts permissions",
      },
      {
        title: "Settings",
        href: "/settings",
        icon: Settings,
        keywords: "configuration preferences system branch",
      },
    ],
  },
]

export function CommandPalette() {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "j" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const handleSelect = (href: string) => {
    setOpen(false)
    router.push(href)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="overflow-hidden p-0">
        <DialogTitle className="sr-only">Search</DialogTitle>
        <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          <div className="flex items-center border-b px-3" cmdk-input-wrapper="">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Command.Input
              value={search}
              onValueChange={setSearch}
              placeholder="Search features and pages..."
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
            <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100 ml-auto">
              <span className="text-xs">⌘</span>J
            </kbd>
          </div>
          <Command.List className="max-h-[300px] overflow-y-auto overflow-x-hidden">
            <Command.Empty className="py-6 text-center text-sm">
              No results found.
            </Command.Empty>
            {searchItems.map((group) => (
              <Command.Group key={group.heading} heading={group.heading}>
                {group.items.map((item) => {
                  const Icon = item.icon
                  return (
                    <Command.Item
                      key={item.href}
                      value={`${item.title} ${item.keywords}`}
                      onSelect={() => handleSelect(item.href)}
                      className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground"
                    >
                      <Icon className="mr-2 h-4 w-4" />
                      <span>{item.title}</span>
                    </Command.Item>
                  )
                })}
              </Command.Group>
            ))}
          </Command.List>
        </Command>
      </DialogContent>
    </Dialog>
  )
}
