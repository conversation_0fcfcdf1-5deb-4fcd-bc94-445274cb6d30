import { NextRequest, NextResponse } from "next/server";
import {
  createParcel,
  deleteParcel,
  getParcels,
  updateParcel,
} from "@/lib/db-helpers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filters: Record<string, any> = {};

    // Extract filter parameters
    if (searchParams.has("sender_name")) {
      filters.sender_name = searchParams.get("sender_name");
    }

    if (searchParams.has("recipient_name")) {
      filters.recipient_name = searchParams.get("recipient_name");
    }

    if (searchParams.has("lr_number")) {
      filters.lr_number = searchParams.get("lr_number");
    }

    if (searchParams.has("current_status")) {
      filters.current_status = searchParams.get("current_status");
    }

    if (searchParams.has("sender_branch_id")) {
      filters.sender_branch_id = parseInt(
        searchParams.get("sender_branch_id") || "0",
      );
    }

    if (searchParams.has("delivery_branch_id")) {
      filters.delivery_branch_id = parseInt(
        searchParams.get("delivery_branch_id") || "0",
      );
    }

    // Extract pagination parameters
    const page = searchParams.has("page")
      ? parseInt(searchParams.get("page") || "1")
      : 1;

    const pageSize = searchParams.has("pageSize")
      ? parseInt(searchParams.get("pageSize") || "20")
      : 20;

    // Get parcels with filters and pagination
    const result = await getParcels(filters, page, pageSize);

    if (!result.parcels) {
      return NextResponse.json({ error: "Failed to fetch parcels" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      parcels: result.parcels,
      pagination: {
        total: result.count,
        page,
        pageSize,
        totalPages: Math.ceil(result.count / pageSize),
      },
    });
  } catch (error: any) {
    console.error("Error in GET /api/parcels:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// POST /api/parcels
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.lr_number || !body.sender_name || !body.recipient_name) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: lr_number, sender_name, recipient_name",
        },
        { status: 400 },
      );
    }

    // Create parcel
    const parcel = await createParcel(body);

    if (!parcel) {
      return NextResponse.json({ error: "Failed to create parcel" }, {
        status: 500,
      });
    }

    return NextResponse.json(parcel, { status: 201 });
  } catch (error: any) {
    console.error("Error in POST /api/parcels:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
