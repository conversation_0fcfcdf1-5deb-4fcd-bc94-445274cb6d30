<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KPN Branch Management</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Helvetica, Arial, sans-serif;
        background-color: #f9f9f9;
        color: #1e293b;
        line-height: 1.5;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
        padding: 1rem;
        text-align: center;
      }

      .container {
        max-width: 600px;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        padding: 2rem;
      }

      h1 {
        color: #003a8c;
        margin-bottom: 1rem;
      }

      p {
        margin-bottom: 2rem;
        color: #64748b;
      }

      .buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
      }

      .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        cursor: pointer;
        transition: background-color 0.2s, color 0.2s;
        border: none;
        text-decoration: none;
      }

      .button-primary {
        background-color: #003a8c;
        color: white;
      }

      .button-primary:hover {
        background-color: #002a66;
      }

      .button-secondary {
        background-color: #f1f5f9;
        color: #1e293b;
      }

      .button-secondary:hover {
        background-color: #e2e8f0;
      }

      .logo {
        font-size: 3rem;
        font-weight: bold;
        color: #003a8c;
        margin-bottom: 1rem;
      }

      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-top: 4px solid #003a8c;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 2rem;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">KPN</div>
      <div class="spinner"></div>
      <h1>Branch Management System</h1>
      <p>Redirecting to login page...</p>
      <div class="buttons">
        <a href="/direct-login.html" class="button button-primary"
          >Go to Login</a
        >
      </div>
    </div>

    <script>
      // Redirect to direct login page after a short delay
      setTimeout(() => {
        window.location.href = "/direct-login.html";
      }, 1000);
    </script>
  </body>
</html>
