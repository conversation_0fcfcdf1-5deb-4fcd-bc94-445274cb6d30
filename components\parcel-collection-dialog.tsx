"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import SignatureCanvas from 'react-signature-canvas'

const collectionFormSchema = z.object({
  collectorName: z.string().min(2, "Name must be at least 2 characters"),
  collectorPhone: z.string().min(10, "Phone number must be at least 10 characters"),
  codAmount: z.string().optional(),
  acknowledgment: z.boolean().refine((val) => val === true, {
    message: "You must acknowledge receipt of the parcel",
  }),
})

interface ParcelCollectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  parcel: {
    lrn: string
    paymentMode: string
    codAmount?: string
  }
  onCollected: (data: z.infer<typeof collectionFormSchema>) => void
}

export function ParcelCollectionDialog({
  open,
  onOpenChange,
  parcel,
  onCollected,
}: ParcelCollectionDialogProps) {
  const { toast } = useToast()
  const [signature, setSignature] = useState<SignatureCanvas | null>(null)
  
  const form = useForm<z.infer<typeof collectionFormSchema>>({
    resolver: zodResolver(collectionFormSchema),
    defaultValues: {
      collectorName: "",
      collectorPhone: "",
      codAmount: parcel.codAmount,
      acknowledgment: false,
    },
  })

  const clearSignature = () => {
    if (signature) {
      signature.clear()
    }
  }

  function onSubmit(values: z.infer<typeof collectionFormSchema>) {
    if (!signature?.isEmpty()) {
      // In a real app, you would convert the signature to an image and upload it
      onCollected(values)
      onOpenChange(false)
      form.reset()
      clearSignature()
    } else {
      toast({
        title: "Signature Required",
        description: "Please provide a signature to complete collection",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Mark Parcel as Collected</DialogTitle>
          <DialogDescription>
            Record collection details for parcel {parcel.lrn}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="collectorName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Collector's Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter collector's name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="collectorPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Collector's Phone</FormLabel>
                  <FormControl>
                    <Input placeholder="+91 XXXXX XXXXX" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {parcel.paymentMode === "COD" && (
              <FormField
                control={form.control}
                name="codAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>COD Amount (₹)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter COD amount"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Expected amount: ₹{parcel.codAmount}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="space-y-2">
              <FormLabel>Signature</FormLabel>
              <div className="rounded-md border">
                <SignatureCanvas
                  ref={(ref) => setSignature(ref)}
                  canvasProps={{
                    className: "w-full h-[150px]",
                  }}
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={clearSignature}
              >
                Clear Signature
              </Button>
            </div>

            <FormField
              control={form.control}
              name="acknowledgment"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      I acknowledge receipt of the parcel
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit">Confirm Collection</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}