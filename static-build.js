/**
 * Static build script for Next.js
 * This script is used during the build process to handle static generation
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
require("dotenv").config({ path: ".env.local" });

console.log("Starting static build process...");
console.log("Loaded environment variables from .env.local");

// Ensure Supabase environment variables are set
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_URL =
    "https://nekjeqxlwhfwyekeinnc.supabase.co";
  console.log("Set default NEXT_PUBLIC_SUPABASE_URL");
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
  console.log("Set default NEXT_PUBLIC_SUPABASE_ANON_KEY");
}

// Create a minimal next.config.js that will work for the build
const minimalConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: { unoptimized: true },
  webpack: (config, { dev, isServer }) => {
    // Disable minification to avoid build errors
    if (config.optimization) {
      config.optimization.minimize = false;
    }

    // Reduce bundle size by ignoring certain modules in server build
    if (isServer) {
      config.externals = [...(config.externals || []), 'canvas', 'jsdom'];
    }

    return config;
  },
  // Transpile specific problematic modules
  transpilePackages: [
    '@radix-ui',
    'lucide-react',
    'cmdk',
    'react-day-picker',
    'embla-carousel-react',
    'vaul',
    'class-variance-authority'
  ],
  // Disable source maps in production to reduce memory usage
  productionBrowserSourceMaps: false,
  // Disable React strict mode for production build
  reactStrictMode: false,
  // Disable unnecessary features
  swcMinify: false,
  // Enable static exports
  output: 'export',
  // Disable image optimization for static export
  images: { unoptimized: true },
  // Disable dynamic routes for static export
  trailingSlash: true,
};

module.exports = nextConfig;
`;

// Backup the original next.config.js
const configPath = path.join(process.cwd(), "next.config.js");
const backupPath = path.join(process.cwd(), "next.config.js.bak");

try {
  if (fs.existsSync(configPath)) {
    console.log("Backing up original next.config.js...");
    fs.copyFileSync(configPath, backupPath);
  }

  // Write the minimal config
  console.log("Writing minimal next.config.js for build...");
  fs.writeFileSync(configPath, minimalConfig, "utf8");
} catch (error) {
  console.error("Error updating next.config.js:", error.message);
}

// Run the Next.js build command
let buildSuccess = false;

try {
  console.log("Running Next.js build with static export...");
  // Set environment variables directly
  const buildEnv = {
    ...process.env,
    NODE_OPTIONS: "--max-old-space-size=4096",
    NEXT_TELEMETRY_DISABLED: "1", // Disable telemetry to reduce build complexity
    NEXT_DISABLE_SOURCEMAPS: "1", // Disable source maps
    GENERATE_SOURCEMAP: "false", // Another way to disable source maps
  };

  // Run the build command with static export
  execSync("npx next build", {
    stdio: "inherit",
    env: buildEnv,
  });

  console.log("Build completed, copying output to 'out' directory...");

  // Copy the output to the 'out' directory for Netlify
  if (fs.existsSync("out")) {
    console.log("Removing existing 'out' directory...");
    fs.rmSync("out", { recursive: true, force: true });
  }

  console.log("Copying export output to 'out' directory...");
  fs.cpSync(".next/export", "out", { recursive: true });
  console.log("Build completed successfully!");
  buildSuccess = true;
} catch (error) {
  console.error("Build failed:", error.message);
} finally {
  // Restore the original next.config.js
  if (fs.existsSync(backupPath)) {
    try {
      console.log("Restoring original next.config.js...");
      fs.copyFileSync(backupPath, configPath);
      fs.unlinkSync(backupPath);
    } catch (restoreError) {
      console.error("Error restoring next.config.js:", restoreError.message);
    }
  }
}

console.log("Static build process completed!");

// Exit with appropriate code
process.exit(buildSuccess ? 0 : 1);
