import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';

// GET /api/auth/session
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ user: null });
    }
    
    return NextResponse.json({ 
      user: {
        id: session.user.id,
        email: session.user.email,
        role: session.user.user_metadata?.role || null
      } 
    });
  } catch (error: any) {
    console.error('Error in GET /api/auth/session:', error);
    return NextResponse.json({ user: null }, { status: 500 });
  }
}
