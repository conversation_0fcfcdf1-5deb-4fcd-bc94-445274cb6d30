import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/drivers/check?phone=1234567890
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchValue = url.searchParams.get("search");

    if (!searchValue) {
      return NextResponse.json(
        { error: "Missing required parameter: search" },
        { status: 400 },
      );
    }

    console.log(`Checking for driver with search value: ${searchValue}`);

    // First, let's get all drivers to see what's available
    const { data: allDrivers } = await supabase
      .from("drivers")
      .select("driver_id, name, phone_number, driver_number")
      .limit(10);

    console.log("Available drivers:", allDrivers);

    // Try direct searches first
    const { data: phoneData, error: phoneError } = await supabase
      .from("drivers")
      .select("driver_id, name, phone_number, driver_number")
      .eq("phone_number", searchValue);

    console.log("Direct phone search:", {
      found: phoneData && phoneData.length > 0,
      count: phoneData?.length || 0,
    });

    const { data: driverNumData, error: driverNumError } = await supabase
      .from("drivers")
      .select("driver_id, name, phone_number, driver_number")
      .eq("driver_number", searchValue);

    console.log("Direct driver_number search:", {
      found: driverNumData && driverNumData.length > 0,
      count: driverNumData?.length || 0,
    });

    // Use direct search results if available
    let data = null;
    let error = null;
    let count = 0;

    if (phoneData && phoneData.length > 0) {
      data = phoneData;
      count = phoneData.length;
      console.log("Using phone search results");
    } else if (driverNumData && driverNumData.length > 0) {
      data = driverNumData;
      count = driverNumData.length;
      console.log("Using driver_number search results");
    } else {
      // Fall back to OR query
      const result = await supabase
        .from("drivers")
        .select("driver_id, name, phone_number, driver_number", {
          count: "exact",
        })
        .or(`driver_number.eq.${searchValue},phone_number.eq.${searchValue}`);

      data = result.data;
      error = result.error;
      count = result.count || 0;
      console.log("Using OR query results");
    }

    console.log("Driver check result:", {
      found: count && count > 0,
      count: count || 0,
      error: error?.message,
      data,
    });

    if (error) {
      console.error("Error checking driver:", error);
      return NextResponse.json(
        { error: "Failed to check driver" },
        { status: 500 },
      );
    }

    return NextResponse.json({
      exists: count && count > 0,
      count: count || 0,
      driver: data && data.length > 0 ? data[0] : null,
    });
  } catch (error: any) {
    console.error("Error in GET /api/drivers/check:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
