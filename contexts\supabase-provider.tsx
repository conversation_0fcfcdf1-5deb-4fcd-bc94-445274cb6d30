'use client';

import { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';
import { createBrowserClient } from '@/lib/supabase';
import { Session, User, SupabaseClient } from '@supabase/supabase-js';

type SupabaseContextType = {
  supabase: SupabaseClient;
  user: User | null;
  session: Session | null;
};

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

export function SupabaseProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [supabase] = useState(() => createBrowserClient());

  // Use a ref to track if we've already initialized
  const initialized = useRef(false);

  useEffect(() => {
    // Prevent multiple initializations
    if (initialized.current) return;
    initialized.current = true;

    let subscription: { unsubscribe: () => void } | null = null;

    const setData = async () => {
      try {
        // Try to get the session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error);
        } else if (session) {
          setSession(session);
          setUser(session.user);

          // Store basic session info in localStorage
          try {
            localStorage.setItem('kpn_auth_active', 'true');
          } catch (storageError: any) {
            console.error("Error storing session info:", storageError);
          }
        } else {
          // Clear any stale session data
          setSession(null);
          setUser(null);

          try {
            localStorage.removeItem('kpn_auth_active');
          } catch (storageError: any) {
            console.error("Error clearing localStorage:", storageError);
          }
        }

        // Listen for auth changes
        try {
          const authListener = supabase.auth.onAuthStateChange(
            (event, session) => {
              if (session) {
                setSession(session);
                setUser(session.user);

                try {
                  localStorage.setItem('kpn_auth_active', 'true');
                } catch (storageError: any) {
                  console.error("Error updating localStorage:", storageError);
                }
              } else if (event === 'SIGNED_OUT') {
                setSession(null);
                setUser(null);

                try {
                  localStorage.removeItem('kpn_auth_active');
                } catch (storageError: any) {
                  console.error("Error clearing localStorage:", storageError);
                }
              }
            }
          );

          subscription = authListener.data.subscription;
        } catch (error: any) {
          console.error("Error setting up auth listener:", error);
        }
      } catch (error: any) {
        console.error("Unexpected error in auth setup:", error);
      } finally {
        setLoading(false);
      }
    };

    // Call setData to get the session
    setData();

    // Set up a periodic session check (not refresh) at a much lower frequency
    const checkInterval = setInterval(() => {
      // Only check if we think we're logged in
      if (session) {
        // Just check if the session is still valid, don't try to refresh it
        supabase.auth.getSession().then(({ data, error }) => {
          if (error) {
            console.error("Error checking session:", error);
          } else if (!data.session) {
            setSession(null);
            setUser(null);

            try {
              localStorage.removeItem('kpn_auth_active');
            } catch (storageError: any) {
              console.error("Error clearing localStorage:", storageError);
            }
          }
        });
      }
    }, 10 * 60 * 1000); // Check every 10 minutes

    return () => {
      if (subscription) {
        try {
          subscription.unsubscribe();
        } catch (error: any) {
          console.error("Error unsubscribing:", error);
        }
      }

      clearInterval(checkInterval);
    };
  }, []); // Empty dependency array to run only once

  const value = {
    supabase,
    user,
    session,
  };

  return (
    <SupabaseContext.Provider value={value}>
      {!loading && children}
    </SupabaseContext.Provider>
  );
}

export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
};
