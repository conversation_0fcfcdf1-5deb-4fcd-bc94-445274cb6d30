"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { NewParcelBooking } from "@/components/new-parcel-booking"
import { ParcelList } from "@/components/parcel-list"
import { useToast } from "@/hooks/use-toast"

export function ParcelManagement() {
  const { toast } = useToast();

  // Seed default parcel types on component mount
  useEffect(() => {
    const seedParcelTypes = async () => {
      try {
        const response = await fetch('/api/parceltypes/seed', {
          method: 'POST',
        });

        if (!response.ok) {
          console.error('Failed to seed parcel types');
          // Don't show an error toast to the user as this is a background operation
        } else {
          const data = await response.json();
          console.log('Parcel types seed result:', data.message);
        }
      } catch (error: any) {
        console.error('Error seeding parcel types:', error);
        // Don't show an error toast to the user as this is a background operation
      }
    };

    seedParcelTypes();
  }, []);

  return (
    <div className="space-y-4">
      <Tabs defaultValue="manage" className="space-y-4">
        <TabsList>
          <TabsTrigger value="manage">Manage Parcels</TabsTrigger>
          <TabsTrigger value="new">New Booking</TabsTrigger>
        </TabsList>
        <TabsContent value="manage" className="space-y-4">
          <ParcelList />
        </TabsContent>
        <TabsContent value="new" className="space-y-4">
          <NewParcelBooking />
        </TabsContent>
      </Tabs>
    </div>
  )
}