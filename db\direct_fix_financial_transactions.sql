-- Direct SQL fix for financial_transactions table
-- Run this directly in your database SQL console

-- 1. Update existing records to set status = approval_status where status is null
UPDATE financial_transactions
SET status = approval_status
WHERE status IS NULL;

-- 2. Create a trigger to keep status and approval_status in sync
CREATE OR REPLACE FUNCTION sync_approval_status_to_status()
RETURNS TRIGGER AS $$
BEGIN
  NEW.status = NEW.approval_status;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Add the trigger to the table
DROP TRIGGER IF EXISTS sync_status_trigger ON financial_transactions;
CREATE TRIGGER sync_status_trigger
BEFORE INSERT OR UPDATE ON financial_transactions
FOR EACH ROW
EXECUTE FUNCTION sync_approval_status_to_status();
