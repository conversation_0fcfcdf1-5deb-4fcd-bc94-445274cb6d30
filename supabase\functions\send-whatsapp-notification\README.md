# WhatsApp Notification Edge Function

This Supabase Edge Function sends WhatsApp notifications to parcel senders when the status of their parcels changes.

## Features

- Automatically sends WhatsApp notifications when parcel status changes
- Uses predefined WhatsApp templates for different status types
- Customizes message content based on parcel details
- Handles phone number formatting for Indian numbers

## Templates Used

The function uses the following WhatsApp templates:

1. `booking_confirmed` - Sent when a parcel is booked
2. `in_transit_2` - Sent when a parcel status changes to "In Transit"
3. `arrived_at_hub` - Sent when a parcel arrives at a hub (status "To Be Received" or "To Be Delivered")
4. `delivered` - Sent when a parcel is delivered

## Setup Instructions

### 1. Deploy the Edge Function

```bash
# Navigate to your project directory
cd your-project-directory

# Deploy the edge function
supabase functions deploy send-whatsapp-notification
```

### 2. API Token Configuration

The WhatsApp API token is already hardcoded in the function:

```typescript
const WATI_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Is61XbGlbosFzbbOupy8fer0oI2Hx8lnmvCNbsCtK4E";
```

If you need to update the token in the future, simply edit the `index.ts` file.

### 3. Set Up Database Webhook

1. Go to your Supabase project dashboard
2. Navigate to Database -> Webhooks
3. Create a new webhook with the following settings:
   - Name: `send-whatsapp-notification`
   - Table: `parcels`
   - Events: `UPDATE`
   - Filter: `current_status`
   - URL: `https://your-project-ref.supabase.co/functions/v1/send-whatsapp-notification`
   - HTTP Method: `POST`
   - Enable: `Yes`

## Testing

You can test the function by updating a parcel's status in the database:

```sql
-- Update a parcel status to trigger the webhook
UPDATE parcels
SET current_status = 'In Transit'
WHERE parcel_id = 123;
```

## Troubleshooting

If notifications aren't being sent:

1. Check the Supabase Edge Function logs in the dashboard
2. Verify that the WATI_API_TOKEN is set correctly
3. Ensure the webhook is properly configured and enabled
4. Check that the sender's phone number is correctly formatted in the database

## WhatsApp Templates

Make sure your WATI account has the following templates approved:

1. `booking_confirmed`
2. `in_transit_2`
3. `arrived_at_hub`
4. `delivered`

See the `whatsappbot.md` file for template details and variable mappings.
