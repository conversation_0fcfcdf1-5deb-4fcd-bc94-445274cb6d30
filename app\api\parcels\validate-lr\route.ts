import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/validate-lr
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number");
    const destination_branch_id = url.searchParams.get("destination_branch_id");
    const user_branch_id = url.searchParams.get("user_branch_id");

    if (!lr_number) {
      return NextResponse.json(
        { valid: false, message: "LR number is required" },
        { status: 400 },
      );
    }

    // Check if LR exists and is in a valid state for loading
    const { data: parcel, error } = await supabase
      .from("parcels")
      .select(`
        *,
        sender_branch:branches!sender_branch_id(name, code),
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("lr_number", lr_number)
      .single();

    if (error) {
      console.error("Error validating LR:", error);
      return NextResponse.json(
        { valid: false, message: "LR number not found" },
        { status: 404 },
      );
    }

    // Check if parcel is in a valid state for loading
    if (parcel.current_status !== "Booked") {
      return NextResponse.json(
        {
          valid: false,
          message:
            `LR is in ${parcel.current_status} status. Only Booked parcels can be loaded.`,
        },
        { status: 400 },
      );
    }

    // Check if parcel is at the user's branch
    if (
      user_branch_id && parcel.sender_branch_id.toString() !== user_branch_id
    ) {
      return NextResponse.json(
        {
          valid: false,
          message:
            `LR is not at your branch. It was booked at ${parcel.sender_branch.name}.`,
        },
        { status: 400 },
      );
    }

    // Check if parcel's destination matches the memo's destination
    if (
      destination_branch_id &&
      parcel.delivery_branch_id.toString() !== destination_branch_id
    ) {
      return NextResponse.json(
        {
          valid: false,
          message:
            `LR's destination (${parcel.delivery_branch.name}) doesn't match the memo's destination.`,
        },
        { status: 400 },
      );
    }

    // Check if parcel is already loaded on another vehicle
    const { data: loadingItems, error: loadingError } = await supabase
      .from("loading_chart_items")
      .select("*")
      .eq("lr_number", lr_number)
      .eq("status", "Pending");

    if (loadingError) {
      console.error("Error checking loading status:", loadingError);
    } else if (loadingItems && loadingItems.length > 0) {
      return NextResponse.json(
        {
          valid: false,
          message: "LR is already loaded on another vehicle",
        },
        { status: 400 },
      );
    }

    return NextResponse.json({
      valid: true,
      parcel_id: parcel.parcel_id,
      lr_number: parcel.lr_number,
      sender_branch: parcel.sender_branch,
      destination_branch: parcel.delivery_branch,
      item_count: parcel.number_of_items || 1,
      weight: parcel.weight || 0,
    });
  } catch (error: any) {
    console.error("Error in GET /api/parcels/validate-lr:", error);
    return NextResponse.json(
      { valid: false, message: "Failed to validate LR number" },
      { status: 500 },
    );
  }
}

// GET /api/parcels/validate-lr/branch
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { destination_branch_id, user_branch_id } = body;

    if (!destination_branch_id || !user_branch_id) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: destination_branch_id, user_branch_id",
        },
        { status: 400 },
      );
    }

    // Query all parcels from the user's branch with the specified destination
    const { data: parcels, error } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        number_of_items,
        weight,
        current_status,
        sender_branch:branches!sender_branch_id(name, code),
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("sender_branch_id", user_branch_id)
      .eq("delivery_branch_id", destination_branch_id)
      .eq("current_status", "Booked");

    if (error) {
      console.error("Error fetching parcels:", error);
      return NextResponse.json({ error: "Failed to fetch parcels" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      parcels: parcels || [],
    });
  } catch (error: any) {
    console.error("Error in POST /api/parcels/validate-lr/branch:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
