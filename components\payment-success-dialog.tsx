"use client"

import { useState } from "react"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { CheckCircle2, Download, ArrowLeft } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

interface PaymentSuccessDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  bookingDetails: {
    id: string
    date: string
    amount: string
    customerName: string
    customerEmail: string
    bookingType: string
  }
  onClose?: () => void
  hideReceiptOption?: boolean // Add prop to hide receipt option in admin app
}

export function PaymentSuccessDialog({
  open,
  onOpenChange,
  bookingDetails,
  onClose,
  hideReceiptOption = false
}: PaymentSuccessDialogProps) {
  const [hasDownloaded, setHasDownloaded] = useState(false)
  const [showBackConfirm, setShowBackConfirm] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleDownloadInvoice = () => {
    // Create a simple invoice content
    const invoiceContent = `
INVOICE
==============================
Booking ID: ${bookingDetails.id}
Date: ${bookingDetails.date}
Customer: ${bookingDetails.customerName}
Email: ${bookingDetails.customerEmail}
Amount: ₹${bookingDetails.amount}
Type: ${bookingDetails.bookingType}
==============================
Thank you for your business!
    `;

    // Create a Blob with the invoice content
    const blob = new Blob([invoiceContent], { type: 'text/plain' });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a link element
    const link = document.createElement('a');
    link.href = url;
    link.download = `Invoice-${bookingDetails.id}.txt`;

    // Append the link to the body
    document.body.appendChild(link);

    // Click the link to trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast({
      title: "Invoice Downloaded",
      description: "Your invoice has been downloaded successfully.",
    });

    setHasDownloaded(true);
  }

  const handleBackToBooking = () => {
    if (!hasDownloaded && !hideReceiptOption) {
      setShowBackConfirm(true)
    } else {
      goBackToBooking()
    }
  }

  const goBackToBooking = () => {
    onOpenChange(false)

    // Call the onClose function if provided
    if (onClose) {
      onClose()
    } else {
      // Fallback behavior if onClose is not provided
      router.push("/")
      toast({
        title: "Returned to Booking",
        description: "You have been returned to the booking options.",
      })
    }
  }

  return (
    <>
      <AlertDialog open={open && !showBackConfirm} onOpenChange={onOpenChange}>
        <AlertDialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <AlertDialogHeader>
            <div className="flex flex-col items-center justify-center text-center">
              <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
              <AlertDialogTitle className="text-xl font-semibold">Booking Confirmed!</AlertDialogTitle>
              <AlertDialogDescription className="mt-2">
                Your booking has been successfully confirmed. Thank you for your purchase.
              </AlertDialogDescription>
            </div>
          </AlertDialogHeader>

          <div className="my-4 p-4 bg-muted rounded-lg">
            <h3 className="font-semibold text-lg mb-2">Booking Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Booking ID:</span>
                <span className="font-medium break-all">{bookingDetails.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Date:</span>
                <span className="font-medium">{bookingDetails.date}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Amount:</span>
                <span className="font-medium">₹{bookingDetails.amount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Customer:</span>
                <span className="font-medium">{bookingDetails.customerName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Email:</span>
                <span className="font-medium">{bookingDetails.customerEmail}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Type:</span>
                <span className="font-medium">{bookingDetails.bookingType}</span>
              </div>
            </div>
          </div>

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={handleBackToBooking}
              className="w-full sm:w-auto"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Booking
            </Button>
            {!hideReceiptOption && (
              <Button
                onClick={handleDownloadInvoice}
                className="w-full sm:w-auto"
                variant="outline"
              >
                <Download className="mr-2 h-4 w-4" />
                {hasDownloaded ? "Download Again" : "Download Invoice"}
              </Button>
            )}
            <Button
              onClick={goBackToBooking}
              className="w-full sm:w-auto bg-primary hover:bg-primary/90"
            >
              Continue
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Confirmation dialog for going back without downloading */}
      {!hideReceiptOption && (
        <AlertDialog open={showBackConfirm} onOpenChange={setShowBackConfirm}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Download Invoice First?</AlertDialogTitle>
              <AlertDialogDescription>
                You haven't downloaded your invoice yet. Would you like to download it before going back to booking options?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowBackConfirm(false)}>
                Cancel
              </AlertDialogCancel>
              <Button
                onClick={handleDownloadInvoice}
                className="bg-primary hover:bg-primary/90"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Invoice
              </Button>
              <AlertDialogAction onClick={goBackToBooking}>
                Skip & Go Back
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  )
}
