"use client"

import { useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { NavigationLink } from "@/components/navigation-link"
import { useRouter } from "next/navigation"

export default function AuthTestPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [authState, setAuthState] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toISOString()}: ${message}`])
  }

  useEffect(() => {
    const checkAuth = async () => {
      try {
        addLog("Starting auth test check...")

        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          addLog(`Session error: ${sessionError.message}`)
          setError(`Session error: ${sessionError.message}`)
          setIsLoading(false)
          return
        }

        if (!session) {
          addLog("No session found")
          setAuthState({ status: "No session" })
          setIsLoading(false)
          return
        }

        addLog(`Session found: ${session.user.id} (${session.user.email})`)
        setAuthState({
          status: "Authenticated",
          user: {
            id: session.user.id,
            email: session.user.email,
            metadata: session.user.user_metadata
          },
          session: {
            expires_at: new Date(session.expires_at * 1000).toLocaleString(),
            status: "Active"
          }
        })

        // Try to refresh the session
        try {
          addLog("Attempting to refresh session...")
          const { data, error: refreshError } = await supabase.auth.refreshSession()

          if (refreshError) {
            addLog(`Session refresh error: ${refreshError.message}`)
          } else if (data.session) {
            addLog("Session refreshed successfully")
            setAuthState(prev => ({
              ...prev,
              session: {
                ...prev.session,
                expires_at: new Date(data.session.expires_at * 1000).toLocaleString(),
                refreshed: true
              }
            }))
          }
        } catch (refreshError: any) {
          addLog(`Unexpected error refreshing session: ${refreshError}`)
        }
      } catch (error: any) {
        addLog(`Unexpected error: ${error}`)
        setError(`Unexpected error: ${error}`)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const router = useRouter()

  const handleGoToHome = () => {
    addLog("Navigating to home page...")
    router.push("/")
  }

  const handleGoToLogin = () => {
    addLog("Navigating to login page...")
    router.push("/login")
  }

  const handleGoToOperations = () => {
    addLog("Navigating to operations page...")
    router.push("/operations")
  }

  const handleGoToParcels = () => {
    addLog("Navigating to parcels page...")
    router.push("/parcels")
  }

  const handleSignOut = async () => {
    try {
      addLog("Signing out...")
      await supabase.auth.signOut()
      addLog("Sign out successful")
      window.location.href = "/login"
    } catch (error: any) {
      addLog(`Sign out error: ${error}`)
      setError(`Sign out error: ${error}`)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
          <CardDescription>
            This page helps test authentication and navigation
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Checking authentication state...</span>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Current authentication state</h3>
                {authState ? (
                  <div className="space-y-2">
                    <p><span className="font-medium">User:</span> {authState.status}</p>
                    {authState.user && (
                      <>
                        <p><span className="font-medium">Email:</span> {authState.user.email}</p>
                        <p><span className="font-medium">User ID:</span> {authState.user.id}</p>
                        <p><span className="font-medium">Metadata:</span> {JSON.stringify(authState.user.metadata)}</p>
                      </>
                    )}
                    {authState.session && (
                      <>
                        <p><span className="font-medium">Session:</span> {authState.session.status}</p>
                        <p><span className="font-medium">Expires:</span> {authState.session.expires_at}</p>
                        {authState.session.refreshed && (
                          <p className="text-green-600 font-medium">Session was successfully refreshed</p>
                        )}
                      </>
                    )}
                  </div>
                ) : (
                  <p>No authentication state available</p>
                )}
              </div>

              {error && (
                <div className="p-4 bg-destructive/10 text-destructive rounded-md">
                  <h3 className="font-bold mb-2">Error</h3>
                  <p>{error}</p>
                </div>
              )}

              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Debug Logs</h3>
                <div className="whitespace-pre-wrap overflow-auto max-h-[300px] text-xs p-2 bg-background rounded">
                  {logs.map((log, index) => (
                    <div key={index} className="py-1 border-b border-border last:border-0">
                      {log}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button onClick={handleGoToHome} variant="outline">
            Go to Home
          </Button>
          <Button onClick={handleGoToOperations} variant="default">
            Go to Operations
          </Button>
          <Button onClick={handleGoToParcels} variant="default">
            Go to Parcels
          </Button>
          <Button onClick={handleGoToLogin} variant="outline">
            Go to Login
          </Button>
          <Button onClick={handleSignOut} variant="destructive">
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
