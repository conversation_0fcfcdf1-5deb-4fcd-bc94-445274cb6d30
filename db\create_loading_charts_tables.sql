-- Create loading_charts table
CREATE TABLE IF NOT EXISTS public.loading_charts (
  chart_id SERIAL PRIMARY KEY,
  chart_number VARCHAR(50) UNIQUE NOT NULL,
  memo_id INT REFERENCES memos(memo_id) ON DELETE SET NULL,
  vehicle_id INT REFERENCES vehicles(vehicle_id) ON DELETE SET NULL,
  destination_branch_id INT REFERENCES branches(branch_id) ON DELETE SET NULL,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'Created',
  notes TEXT
);

-- Create loading_chart_items table
CREATE TABLE IF NOT EXISTS public.loading_chart_items (
  item_id SERIAL PRIMARY KEY,
  chart_id INT REFERENCES loading_charts(chart_id) ON DELETE CASCADE,
  lr_number VARCHAR(50) NOT NULL,
  quantity INT NOT NULL DEFAULT 1,
  status VARCHAR(20) NOT NULL DEFAULT 'Pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS loading_charts_memo_id_idx ON loading_charts(memo_id);
CREATE INDEX IF NOT EXISTS loading_charts_vehicle_id_idx ON loading_charts(vehicle_id);
CREATE INDEX IF NOT EXISTS loading_chart_items_chart_id_idx ON loading_chart_items(chart_id);
CREATE INDEX IF NOT EXISTS loading_chart_items_lr_number_idx ON loading_chart_items(lr_number);

-- Add parcel_status enum values if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'parcel_status') THEN
    CREATE TYPE parcel_status AS ENUM ('Booked', 'In Transit', 'Out for Delivery', 'Delivered', 'Returned', 'Lost');
  ELSE
    -- Check if 'In Transit' value exists in the enum
    IF NOT EXISTS (
      SELECT 1
      FROM pg_enum e
      JOIN pg_type t ON e.enumtypid = t.oid
      WHERE t.typname = 'parcel_status' AND e.enumlabel = 'In Transit'
    ) THEN
      -- Add 'In Transit' value to the enum
      ALTER TYPE parcel_status ADD VALUE 'In Transit' AFTER 'Booked';
    END IF;
  END IF;
END$$;

-- Add op_type enum values if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'op_type') THEN
    CREATE TYPE op_type AS ENUM ('Load', 'Unload');
  END IF;
END$$;

-- Create a function to update parcel status when a loading chart item is created
CREATE OR REPLACE FUNCTION update_parcel_status_on_loading()
RETURNS TRIGGER AS $$
BEGIN
  -- Update parcel status to 'In Transit' when loaded
  UPDATE parcels
  SET current_status = 'In Transit'
  WHERE lr_number = NEW.lr_number;

  -- Create an operation record
  INSERT INTO operations (
    operation_type,
    vehicle_id,
    parcel_id,
    confirmed_item_count,
    total_item_count,
    operator_id
  )
  SELECT
    'Load'::op_type,
    lc.vehicle_id,
    p.parcel_id,
    NEW.quantity,
    p.number_of_items,
    (SELECT user_id FROM users WHERE auth_id = (SELECT created_by FROM loading_charts WHERE chart_id = NEW.chart_id))
  FROM
    loading_charts lc
    JOIN parcels p ON p.lr_number = NEW.lr_number
  WHERE
    lc.chart_id = NEW.chart_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update parcel status when a loading chart item is created
CREATE TRIGGER update_parcel_status_on_loading_trigger
AFTER INSERT ON loading_chart_items
FOR EACH ROW
EXECUTE FUNCTION update_parcel_status_on_loading();
