"use client"

import { useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { PageLayout } from "@/components/page-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function MigrationsPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const runMigration = async (migrationName: string) => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch(`/api/migrations/${migrationName}`, {
        method: 'POST',
      })

      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || 'Migration completed successfully'
        })
        toast({
          title: "Success",
          description: "Migration completed successfully",
        })
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to run migration'
        })
        toast({
          title: "Error",
          description: data.error || "Failed to run migration",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error(`Error running ${migrationName} migration:`, error)
      setResult({
        success: false,
        message: 'An unexpected error occurred'
      })
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <PageLayout
      title="Database Migrations"
      subtitle="Run database migrations to update the schema"
    >
      <DashboardShell>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Loading Charts Tables</CardTitle>
              <CardDescription>
                Create tables for loading charts functionality
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This migration creates the following tables:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                <li>loading_charts</li>
                <li>loading_chart_items</li>
              </ul>
              <p className="text-sm text-muted-foreground mt-2">
                It also adds the 'In Transit' status to the parcel_status enum.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => runMigration('loading-charts')}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  "Run Migration"
                )}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Driver Expenses</CardTitle>
              <CardDescription>
                Add support for driver expenses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This migration adds:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                <li>driver_expenses_json column to memos table</li>
                <li>driver_expenses table for detailed expense tracking</li>
              </ul>
              <p className="text-sm text-muted-foreground mt-2">
                This fixes the error when completing memos with driver expenses.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => runMigration('driver-expenses')}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  "Run Migration"
                )}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Add Metadata Column</CardTitle>
              <CardDescription>
                Add metadata column to financial transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This migration adds:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                <li>metadata column (JSONB) to financial_transactions table</li>
              </ul>
              <p className="text-sm text-muted-foreground mt-2">
                This enables tracking payment methods and separating cash vs. online payments in branch balance calculations.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => runMigration('add-metadata-column')}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  "Run Migration"
                )}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Sync Transaction Status</CardTitle>
              <CardDescription>
                Sync approval_status to status in financial transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This migration:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                <li>Creates a trigger to sync approval_status to status</li>
                <li>Updates existing records to ensure consistency</li>
              </ul>
              <p className="text-sm text-muted-foreground mt-2">
                This fixes errors when creating financial transactions by ensuring both status fields are properly set.
              </p>
            </CardContent>
            <CardFooter>
              <div className="flex flex-col space-y-2">
                <Button
                  onClick={() => runMigration('sync-transaction-status')}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Running...
                    </>
                  ) : (
                    "Run Migration"
                  )}
                </Button>
                <Button
                  onClick={() => runMigration('sync-transaction-status-simple')}
                  disabled={isLoading}
                  variant="outline"
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Running...
                    </>
                  ) : (
                    "Run Simple Version"
                  )}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </div>

        {result && (
          <Alert variant={result.success ? "default" : "destructive"} className="mt-4">
            {result.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertTitle>{result.success ? "Success" : "Error"}</AlertTitle>
            <AlertDescription>{result.message}</AlertDescription>
          </Alert>
        )}
      </DashboardShell>
    </PageLayout>
  )
}
