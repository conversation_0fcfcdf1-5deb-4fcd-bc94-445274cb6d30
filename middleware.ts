import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs";

// List of paths that don't require authentication
const publicPaths = [
  "/login",
  "/forgot-password",
  "/reset-password",
  "/api/auth/callback",
  "/api/auth/validate-email-for-reset",
  "/_next",
  "/favicon.ico",
  "/test-branch",
  "/assign-branch",
  "/check-branch",
  "/auth-test", // Auth test page
];

export async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;
  console.log(`Middleware processing path: ${path}`);

  // Check if the path is public
  if (publicPaths.some((publicPath) => path.startsWith(publicPath))) {
    console.log(`Path ${path} is public, skipping auth check`);
    return NextResponse.next();
  }

  // Create a response object that we can modify
  const res = NextResponse.next();

  // Create a Supabase client configured to use cookies
  const supabase = createMiddlewareClient({ req, res });

  try {
    // Refresh session if expired
    const { data: { session } } = await supabase.auth.getSession();

    console.log(
      `Session check for ${path}: ${
        session ? "Authenticated" : "Not authenticated"
      }`,
    );

    // If no session and not a public path, handle accordingly
    if (!session) {
      // If accessing an API route, return 401
      if (path.startsWith("/api/")) {
        console.log(`Unauthorized API access: ${path}`);
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      // Otherwise redirect to login
      console.log(`Redirecting to login: ${path} (no session)`);
      return NextResponse.redirect(new URL("/login", req.url));
    }

    // User is authenticated, proceed
    console.log(`User authenticated, proceeding to ${path}`);
    return res;
  } catch (error: any) {
    console.error(`Error in middleware for ${path}:`, error);

    // If there's an error and it's an API route, return 500
    if (path.startsWith("/api/")) {
      return NextResponse.json({ error: "Internal Server Error" }, {
        status: 500,
      });
    }

    // Otherwise redirect to login
    console.log(`Redirecting to login due to error: ${path}`);
    return NextResponse.redirect(new URL("/login", req.url));
  }
}

// Run on all routes
export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
