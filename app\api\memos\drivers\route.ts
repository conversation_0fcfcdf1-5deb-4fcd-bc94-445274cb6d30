import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/memos/drivers
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { driver_ids } = body;

    if (!driver_ids || !Array.isArray(driver_ids) || driver_ids.length === 0) {
      return NextResponse.json(
        { error: "Missing or invalid driver_ids" },
        { status: 400 },
      );
    }

    // Fetch driver information for the provided IDs
    const { data: drivers, error } = await supabase
      .from("drivers")
      .select("driver_id, driver_number")
      .in("driver_id", driver_ids);

    if (error) {
      console.error("Error fetching driver information:", error);
      return NextResponse.json(
        { error: "Failed to fetch driver information" },
        { status: 500 },
      );
    }

    // Create a mapping of driver_id to driver_number
    const driverMap = drivers.reduce<Record<number, string>>((map, driver) => {
      map[driver.driver_id] = driver.driver_number;
      return map;
    }, {});

    // Create an array of driver_numbers in the same order as driver_ids
    const driver_numbers = driver_ids.map((id) => driverMap[id] || null);

    return NextResponse.json({ driver_numbers });
  } catch (error: any) {
    console.error("Error in POST /api/memos/drivers:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error.message || String(error),
      },
      { status: 500 },
    );
  }
}
