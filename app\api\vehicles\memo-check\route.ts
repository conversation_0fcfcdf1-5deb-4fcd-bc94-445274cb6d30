import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/vehicles/memo-check?vehicle_number=ABC123
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const vehicleNumber = url.searchParams.get("vehicle_number");

    if (!vehicleNumber) {
      return NextResponse.json(
        { error: "Missing required parameter: vehicle_number" },
        { status: 400 }
      );
    }

    // Get user's branch information
    const { data: { user } } = await routeHandlerClient.auth.getUser();
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // First, find the vehicle by registration number
    const normalizedVehicleNumber = vehicleNumber.replace(/\s+/g, "");
    
    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select("vehicle_id, registration_number")
      .or(
        `registration_number.ilike.${normalizedVehicleNumber},registration_number.ilike.%${vehicleNumber}%`
      )
      .single();

    if (vehicleError) {
      return NextResponse.json({
        found: false,
        message: "Vehicle not found"
      });
    }

    // Check for active memos with "Created" status
    const { data: activeMemos, error: memosError } = await supabase
      .from("memos")
      .select(`
        memo_id,
        memo_number,
        status,
        from_branch_id,
        to_branch_id,
        branches!memos_from_branch_id_fkey(name),
        to_branch:branches!memos_to_branch_id_fkey(name)
      `)
      .eq("vehicle_id", vehicle.vehicle_id)
      .eq("status", "Created")
      .order("created_at", { ascending: false });

    if (memosError) {
      console.error("Error checking memos:", memosError);
      return NextResponse.json({ error: "Failed to check memos" }, { status: 500 });
    }

    if (!activeMemos || activeMemos.length === 0) {
      return NextResponse.json({
        found: true,
        vehicle_id: vehicle.vehicle_id,
        registration_number: vehicle.registration_number,
        has_active_memo: false,
        message: "No active memo found for this vehicle"
      });
    }

    // Check if any memo is from the user's branch
    const userBranchMemo = activeMemos.find(memo => memo.from_branch_id === userData.branch_id);

    if (userBranchMemo) {
      return NextResponse.json({
        found: true,
        vehicle_id: vehicle.vehicle_id,
        registration_number: vehicle.registration_number,
        has_active_memo: true,
        memo_from_user_branch: true,
        memo: {
          memo_id: userBranchMemo.memo_id,
          memo_number: userBranchMemo.memo_number,
          status: userBranchMemo.status,
          from_branch: userBranchMemo.branches?.name,
          to_branch: userBranchMemo.to_branch?.name
        },
        message: `Active memo found: ${userBranchMemo.memo_number}`
      });
    } else {
      // Vehicle has active memo but not from user's branch
      const otherMemo = activeMemos[0];
      return NextResponse.json({
        found: true,
        vehicle_id: vehicle.vehicle_id,
        registration_number: vehicle.registration_number,
        has_active_memo: true,
        memo_from_user_branch: false,
        memo: {
          memo_number: otherMemo.memo_number,
          from_branch: otherMemo.branches?.name,
          to_branch: otherMemo.to_branch?.name
        },
        message: `Vehicle has active memo from ${otherMemo.branches?.name}, but not from your branch`
      });
    }

  } catch (error: any) {
    console.error("Error in GET /api/vehicles/memo-check:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
