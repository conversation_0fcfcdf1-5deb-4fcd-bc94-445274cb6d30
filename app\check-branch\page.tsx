"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Loader2 } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase"

export default function CheckBranchPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [supabase] = useState(() => createBrowserClient())

  useEffect(() => {
    const checkUserBranch = async () => {
      try {
        console.log("Starting check-branch process...")

        // Get the current user
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error("Authentication error:", authError)
          setError("Authentication error. Please try logging in again.")
          setIsLoading(false)
          return
        }

        if (!user) {
          console.error("No user found in auth state")
          setError("No user found. Please try logging in again.")
          setIsLoading(false)
          return
        }

        console.log("Auth user found:", user.id, user.email)

        // Get the user's profile using email (since auth.id is UUID and users.user_id is integer)
        let profileData
        let profileError

        console.log("Looking up user by email:", user.email)

        // Use email for lookup since it's the common identifier between auth and public users table
        const { data: userByEmail, error: userByEmailError } = await supabase
          .from("users")
          .select("*")
          .eq("email", user.email)
          .single()

        if (userByEmailError) {
          console.error("Error fetching user profile by email:", userByEmailError)
          setError("Could not find your user profile. Please contact your administrator.")
          setIsLoading(false)
          return
        }

        profileData = userByEmail
        profileError = userByEmailError

        if (!profileData) {
          console.error("No profile data found for user")
          setError("Your user profile could not be found. Please contact your administrator.")
          setIsLoading(false)
          return
        }

        console.log("User profile found:", profileData)

        // Check if the user has a branch assigned
        if (!profileData.branch_id) {
          // Check if the user is an admin - only admins should be able to assign themselves to branches
          if (profileData.role === "Super Admin" || profileData.role === "Admin") {
            console.log("Admin user has no branch assigned, redirecting to assign-branch")
            window.location.href = "/assign-branch"
          } else {
            // Regular users without a branch should see an error message
            console.log("Regular user has no branch assigned, showing error")
            setError("Your account is not associated with any branch. Please contact your administrator.")
          }
        } else {
          console.log("User has branch assigned, redirecting to dashboard")

          // Store branch ID in localStorage for fallback
          try {
            localStorage.setItem('user_branch_id', profileData.branch_id.toString());
            console.log("Stored branch ID in localStorage:", profileData.branch_id);
          } catch (e) {
            console.warn("Failed to store branch ID in localStorage:", e);
          }

          console.log("Redirecting to dashboard...")
          // Use window.location to ensure we get the correct port
          window.location.href = "/"
        }
      } catch (error: any) {
        console.error("Error checking user branch:", error)
        setError("An unexpected error occurred. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    checkUserBranch()
  }, [router])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {isLoading ? (
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <p className="text-lg font-medium">Checking your account...</p>
        </div>
      ) : error ? (
        <div className="p-6 bg-destructive/10 text-destructive rounded-md max-w-md">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error}</p>
          <div className="mt-4 flex flex-col space-y-2">
            <button
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
              onClick={() => {
                // Clear any Supabase-related items from localStorage before redirecting
                try {
                  for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith("supabase.") || key.includes("auth"))) {
                      localStorage.removeItem(key);
                    }
                  }
                } catch (e: any) {
                  console.warn("Error clearing localStorage:", e);
                }

                // Sign out from Supabase
                supabase.auth.signOut().then(() => {
                  // Use window.location to ensure we get the correct port
                  window.location.href = "/login";
                });
              }}
            >
              Go to Login
            </button>
            <p className="text-xs text-center mt-2">
              If you continue to experience issues, please contact your administrator.
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <p className="text-lg font-medium">Redirecting...</p>
        </div>
      )}
    </div>
  )
}
