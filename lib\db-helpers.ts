import { supabase } from "./supabase";
import type { Database } from "./database.types";

// Parcels
export type Parcel = Database["public"]["Tables"]["parcels"]["Row"];
export type NewParcel = Database["public"]["Tables"]["parcels"]["Insert"];
export type UpdateParcel = Database["public"]["Tables"]["parcels"]["Update"];

export async function getParcels(
  filters: Partial<Parcel> = {},
  page: number = 1,
  pageSize: number = 20,
) {
  // Calculate range for pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Build query with pagination
  const query = supabase
    .from("parcels")
    .select(
      "*, parceltypes(*), sender_branch:branches!parcels_sender_branch_id_fkey(*), delivery_branch:branches!parcels_delivery_branch_id_fkey(*)",
      { count: "exact" },
    )
    .match(filters)
    .order("booking_datetime", { ascending: false })
    .range(from, to);

  const { data, error, count } = await query;

  if (error) {
    console.error("Error fetching parcels:", error);
    return { parcels: null, count: 0 };
  }

  return {
    parcels: data as (Parcel & {
      parceltypes: ParcelType | null;
      sender_branch: Branch | null;
      delivery_branch: Branch | null;
    })[],
    count: count || 0,
  };
}

export async function getParcelById(id: number) {
  const { data, error } = await supabase
    .from("parcels")
    .select(
      "*, parceltypes(*), sender_branch:branches!parcels_sender_branch_id_fkey(*), delivery_branch:branches!parcels_delivery_branch_id_fkey(*)",
    )
    .eq("parcel_id", id)
    .single();

  if (error) {
    console.error("Error fetching parcel:", error);
    return null;
  }

  return data as (Parcel & {
    parceltypes: ParcelType | null;
    sender_branch: Branch | null;
    delivery_branch: Branch | null;
  });
}

export async function createParcel(parcel: NewParcel) {
  const { data, error } = await supabase
    .from("parcels")
    .insert(parcel)
    .select()
    .single();

  if (error) {
    console.error("Error creating parcel:", error);
    return null;
  }

  return data as Parcel;
}

export async function updateParcel(id: number, updates: UpdateParcel) {
  const { data, error } = await supabase
    .from("parcels")
    .update(updates)
    .eq("parcel_id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating parcel:", error);
    return null;
  }

  return data as Parcel;
}

export async function deleteParcel(id: number) {
  const { error } = await supabase
    .from("parcels")
    .delete()
    .eq("parcel_id", id);

  if (error) {
    console.error("Error deleting parcel:", error);
    return false;
  }

  return true;
}

// Parcel Types
export type ParcelType = Database["public"]["Tables"]["parceltypes"]["Row"];
export type NewParcelType =
  Database["public"]["Tables"]["parceltypes"]["Insert"];
export type UpdateParcelType =
  Database["public"]["Tables"]["parceltypes"]["Update"];

export async function getParcelTypes(filters: Partial<ParcelType> = {}) {
  const { data, error } = await supabase
    .from("parceltypes")
    .select("*")
    .match(filters)
    .order("type_name", { ascending: true });

  if (error) {
    console.error("Error fetching parcel types:", error);
    return null;
  }

  return data as ParcelType[];
}

export async function getParcelTypeById(id: number) {
  const { data, error } = await supabase
    .from("parceltypes")
    .select("*")
    .eq("type_id", id)
    .single();

  if (error) {
    console.error("Error fetching parcel type:", error);
    return null;
  }

  return data as ParcelType;
}

export async function createParcelType(parcelType: NewParcelType) {
  const { data, error } = await supabase
    .from("parceltypes")
    .insert(parcelType)
    .select()
    .single();

  if (error) {
    console.error("Error creating parcel type:", error);
    return null;
  }

  return data as ParcelType;
}

export async function updateParcelType(id: number, updates: UpdateParcelType) {
  const { data, error } = await supabase
    .from("parceltypes")
    .update(updates)
    .eq("type_id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating parcel type:", error);
    return null;
  }

  return data as ParcelType;
}

export async function deleteParcelType(id: number) {
  const { error } = await supabase
    .from("parceltypes")
    .delete()
    .eq("type_id", id);

  if (error) {
    console.error("Error deleting parcel type:", error);
    return false;
  }

  return true;
}

// Cities
export type City = Database["public"]["Tables"]["cities"]["Row"];
export type NewCity = Database["public"]["Tables"]["cities"]["Insert"];
export type UpdateCity = Database["public"]["Tables"]["cities"]["Update"];

export async function getCities(filters: Partial<City> = {}) {
  const { data, error } = await supabase
    .from("cities")
    .select("*")
    .match(filters)
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching cities:", error);
    return null;
  }

  return data as City[];
}

export async function getCityById(id: number) {
  const { data, error } = await supabase
    .from("cities")
    .select("*")
    .eq("city_id", id)
    .single();

  if (error) {
    console.error("Error fetching city:", error);
    return null;
  }

  return data as City;
}

// Branches
export type Branch = Database["public"]["Tables"]["branches"]["Row"];
export type NewBranch = Database["public"]["Tables"]["branches"]["Insert"];
export type UpdateBranch = Database["public"]["Tables"]["branches"]["Update"];

export async function getBranches(filters: Partial<Branch> = {}) {
  const { data, error } = await supabase
    .from("branches")
    .select("*, cities(*)")
    .match(filters)
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching branches:", error);
    return null;
  }

  return data as (Branch & { cities: City | null })[];
}

export async function getBranchById(id: number) {
  const { data, error } = await supabase
    .from("branches")
    .select("*, cities(*)")
    .eq("branch_id", id)
    .single();

  if (error) {
    console.error("Error fetching branch:", error);
    return null;
  }

  return data as (Branch & { cities: City | null });
}

export async function getBranchesByCity(cityId: number) {
  const { data, error } = await supabase
    .from("branches")
    .select("*, cities(*)")
    .eq("city_id", cityId)
    .order("name", { ascending: true });

  if (error) {
    console.error("Error fetching branches by city:", error);
    return null;
  }

  return data as (Branch & { cities: City | null })[];
}

// Financial Transactions
export type FinancialTransaction =
  Database["public"]["Tables"]["financialtransactions"]["Row"];
export type NewFinancialTransaction =
  Database["public"]["Tables"]["financialtransactions"]["Insert"];
export type UpdateFinancialTransaction =
  Database["public"]["Tables"]["financialtransactions"]["Update"];

export async function getFinancialTransactions(
  filters: Partial<FinancialTransaction> = {},
) {
  const { data, error } = await supabase
    .from("financialtransactions")
    .select("*, branches(*)")
    .match(filters)
    .order("date_time", { ascending: false });

  if (error) {
    console.error("Error fetching financial transactions:", error);
    return null;
  }

  return data as (FinancialTransaction & { branches: Branch })[];
}

export async function createFinancialTransaction(
  transaction: NewFinancialTransaction,
) {
  const { data, error } = await supabase
    .from("financialtransactions")
    .insert(transaction)
    .select()
    .single();

  if (error) {
    console.error("Error creating financial transaction:", error);
    return null;
  }

  return data as FinancialTransaction;
}
