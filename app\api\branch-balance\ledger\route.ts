import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/branch-balance/ledger
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const branch_id = url.searchParams.get("branch_id");
    const from_date = url.searchParams.get("from_date");
    const to_date = url.searchParams.get("to_date");
    const limit = parseInt(url.searchParams.get("limit") || "30"); // Default to 30 days
    const offset = parseInt(url.searchParams.get("offset") || "0");

    if (!branch_id) {
      return NextResponse.json({ error: "Branch ID is required" }, { status: 400 });
    }

    // Build query
    let query = supabase
      .from("accountsledger")
      .select("*")
      .eq("branch_id", branch_id);

    // Add date filters if provided
    if (from_date) {
      query = query.gte("ledger_date", from_date);
    }

    if (to_date) {
      query = query.lte("ledger_date", to_date);
    }

    // Add pagination
    query = query.order("ledger_date", { ascending: false })
      .range(offset, offset + limit - 1);

    // Execute query
    const { data: ledgerEntries, error, count } = await query;

    if (error) {
      console.error("Error fetching ledger entries:", error);
      return NextResponse.json({ error: "Failed to fetch ledger entries" }, { status: 500 });
    }

    return NextResponse.json({ ledger_entries: ledgerEntries, count });
  } catch (error: any) {
    console.error("Error in GET /api/branch-balance/ledger:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/branch-balance/ledger
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.branch_id || !body.ledger_date || 
        body.opening_balance === undefined || body.closing_balance === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: branch_id, ledger_date, opening_balance, closing_balance" },
        { status: 400 }
      );
    }

    // Check if ledger entry already exists for this date and branch
    const { data: existingEntry, error: checkError } = await supabase
      .from("accountsledger")
      .select("ledger_id")
      .eq("branch_id", body.branch_id)
      .eq("ledger_date", body.ledger_date)
      .maybeSingle();

    if (checkError) {
      console.error("Error checking existing ledger entry:", checkError);
      return NextResponse.json({ error: "Failed to check existing ledger entry" }, { status: 500 });
    }

    let ledgerEntry;
    
    if (existingEntry) {
      // Update existing entry
      const { data: updatedEntry, error: updateError } = await supabase
        .from("accountsledger")
        .update({
          opening_balance: body.opening_balance,
          total_collection: body.total_collection || 0,
          total_expense: body.total_expense || 0,
          remitted: body.remitted || 0,
          closing_balance: body.closing_balance
        })
        .eq("ledger_id", existingEntry.ledger_id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating ledger entry:", updateError);
        return NextResponse.json({ error: "Failed to update ledger entry" }, { status: 500 });
      }

      ledgerEntry = updatedEntry;
    } else {
      // Create new entry
      const { data: newEntry, error: insertError } = await supabase
        .from("accountsledger")
        .insert({
          branch_id: body.branch_id,
          ledger_date: body.ledger_date,
          opening_balance: body.opening_balance,
          total_collection: body.total_collection || 0,
          total_expense: body.total_expense || 0,
          remitted: body.remitted || 0,
          closing_balance: body.closing_balance
        })
        .select()
        .single();

      if (insertError) {
        console.error("Error creating ledger entry:", insertError);
        return NextResponse.json({ error: "Failed to create ledger entry" }, { status: 500 });
      }

      ledgerEntry = newEntry;
    }

    return NextResponse.json({ ledger_entry: ledgerEntry });
  } catch (error: any) {
    console.error("Error in POST /api/branch-balance/ledger:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
