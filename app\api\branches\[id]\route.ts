import { NextRequest, NextResponse } from 'next/server';
import { getBranchById } from '@/lib/db-helpers';
import { supabase } from '@/lib/supabase';

// GET /api/branches/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid branch ID' }, { status: 400 });
    }
    
    // Try to get branch using the helper function
    const branch = await getBranchById(id);
    
    if (branch) {
      return NextResponse.json({ branch });
    }
    
    // If the helper function fails, try direct Supabase query as fallback
    const { data, error } = await supabase
      .from("branches")
      .select("*, cities(*)")
      .eq("branch_id", id)
      .single();
    
    if (error) {
      console.error(`Error in GET /api/branches/${params.id}:`, error);
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }
    
    return NextResponse.json({ branch: data });
  } catch (error: any) {
    console.error(`Error in GET /api/branches/${params.id}:`, error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
