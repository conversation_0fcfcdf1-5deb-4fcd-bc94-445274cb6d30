import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// POST /api/expenses/verify-memo
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { memo_number } = body;

    if (!memo_number) {
      return NextResponse.json(
        { error: "Missing required field: memo_number" },
        { status: 400 },
      );
    }

    // Check if memo exists and is in Received status
    const { data: memo, error: memoError } = await supabase
      .from("memos")
      .select(`
        memo_id,
        memo_number,
        status,
        bata_amount,
        salary_amount,
        driver_ids,
        driver_expenses_json,
        from_branch:branches!from_branch_id(name, branch_id),
        to_branch:branches!to_branch_id(name, branch_id)
      `)
      .eq("memo_number", memo_number)
      .single();

    if (memoError) {
      console.error("Error fetching memo:", memoError);
      return NextResponse.json(
        { error: "Memo not found" },
        { status: 404 },
      );
    }

    if (memo.status !== "Received") {
      return NextResponse.json(
        { error: "Memo must be in Received status to create an expense" },
        { status: 400 },
      );
    }

    // Check if an expense already exists for this memo
    const { data: existingExpense, error: expenseError } = await supabase
      .from("expenses")
      .select("id")
      .eq("memo_number", memo_number)
      .maybeSingle();

    if (existingExpense) {
      return NextResponse.json(
        { error: "An expense already exists for this memo" },
        { status: 400 },
      );
    }

    // Get driver details if available
    let driverDetails: {
      driver_id: number;
      name: string;
      driver_number: string;
    }[] = [];
    if (memo.driver_ids && Array.isArray(memo.driver_ids)) {
      const { data: drivers, error: driversError } = await supabase
        .from("drivers")
        .select("driver_id, name, driver_number")
        .in("driver_id", memo.driver_ids);

      if (!driversError && drivers) {
        driverDetails = drivers;
      }
    }

    return NextResponse.json({
      memo: {
        ...memo,
        driverDetails,
      },
    });
  } catch (error: any) {
    console.error("Error in POST /api/expenses/verify-memo:", error);
    return NextResponse.json({
      error: "Internal server error",
      details: error.message || String(error),
    }, {
      status: 500,
    });
  }
}
