
---

## KPN Parcel WhatsApp Templates

### 1. `booking_confirmed`

**Template text:**

```
📦 *KPN Parcel Update: Booking Confirmed*

Hi {{var1}},  
Your parcel booking (Booking ID: {{var2}}) is confirmed.  
📅 Booked on: {{var3}}  
📍 Pickup address: {{var4}}
```

| Variable | Meaning       |
| -------- | ------------- |
| var1     | CustomerName  |
| var2     | BookingID     |
| var3     | BookingDate   |
| var4     | PickupAddress |

**Example payload:**

```bash
curl -X POST 'https://live-mt-server.wati.io/7454/api/v1/sendTemplateMessages' \
  -H 'accept: */*' \
  -H 'Authorization: Bearer <YOUR_TOKEN>' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
    "template_name": "booking_confirmed",
    "broadcast_name": "booking-confirmation",
    "receivers": [
      {
        "whatsappNumber": "918086221201",
        "customParams": [
          { "name": "var1", "value": "Alice" },
          { "name": "var2", "value": "ABC123" },
          { "name": "var3", "value": "2025-05-14" },
          { "name": "var4", "value": "123 Main St, Mumbai" }
        ]
      }
    ]
  }'
```

---

### 2. `in_transit_2`

**Template text:**

```
🚚 *KPN Parcel Update: In Transit*

Hello {{var1}},  
Your parcel (Booking ID: {{var2}}) is In Transit.  
📍 Last scanned at {{var3}} on {{var4}}  
🗓️ Expected delivery date: {{var5}}
```

| Variable | Meaning              |
| -------- | -------------------- |
| var1     | CustomerName         |
| var2     | BookingID            |
| var3     | ScanLocation         |
| var4     | ScanDateTime         |
| var5     | ExpectedDeliveryDate |

**Example payload:**

```bash
curl -X POST 'https://live-mt-server.wati.io/7454/api/v1/sendTemplateMessages' \
  -H 'accept: */*' \
  -H 'Authorization: Bearer <YOUR_TOKEN>' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
    "template_name": "in_transit_2",
    "broadcast_name": "in-transit-update",
    "receivers": [
      {
        "whatsappNumber": "918086221201",
        "customParams": [
          { "name": "var1", "value": "Alice" },
          { "name": "var2", "value": "ABC123" },
          { "name": "var3", "value": "Mumbai Hub" },
          { "name": "var4", "value": "2025-05-14 10:00 AM" },
          { "name": "var5", "value": "2025-05-15" }
        ]
      }
    ]
  }'
```

---

### 3. `arrived_at_hub`

**Template text:**

```
🗺️ *KPN Parcel Update: Arrived at Hub*

Hi {{var1}},  
Your parcel (Booking ID: {{var2}}) has arrived at the {{var3}} hub.  
📍 Arrival time: {{var4}}
```

| Variable | Meaning            |
| -------- | ------------------ |
| var1     | CustomerName       |
| var2     | BookingID          |
| var3     | HubName            |
| var4     | HubArrivalDateTime |

**Example payload:**

```bash
curl -X POST 'https://live-mt-server.wati.io/7454/api/v1/sendTemplateMessages' \
  -H 'accept: */*' \
  -H 'Authorization: Bearer <YOUR_TOKEN>' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
    "template_name": "arrived_at_hub",
    "broadcast_name": "hub-arrival-notice",
    "receivers": [
      {
        "whatsappNumber": "918086221201",
        "customParams": [
          { "name": "var1", "value": "Alice" },
          { "name": "var2", "value": "ABC123" },
          { "name": "var3", "value": "Mumbai" },
          { "name": "var4", "value": "2025-05-14 02:30 PM" }
        ]
      }
    ]
  }'
```

---

### 4. `delivered`

**Template text:**

```
✅ *KPN Parcel Update: Delivered*

Hi {{var1}},  
Your parcel (Booking ID: {{var2}}) has been delivered successfully!  
📦 Delivered on: {{var3}}  
📍 Delivered to: {{var4}}
```

| Variable | Meaning          |
| -------- | ---------------- |
| var1     | CustomerName     |
| var2     | BookingID        |
| var3     | DeliveryDateTime |
| var4     | DeliveryAddress  |

**Example payload:**

```bash
curl -X POST 'https://live-mt-server.wati.io/7454/api/v1/sendTemplateMessages' \
  -H 'accept: */*' \
  -H 'Authorization: Bearer <YOUR_TOKEN>' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
    "template_name": "delivered",
    "broadcast_name": "delivery-confirmation",
    "receivers": [
      {
        "whatsappNumber": "918086221201",
        "customParams": [
          { "name": "var1", "value": "Alice" },
          { "name": "var2", "value": "ABC123" },
          { "name": "var3", "value": "2025-05-15 11:45 AM" },
          { "name": "var4", "value": "123 Main St, Mumbai" }
        ]
      }
    ]
  }'
```

---

**Notes for your devs:**

* Replace `<YOUR_TOKEN>` with your actual Bearer token.
* Adjust `broadcast_name` as needed for each send campaign.
* Ensure the template definitions on WATI match the variable placeholders exactly (`var1`, `var2`, etc.).
