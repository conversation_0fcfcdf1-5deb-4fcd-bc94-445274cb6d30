import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { supabase } from "@/lib/supabase";

// GET /api/auth/user-role
export async function GET(request: NextRequest) {
  try {
    // Use the route handler client with cookies for proper session management
    const routeHandlerClient = createRouteHandlerClient({ cookies });

    // Get the session
    const { data: { session }, error: sessionError } = await routeHandlerClient
      .auth.getSession();

    if (sessionError) {
      console.error("Error getting session:", sessionError);
      return NextResponse.json({
        error: "Session error",
        details: sessionError.message,
      }, { status: 500 });
    }

    if (!session) {
      console.log("No session found, returning unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's role using email (since auth.id is UUID and users.user_id is integer)
    const { data: user, error } = await supabase
      .from("users")
      .select("role")
      .eq("email", session.user.email)
      .single();

    if (error) {
      console.error("Error fetching user role:", error);
      return NextResponse.json({
        error: "Failed to fetch user role",
        details: error.message,
      }, {
        status: 500,
      });
    }

    return NextResponse.json({
      role: user?.role || null,
      user_id: session.user.id,
    });
  } catch (error: any) {
    console.error("Error in GET /api/auth/user-role:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error",
    }, {
      status: 500,
    });
  }
}
