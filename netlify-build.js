/**
 * Simple build script for Netlify
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

// Load environment variables
require("dotenv").config({ path: ".env.local" });

console.log("Starting Netlify build process...");

// Ensure Supabase environment variables are set
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_URL = "https://nekjeqxlwhfwyekeinnc.supabase.co";
  console.log("Set default NEXT_PUBLIC_SUPABASE_URL");
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
  console.log("Set default NEXT_PUBLIC_SUPABASE_ANON_KEY");
}

// Create a minimal next.config.js for the build
const nextConfig = `/** @type {import('next').NextConfig} */
module.exports = {
  output: 'export',
  images: { unoptimized: true },
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  swcMinify: false,
  productionBrowserSourceMaps: false,
  transpilePackages: [
    '@radix-ui',
    'lucide-react',
    'cmdk',
    'react-day-picker',
    'embla-carousel-react',
    'vaul',
    'class-variance-authority'
  ]
};`;

// Backup the original next.config.js
const configPath = path.join(process.cwd(), "next.config.js");
const backupPath = path.join(process.cwd(), "next.config.js.bak");

try {
  if (fs.existsSync(configPath)) {
    console.log("Backing up original next.config.js...");
    fs.copyFileSync(configPath, backupPath);
  }

  // Write the minimal config
  console.log("Writing minimal next.config.js for build...");
  fs.writeFileSync(configPath, nextConfig, "utf8");

  // Run the build
  console.log("Running Next.js build...");
  execSync("npx next build", {
    stdio: "inherit",
    env: {
      ...process.env,
      NODE_OPTIONS: "--max-old-space-size=4096",
      NEXT_TELEMETRY_DISABLED: "1"
    }
  });

  console.log("Build completed successfully!");
  process.exit(0);
} catch (error) {
  console.error("Build failed:", error.message);
  process.exit(1);
} finally {
  // Restore the original next.config.js
  if (fs.existsSync(backupPath)) {
    try {
      console.log("Restoring original next.config.js...");
      fs.copyFileSync(backupPath, configPath);
      fs.unlinkSync(backupPath);
    } catch (restoreError) {
      console.error("Error restoring next.config.js:", restoreError.message);
    }
  }
}
