"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { HelpCircle } from "lucide-react"

export function MinimumRateChart() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-6 w-6">
          <HelpCircle className="h-4 w-4" />
          <span className="sr-only">Open minimum rate chart</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Minimum Rate Chart</DialogTitle>
          <DialogDescription>
            Reference chart for minimum rates based on weight and distance.
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[400px] overflow-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Weight Range</TableHead>
                <TableHead>0-50 km</TableHead>
                <TableHead>51-100 km</TableHead>
                <TableHead>101-200 km</TableHead>
                <TableHead>201-500 km</TableHead>
                <TableHead>&gt;500 km</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">0-5 kg</TableCell>
                <TableCell>₹100</TableCell>
                <TableCell>₹150</TableCell>
                <TableCell>₹200</TableCell>
                <TableCell>₹300</TableCell>
                <TableCell>₹400</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">6-10 kg</TableCell>
                <TableCell>₹150</TableCell>
                <TableCell>₹200</TableCell>
                <TableCell>₹250</TableCell>
                <TableCell>₹350</TableCell>
                <TableCell>₹450</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">11-20 kg</TableCell>
                <TableCell>₹200</TableCell>
                <TableCell>₹250</TableCell>
                <TableCell>₹300</TableCell>
                <TableCell>₹400</TableCell>
                <TableCell>₹500</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">21-50 kg</TableCell>
                <TableCell>₹300</TableCell>
                <TableCell>₹350</TableCell>
                <TableCell>₹400</TableCell>
                <TableCell>₹500</TableCell>
                <TableCell>₹600</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">51-100 kg</TableCell>
                <TableCell>₹400</TableCell>
                <TableCell>₹450</TableCell>
                <TableCell>₹500</TableCell>
                <TableCell>₹600</TableCell>
                <TableCell>₹700</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">&gt;100 kg</TableCell>
                <TableCell>₹500</TableCell>
                <TableCell>₹550</TableCell>
                <TableCell>₹600</TableCell>
                <TableCell>₹700</TableCell>
                <TableCell>₹800</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
        <DialogFooter>
          <p className="text-xs text-muted-foreground">
            Note: These are minimum rates. Actual rates may be higher based on parcel type, value, and other factors.
          </p>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
