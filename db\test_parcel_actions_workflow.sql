-- Test Parcel Actions Workflow
-- This script tests the complete parcel workflow to ensure all actions are created correctly

-- Step 1: Create a test parcel to verify the new "Booked" action trigger
INSERT INTO parcels (
  lr_number,
  booking_datetime,
  sender_name,
  sender_phone,
  sender_branch_id,
  recipient_name,
  recipient_phone,
  delivery_branch_id,
  number_of_items,
  item_type,
  weight,
  payment_mode,
  delivery_charges,
  base_price,
  total_amount,
  current_status
) VALUES (
  'TEST-20250529-001',
  NOW(),
  'Test Sender',
  '9876543210',
  10, -- testbranch2
  'Test Recipient',
  '9876543211',
  11, -- test branch3
  3,
  1, -- Assuming type_id 1 exists
  5.5,
  'Paid',
  100.00,
  200.00,
  300.00,
  'Booked'
);

-- Step 2: Get the created parcel ID
DO $$
DECLARE
  v_test_parcel_id INT;
  v_test_chart_id INT;
  v_test_chart_number VARCHAR(50);
BEGIN
  -- Get the test parcel ID
  SELECT parcel_id INTO v_test_parcel_id
  FROM parcels 
  WHERE lr_number = 'TEST-20250529-001';
  
  RAISE NOTICE 'Test parcel created with ID: %', v_test_parcel_id;
  
  -- Verify "Booked" action was created automatically
  IF EXISTS (
    SELECT 1 FROM parcel_actions 
    WHERE parcel_id = v_test_parcel_id 
    AND action_type = 'Booked'
  ) THEN
    RAISE NOTICE '✅ Booked action created successfully';
  ELSE
    RAISE NOTICE '❌ Booked action NOT created';
  END IF;
  
  -- Step 3: Create a test loading chart
  v_test_chart_number := 'TC' || TO_CHAR(NOW(), 'YYYYMMDDHH24MI');
  
  INSERT INTO loading_charts (
    chart_number,
    vehicle_id,
    destination_branch_id,
    loading_type,
    status,
    total_parcels,
    loaded_parcels,
    notes
  ) VALUES (
    v_test_chart_number,
    2, -- Assuming vehicle_id 2 exists
    11, -- test branch3 (destination)
    'Direct',
    'Created',
    1,
    0,
    'Test loading chart'
  ) RETURNING chart_id INTO v_test_chart_id;
  
  RAISE NOTICE 'Test loading chart created with ID: % and number: %', v_test_chart_id, v_test_chart_number;
  
  -- Step 4: Create loading chart item (this should trigger "Loaded" action)
  INSERT INTO loading_chart_items (
    chart_id,
    lr_number,
    quantity,
    status,
    loading_type,
    destination_branch_id
  ) VALUES (
    v_test_chart_id,
    'TEST-20250529-001',
    3,
    'Pending',
    'Direct',
    11
  );
  
  RAISE NOTICE 'Loading chart item created';
  
  -- Verify "Loaded" action was created
  PERFORM pg_sleep(1); -- Wait for trigger to complete
  
  IF EXISTS (
    SELECT 1 FROM parcel_actions 
    WHERE parcel_id = v_test_parcel_id 
    AND action_type = 'Loaded'
  ) THEN
    RAISE NOTICE '✅ Loaded action created successfully';
  ELSE
    RAISE NOTICE '❌ Loaded action NOT created';
  END IF;
  
  -- Step 5: Simulate receiving (update loading chart item status)
  UPDATE loading_chart_items 
  SET status = 'Received', quantity = 3
  WHERE chart_id = v_test_chart_id 
  AND lr_number = 'TEST-20250529-001';
  
  RAISE NOTICE 'Loading chart item marked as received';
  
  -- Verify "Received" action was created
  PERFORM pg_sleep(1); -- Wait for trigger to complete
  
  IF EXISTS (
    SELECT 1 FROM parcel_actions 
    WHERE parcel_id = v_test_parcel_id 
    AND action_type = 'Received'
  ) THEN
    RAISE NOTICE '✅ Received action created successfully';
  ELSE
    RAISE NOTICE '❌ Received action NOT created';
  END IF;
  
  -- Step 6: Show all actions for the test parcel
  RAISE NOTICE '=== Test Parcel Actions Summary ===';
  FOR rec IN 
    SELECT 
      action_type,
      action_timestamp,
      location_name,
      quantity_loaded,
      quantity_received,
      remarks
    FROM parcel_actions 
    WHERE parcel_id = v_test_parcel_id 
    ORDER BY action_timestamp
  LOOP
    RAISE NOTICE '% | % | % | Loaded: % | Received: % | %', 
      rec.action_type,
      rec.action_timestamp,
      COALESCE(rec.location_name, 'NULL'),
      COALESCE(rec.quantity_loaded::text, 'NULL'),
      COALESCE(rec.quantity_received::text, 'NULL'),
      rec.remarks;
  END LOOP;
  
END $$;

-- Step 7: Clean up test data
DELETE FROM loading_chart_items WHERE lr_number = 'TEST-20250529-001';
DELETE FROM loading_charts WHERE chart_number LIKE 'TC%';
DELETE FROM parcel_actions WHERE parcel_id = (SELECT parcel_id FROM parcels WHERE lr_number = 'TEST-20250529-001');
DELETE FROM parcels WHERE lr_number = 'TEST-20250529-001';

-- Step 8: Final verification - show summary of all parcel actions
SELECT 
  'FINAL SUMMARY' as status,
  action_type,
  COUNT(*) as total_actions,
  COUNT(DISTINCT parcel_id) as unique_parcels,
  COUNT(CASE WHEN location_name IS NOT NULL THEN 1 END) as actions_with_location,
  COUNT(CASE WHEN remarks LIKE '%Status updated to%' THEN 1 END) as generic_status_actions
FROM parcel_actions 
GROUP BY action_type 
ORDER BY action_type;
