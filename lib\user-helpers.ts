import { createBrowserClient, supabase } from "./supabase";
import type { Database } from "./database.types";

// User types
export type User = {
  user_id: number;
  name: string;
  email: string;
  phone?: string | null;
  role: "Super Admin" | "Admin" | "Manager";
  branch_id?: number | null;
  notification_preferences?: any | null;
  two_fa_enabled: boolean;
  login_history?: any | null;
};

export type NewUser = Partial<User>;
export type UpdateUser = Partial<User>;

/**
 * Get the current authenticated user with profile data from the public users table
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    // Use browser client for auth operations
    const browserClient = createBrowserClient();

    // First get the auth user
    const { data: { user: authUser }, error: authError } = await browserClient
      .auth
      .getUser();

    if (authError || !authUser) {
      console.error("Error getting authenticated user:", authError);
      return null;
    }

    // Then get the user profile from the public users table by email
    // Since we can't rely on the ID being the same
    const { data: profileData, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("email", authUser.email)
      .single();

    if (profileError) {
      console.error("Error getting user profile:", profileError);

      // If the user doesn't exist in the public table, create it
      console.log("User not found in public table, creating...");
      return createUserInPublicTable(
        authUser.id,
        authUser.email || "",
        authUser.user_metadata?.name || authUser.user_metadata?.full_name ||
          "User",
        "Manager",
      );
    }

    return profileData as User;
  } catch (error: any) {
    console.error("Unexpected error getting current user:", error);
    return null;
  }
}

/**
 * Get a user by ID
 */
export async function getUserById(id: string | number): Promise<User | null> {
  try {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("user_id", id)
      .single();

    if (error) {
      console.error("Error getting user by ID:", error);
      return null;
    }

    return data as User;
  } catch (error: any) {
    console.error("Unexpected error getting user by ID:", error);
    return null;
  }
}

/**
 * Get all users (with optional filtering)
 */
export async function getUsers(
  filters: Partial<User> = {},
): Promise<User[] | null> {
  try {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .match(filters)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error getting users:", error);
      return null;
    }

    return data as User[];
  } catch (error: any) {
    console.error("Unexpected error getting users:", error);
    return null;
  }
}

/**
 * Update a user's profile
 */
export async function updateUserProfile(
  id: string | number,
  updates: UpdateUser,
): Promise<User | null> {
  try {
    const { data, error } = await supabase
      .from("users")
      .update(updates)
      .eq("user_id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating user profile:", error);
      return null;
    }

    return data as User;
  } catch (error: any) {
    console.error("Unexpected error updating user profile:", error);
    return null;
  }
}

/**
 * Update the current user's last login timestamp
 */
export async function updateLastLogin(): Promise<boolean> {
  try {
    // Use browser client for auth operations
    const browserClient = createBrowserClient();
    const { data: { user } } = await browserClient.auth.getUser();

    if (!user) {
      console.warn("No authenticated user found when updating last login");
      return false;
    }

    console.log("Updating last login for user:", user.email);

    // Find the user by email
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("user_id")
      .eq("email", user.email)
      .single();

    if (userError) {
      console.error("Error finding user by email:", userError);

      // If the user doesn't exist in the public table, create it
      console.log(
        "User not found in public table, creating before updating last login...",
      );
      const newUser = await createUserInPublicTable(
        user.id,
        user.email || "",
        user.user_metadata?.name || user.user_metadata?.full_name || "User",
        "Manager",
      );

      if (!newUser) {
        console.error("Failed to create user in public table");
        return false;
      }

      // Update the login_history for the newly created user
      const { error: updateError } = await supabase
        .from("users")
        .update({
          login_history: JSON.stringify({
            last_login: new Date().toISOString(),
            login_count: 1,
          }),
        })
        .eq("user_id", newUser.user_id);

      if (updateError) {
        console.error("Error updating last login for new user:", updateError);
        return false;
      }

      return true;
    }

    // Update the login_history field for existing user
    const { error } = await supabase
      .from("users")
      .update({
        login_history: JSON.stringify({
          last_login: new Date().toISOString(),
          login_count: 1,
        }),
      })
      .eq("user_id", userData.user_id);

    if (error) {
      console.error("Error updating last login:", error);
      return false;
    }

    console.log("Last login updated successfully");
    return true;
  } catch (error: any) {
    console.error("Unexpected error updating last login:", error);
    return false;
  }
}

/**
 * Get the numeric user_id from an auth_id (UUID)
 * @param authId The UUID from the auth.users table
 * @returns The numeric user_id or null if not found
 */
export async function getUserIdFromAuthId(
  authId: string,
): Promise<number | null> {
  if (!authId) return null;

  try {
    // First check if auth_id column exists
    const { data: hasAuthIdColumn } = await supabase.rpc(
      "check_column_exists",
      {
        table_name: "users",
        column_name: "auth_id",
      },
    );

    // If auth_id column exists, query by auth_id
    if (hasAuthIdColumn) {
      const { data, error } = await supabase
        .from("users")
        .select("user_id")
        .eq("auth_id", authId)
        .single();

      if (!error && data?.user_id) {
        return data.user_id;
      }
    }

    // Fallback: try to find by email using the auth user
    const { data: authUser } = await supabase.auth.getUser(authId);
    if (authUser?.user?.email) {
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("user_id")
        .eq("email", authUser.user.email)
        .single();

      if (!userError && userData?.user_id) {
        return userData.user_id;
      }
    }

    return null;
  } catch (error: any) {
    console.error("Exception in getUserIdFromAuthId:", error);
    return null;
  }
}

/**
 * Create a new user in the public users table
 */
export async function createUserInPublicTable(
  userId: string,
  email: string,
  name: string,
  role: string = "Manager", // Default to Manager role
): Promise<User | null> {
  try {
    // Make sure role is one of the valid enum values
    const validRoles = ["Super Admin", "Admin", "Manager"];
    const safeRole = validRoles.includes(role) ? role : "Manager";

    console.log(
      `Creating user in public table: ${email}, ${name}, ${safeRole}`,
    );

    // First, check if the user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from("users")
      .select("*")
      .eq("email", email)
      .maybeSingle();

    if (existingUser) {
      console.log("User already exists in public table:", existingUser);

      // Check if we need to update the auth_id
      const { data: hasAuthIdColumn } = await supabase.rpc(
        "check_column_exists",
        {
          table_name: "users",
          column_name: "auth_id",
        },
      );

      if (hasAuthIdColumn && !existingUser.auth_id) {
        // Update the auth_id for this user
        const { data: updatedUser, error: updateError } = await supabase
          .from("users")
          .update({ auth_id: userId })
          .eq("user_id", existingUser.user_id)
          .select()
          .single();

        if (!updateError) {
          console.log("Updated auth_id for existing user:", updatedUser);
          return updatedUser as User;
        }
      }

      return existingUser as User;
    }

    // Check if auth_id column exists
    const { data: hasAuthIdColumn } = await supabase.rpc(
      "check_column_exists",
      {
        table_name: "users",
        column_name: "auth_id",
      },
    );

    // Generate a new user_id (don't try to use the auth user ID)
    // This is important because the auth.users.id is a UUID, but our users.user_id is an integer
    const { data, error } = await supabase
      .from("users")
      .insert([
        {
          // Don't try to set user_id, let the database generate it
          email,
          name,
          role: safeRole,
          two_fa_enabled: false,
          ...(hasAuthIdColumn ? { auth_id: userId } : {}),
        },
      ])
      .select()
      .single();

    if (error) {
      console.error("Error creating user in public table:", error);

      // If the error is about the user already existing, try to fetch the user
      if (error.code === "23505") { // Unique violation
        console.log("User might already exist, trying to fetch...");
        const { data: existingUser, error: fetchError } = await supabase
          .from("users")
          .select("*")
          .eq("email", email)
          .single();

        if (!fetchError && existingUser) {
          console.log("Found existing user:", existingUser);
          return existingUser as User;
        }
      }

      return null;
    }

    console.log("User created successfully:", data);
    return data as User;
  } catch (error: any) {
    console.error("Unexpected error creating user in public table:", error);
    return null;
  }
}

/**
 * Assign a user to a branch
 */
export async function assignUserToBranch(
  userId: string | number,
  branchId: string | number,
): Promise<User | null> {
  return updateUserProfile(userId, { branch_id: branchId });
}

/**
 * Change a user's role
 */
export async function changeUserRole(
  userId: string | number,
  role: string,
): Promise<User | null> {
  // Make sure role is one of the valid enum values
  const validRoles = ["Super Admin", "Admin", "Manager"];
  const safeRole = validRoles.includes(role) ? role : "Manager";

  return updateUserProfile(userId, { role: safeRole });
}

/**
 * Enable or disable two-factor authentication for a user
 */
export async function setTwoFactorEnabled(
  userId: string | number,
  enabled: boolean,
): Promise<User | null> {
  return updateUserProfile(userId, { two_fa_enabled: enabled });
}
