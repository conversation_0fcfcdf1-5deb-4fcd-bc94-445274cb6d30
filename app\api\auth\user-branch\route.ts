import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { supabase } from "@/lib/supabase";

// GET /api/auth/user-branch
export async function GET(request: NextRequest) {
  try {
    // Use the route handler client with cookies for proper session management
    const routeHandlerClient = createRouteHandlerClient({ cookies });

    // Get the session
    const { data: { session }, error: sessionError } = await routeHandlerClient
      .auth.getSession();

    if (sessionError) {
      console.error("Error getting session:", sessionError);
      return NextResponse.json({
        error: "Session error",
        details: sessionError.message,
      }, { status: 500 });
    }

    if (!session) {
      // Return unauthorized instead of a hardcoded branch
      console.log("No session found, returning unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's branch ID using email (since auth.id is UUID and users.user_id is integer)
    const { data: user, error } = await supabase
      .from("users")
      .select("branch_id, email, role")
      .eq("email", session.user.email)
      .single();

    if (error) {
      console.error("Error fetching user branch:", error);
      return NextResponse.json({
        error: "Failed to fetch user branch",
        details: error.message,
        user_id: session.user.id,
      }, {
        status: 500,
      });
    }

    return NextResponse.json({
      branch_id: user?.branch_id || null,
      email: user?.email || null,
      role: user?.role || null,
      user_id: session.user.id,
    });
  } catch (error: any) {
    console.error("Error in GET /api/auth/user-branch:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error",
    }, {
      status: 500,
    });
  }
}
