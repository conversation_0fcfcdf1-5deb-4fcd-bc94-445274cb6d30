"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useSupabase } from "@/contexts/supabase-provider";

export function useSupabaseAuth() {
  const { supabase, user } = useSupabase();
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  // Sign in with email and password
  const signIn = async (
    email: string,
    password: string,
  ) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Error signing in",
          description: error.message,
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Signed in successfully",
        description: "Welcome back!",
      });

      // Let the middleware handle redirection
      return true;
    } catch (error: any) {
      console.error("Error signing in:", error);
      toast({
        title: "Error signing in",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (
    email: string,
    password: string,
    metadata: any = {},
  ) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        toast({
          title: "Error signing up",
          description: error.message,
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Signed up successfully",
        description: "Please check your email for confirmation",
      });
      return true;
    } catch (error: any) {
      console.error("Error signing up:", error);
      toast({
        title: "Error signing up",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);

      // First, clear any local storage items that might be related to the session
      try {
        // Clear Supabase-related items from localStorage
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith("supabase.") || key.includes("auth"))) {
            localStorage.removeItem(key);
          }
        }
      } catch (e: any) {
        console.warn("Error clearing localStorage:", e);
      }

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error("Supabase signOut error:", error);
        toast({
          title: "Error signing out",
          description: error.message,
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Signed out successfully",
      });

      // Use a hard redirect instead of Next.js router to avoid chunk loading issues
      // Add a small delay to allow the toast to be seen
      setTimeout(() => {
        // Force a full page reload to clear any cached state
        window.location.href = "/login";
      }, 500);

      return true;
    } catch (error: any) {
      console.error("Error signing out:", error);
      toast({
        title: "Error signing out",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);

      // First validate if the email exists in our system
      const validateResponse = await fetch(
        "/api/auth/validate-email-for-reset",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email: email.trim().toLowerCase() }),
        },
      );

      const validateData = await validateResponse.json();

      if (!validateData.valid) {
        toast({
          title: "Email validation failed",
          description: validateData.message,
          variant: "destructive",
        });
        return false;
      }

      // If email is valid, proceed with password reset
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        toast({
          title: "Error resetting password",
          description: error.message,
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Password reset email sent",
        description: "Please check your email for the reset link",
      });
      return true;
    } catch (error: any) {
      console.error("Error resetting password:", error);
      toast({
        title: "Error resetting password",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
}
