"use client"

import { usePathname, useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Sheet, SheetContent } from '@/components/ui/sheet'
import { Package, Truck, IndianRupee, FileText, Activity, BarChart3, Users, Settings } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { NavigationLink } from '@/components/navigation-link'

const mainItems = [
  {
    title: "Dashboard",
    href: "/",
    icon: Package,
  },
  {
    title: "Parcels",
    href: "/parcels",
    icon: Package,
  },
  {
    title: "Load & Receive",
    href: "/load-receive",
    icon: Truck,
  },
  {
    title: "Memos",
    href: "/memos",
    icon: FileText,
  },
  {
    title: "Accounts",
    href: "/operations",
    icon: IndianRupee,
  }
]

const operationsItems = [
  {
    title: "Expenses",
    href: "/operations/expenses",
    icon: FileText,
  },
  {
    title: "Reports",
    href: "/operations/reports",
    icon: BarChart3,
  },
]

const adminItems = [
  {
    title: "Admin Dashboard",
    href: "/admin",
    icon: Settings,
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
  {
    title: "Activity Log",
    href: "/activity",
    icon: Activity,
  },
]

export function MobileNav({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const pathname = usePathname()
  const [userRole, setUserRole] = useState<string | null>(null)

  // Fetch user role on component mount
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const response = await fetch('/api/auth/user-role')
        if (response.ok) {
          const data = await response.json()
          setUserRole(data.role)
        }
      } catch (error) {
        console.error('Error fetching user role:', error)
      }
    }

    if (isOpen) {
      fetchUserRole()
    }
  }, [isOpen])

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="left" className="w-[240px] sm:w-[280px] bg-white border-r border-gray-100">
        <div className="px-2 py-6">
          <NavigationLink
            href="/"
            className="flex items-center space-x-2 text-primary"
            onClick={onClose}
          >
            <Package className="h-6 w-6" />
            <span className="font-bold">Branch</span>
          </NavigationLink>
          <nav className="mt-6 flex flex-col space-y-6">
            <div className="space-y-3">
              <h4 className="px-2 text-sm font-medium text-primary">Main Navigation</h4>
              {mainItems.map((item) => {
                const Icon = item.icon
                return (
                  <NavigationLink
                    key={item.href}
                    href={item.href}
                    onClick={onClose}
                    className={cn(
                      "flex items-center space-x-2 px-2 py-1 text-sm transition-colors hover:text-foreground/80",
                      pathname === item.href
                        ? "text-foreground"
                        : "text-foreground/60"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </NavigationLink>
                )
              })}
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="px-2 text-sm font-medium text-primary">Operations</h4>
              {operationsItems.map((item) => {
                const Icon = item.icon
                return (
                  <NavigationLink
                    key={item.href}
                    href={item.href}
                    onClick={onClose}
                    className={cn(
                      "flex items-center space-x-2 px-2 py-1 text-sm transition-colors hover:text-foreground/80",
                      pathname === item.href
                        ? "text-foreground"
                        : "text-foreground/60"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </NavigationLink>
                )
              })}
            </div>

            {(userRole === "Admin" || userRole === "Super Admin") && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="px-2 text-sm font-medium text-primary">Administration</h4>
                  {adminItems.map((item) => {
                    const Icon = item.icon
                    return (
                      <NavigationLink
                        key={item.href}
                        href={item.href}
                        onClick={onClose}
                        className={cn(
                          "flex items-center space-x-2 px-2 py-1 text-sm transition-colors hover:text-foreground/80",
                          pathname === item.href
                            ? "text-foreground"
                            : "text-foreground/60"
                        )}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </NavigationLink>
                    )
                  })}
                </div>
              </>
            )}
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  )
}
