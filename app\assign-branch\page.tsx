"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { createBrowserClient } from "@/lib/supabase"

export default function AssignBranchPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [userData, setUserData] = useState<any>(null)
  const [branches, setBranches] = useState<any[]>([])
  const [selectedBranch, setSelectedBranch] = useState<string>("")
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const [supabase] = useState(() => createBrowserClient())

  useEffect(() => {
    fetchUserData()
    fetchBranches()

    // Check if the user is an admin, if not redirect to dashboard
    const checkUserRole = async () => {
      try {
        console.log("Checking user role in assign-branch page...")
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error("Authentication error:", authError)
          setError("Authentication error. Please try logging in again.")
          setIsLoading(false)
          return
        }

        if (!user) {
          console.error("No user found in auth state")
          setError("No user found. Please try logging in again.")
          setIsLoading(false)
          return
        }

        console.log("Auth user found:", user.id, user.email)

        // Use email for lookup since it's the common identifier between auth and public users table
        console.log("Looking up user by email:", user.email)

        const { data: profileData, error: profileError } = await supabase
          .from("users")
          .select("role")
          .eq("email", user.email)
          .single()

        if (profileError) {
          console.error("Error fetching user profile by email:", profileError)
          setError("Could not find your user profile. Please contact your administrator.")
          setIsLoading(false)
          return
        }

        console.log("User found by email, role:", profileData.role)

        // If user is not an admin, redirect to dashboard
        if (profileData.role !== "Super Admin" && profileData.role !== "Admin") {
          console.log("User is not an admin, redirecting to dashboard")
          window.location.href = "/"
        }
      } catch (error: any) {
        console.error("Error checking user role:", error)
        setError("An unexpected error occurred. Please try again later.")
        setIsLoading(false)
      }
    }

    checkUserRole()
  }, [])

  const fetchUserData = async () => {
    try {
      console.log("Fetching user data in assign-branch page...")

      // Get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError) {
        console.error("Authentication error:", authError)
        setError("Authentication error. Please try logging in again.")
        setIsLoading(false)
        return
      }

      if (!user) {
        console.error("No user found in auth state")
        setError("No user found. Please try logging in again.")
        setIsLoading(false)
        return
      }

      console.log("Auth user found:", user.id, user.email)

      // Use email for lookup since it's the common identifier between auth and public users table
      console.log("Looking up user by email:", user.email)

      const { data: profileData, error: profileError } = await supabase
        .from("users")
        .select("*")
        .eq("email", user.email)
        .single()

      if (profileError) {
        console.error("Error fetching user profile by email:", profileError)
        setError("Could not find your user profile. Please contact your administrator.")
        setIsLoading(false)
        return
      }

      console.log("User found by email:", profileData)

      setUserData({
        ...profileData,
        auth_id: user.id,
        auth_email: user.email
      })

      if (profileData.branch_id) {
        setSelectedBranch(profileData.branch_id.toString())
      }
    } catch (error: any) {
      console.error("Error fetching user data:", error)
      setError("An unexpected error occurred. Please try again later.")
    } finally {
      setIsLoading(false)
    }
  }

  const fetchBranches = async () => {
    try {
      const { data, error } = await supabase
        .from("branches")
        .select("*")
        .eq("status", "Active")
        .order("name", { ascending: true })

      if (error) {
        console.error("Error fetching branches:", error)
        toast({
          title: "Error",
          description: "Failed to load branches. Please try again.",
          variant: "destructive",
        })
      } else {
        setBranches(data || [])
      }
    } catch (error: any) {
      console.error("Error fetching branches:", error)
      toast({
        title: "Error",
        description: "Failed to load branches. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleAssignBranch = async () => {
    if (!selectedBranch || !userData?.user_id) {
      toast({
        title: "Error",
        description: "Please select a branch first.",
        variant: "destructive",
      })
      return
    }

    setIsSaving(true)
    try {
      // Update the user's branch_id in the public users table
      const { data, error } = await supabase
        .from("users")
        .update({ branch_id: parseInt(selectedBranch, 10) })
        .eq("user_id", userData.user_id)
        .select()
        .single()

      if (error) {
        console.error("Error assigning branch:", error)
        toast({
          title: "Error",
          description: "Failed to assign branch. Please try again.",
          variant: "destructive",
        })
      } else {
        setUserData({
          ...userData,
          branch_id: parseInt(selectedBranch, 10)
        })

        toast({
          title: "Success",
          description: "Branch selected successfully. You will now manage this branch.",
        })

        // Redirect to the dashboard after a short delay
        setTimeout(() => {
          window.location.href = "/"
        }, 1500)
      }
    } catch (error: any) {
      console.error("Error assigning branch:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Admin Branch Selection</CardTitle>
          <CardDescription>
            As an admin, you can select which branch you want to manage
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading user data...</span>
            </div>
          ) : error ? (
            <div className="p-4 bg-destructive/10 text-destructive rounded-md">
              <h3 className="font-bold">Error</h3>
              <p>{error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">User Information</h3>
                <p><span className="font-medium">User ID:</span> {userData?.user_id || 'Not found'}</p>
                <p><span className="font-medium">Name:</span> {userData?.name || 'Not found'}</p>
                <p><span className="font-medium">Email:</span> {userData?.email || 'Not found'}</p>
                <p><span className="font-medium">Role:</span> {userData?.role || 'Not found'}</p>
                <p><span className="font-medium">Current Branch:</span> {
                  userData?.branch_id
                    ? branches.find(b => b.branch_id === userData.branch_id)?.name || `ID: ${userData.branch_id}`
                    : 'Not assigned'
                }</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="branch">Select Branch</Label>
                <Select
                  value={selectedBranch}
                  onValueChange={setSelectedBranch}
                  disabled={isSaving}
                >
                  <SelectTrigger id="branch">
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem
                        key={branch.branch_id}
                        value={branch.branch_id.toString()}
                      >
                        {branch.name} ({branch.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleAssignBranch}
            disabled={isLoading || isSaving || !selectedBranch}
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Select Branch'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
