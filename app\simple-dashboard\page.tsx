'use client'

import { useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface UserData {
  id: string
  email: string
  user_metadata: any
}

interface BranchData {
  branch_id: number
  name: string
  code: string
  status: string
  branch_type: string
}

interface ProfileData {
  user_id: number
  name: string
  email: string
  role: string
  branch_id: number | null
}

export default function SimpleDashboardPage() {
  const [user, setUser] = useState<UserData | null>(null)
  const [profile, setProfile] = useState<ProfileData | null>(null)
  const [branch, setBranch] = useState<BranchData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const supabase = createClientComponentClient()
  
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get the current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (sessionError) {
          throw sessionError
        }
        
        if (!session) {
          // Redirect to login if not authenticated
          window.location.href = '/simple-login'
          return
        }
        
        // Set the user data
        setUser({
          id: session.user.id,
          email: session.user.email || '',
          user_metadata: session.user.user_metadata
        })
        
        // Get the user profile from the public users table
        const { data: profileData, error: profileError } = await supabase
          .from('users')
          .select('*')
          .eq('email', session.user.email)
          .single()
        
        if (profileError) {
          console.error('Error fetching user profile:', profileError)
        } else {
          setProfile(profileData)
          
          // If the user has a branch, get the branch details
          if (profileData.branch_id) {
            const { data: branchData, error: branchError } = await supabase
              .from('branches')
              .select('*')
              .eq('branch_id', profileData.branch_id)
              .single()
            
            if (branchError) {
              console.error('Error fetching branch:', branchError)
            } else {
              setBranch(branchData)
            }
          }
        }
      } catch (err: any) {
        console.error('Error checking authentication:', err)
        setError('Error checking authentication')
      } finally {
        setLoading(false)
      }
    }
    
    checkAuth()
  }, [supabase])
  
  const handleLogout = async () => {
    setLoading(true)
    
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        throw error
      }
      
      // Redirect to login
      window.location.href = '/simple-login'
    } catch (err: any) {
      console.error('Error signing out:', err)
      setError('Error signing out')
      setLoading(false)
    }
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-xl text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => window.location.href = '/simple-login'}>
              Go to Login
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-primary">KPN Branch Management</h1>
          <Button onClick={handleLogout}>Sign Out</Button>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
              <CardDescription>Your account details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {user && (
                <>
                  <div>
                    <span className="font-medium">Email:</span> {user.email}
                  </div>
                  <div>
                    <span className="font-medium">ID:</span> {user.id}
                  </div>
                  <div>
                    <span className="font-medium">Name:</span> {user.user_metadata?.name || 'Not set'}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Your profile details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {profile ? (
                <>
                  <div>
                    <span className="font-medium">Name:</span> {profile.name}
                  </div>
                  <div>
                    <span className="font-medium">Role:</span> {profile.role}
                  </div>
                  <div>
                    <span className="font-medium">User ID:</span> {profile.user_id}
                  </div>
                </>
              ) : (
                <p>No profile information found.</p>
              )}
            </CardContent>
          </Card>
          
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Branch Information</CardTitle>
              <CardDescription>Your assigned branch</CardDescription>
            </CardHeader>
            <CardContent>
              {branch ? (
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Branch Name:</span> {branch.name}
                  </div>
                  <div>
                    <span className="font-medium">Branch Code:</span> {branch.code}
                  </div>
                  <div>
                    <span className="font-medium">Branch Type:</span> {branch.branch_type}
                  </div>
                  <div>
                    <span className="font-medium">Status:</span> {branch.status}
                  </div>
                </div>
              ) : (
                <p>You are not assigned to any branch.</p>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={() => window.location.href = '/'} variant="outline">
                Go to Main Dashboard
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}
