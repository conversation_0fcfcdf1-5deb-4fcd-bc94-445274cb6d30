/**
 * Simplified build script for Netlify
 * This script creates a minimal version of the app without API routes
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
const glob = require("glob");

// Load environment variables
require("dotenv").config({ path: ".env.local" });

console.log("Starting simplified Netlify build process...");

// Ensure Supabase environment variables are set
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_URL =
    "https://nekjeqxlwhfwyekeinnc.supabase.co";
  console.log("Set default NEXT_PUBLIC_SUPABASE_URL");
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
  console.log("Set default NEXT_PUBLIC_SUPABASE_ANON_KEY");
}

// Create a directory for the simplified app
const tempDir = path.join(process.cwd(), "temp-app");
if (fs.existsSync(tempDir)) {
  console.log("Removing existing temp directory...");
  fs.rmSync(tempDir, { recursive: true, force: true });
}

console.log("Creating temp directory for simplified app...");
fs.mkdirSync(tempDir, { recursive: true });

// Copy essential files to the temp directory
console.log("Copying essential files...");
const essentialDirs = [
  "components",
  "contexts",
  "hooks",
  "lib",
  "public",
  "styles",
  "app",
];

essentialDirs.forEach((dir) => {
  if (fs.existsSync(path.join(process.cwd(), dir))) {
    fs.cpSync(
      path.join(process.cwd(), dir),
      path.join(tempDir, dir),
      { recursive: true },
    );
  }
});

// Copy package.json and other config files
const configFiles = [
  "package.json",
  "package-lock.json",
  "tsconfig.json",
  ".env.local",
  "tailwind.config.ts",
  "tailwind.config.js",
  "postcss.config.js",
];

configFiles.forEach((file) => {
  if (fs.existsSync(path.join(process.cwd(), file))) {
    fs.copyFileSync(
      path.join(process.cwd(), file),
      path.join(tempDir, file),
    );
  }
});

// Remove API routes
console.log("Removing API routes...");
const apiDir = path.join(tempDir, "app", "api");
if (fs.existsSync(apiDir)) {
  fs.rmSync(apiDir, { recursive: true, force: true });
}

// Create a minimal next.config.js for static export
const nextConfig = `/** @type {import('next').NextConfig} */
module.exports = {
  output: 'export',
  images: { unoptimized: true },
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  swcMinify: false,
  productionBrowserSourceMaps: false,
  transpilePackages: [
    '@radix-ui',
    'lucide-react',
    'cmdk',
    'react-day-picker',
    'embla-carousel-react',
    'vaul',
    'class-variance-authority'
  ]
};`;

// Write the minimal config
console.log("Writing minimal next.config.js...");
fs.writeFileSync(path.join(tempDir, "next.config.js"), nextConfig, "utf8");

// Create a simple index page that redirects to the dashboard
const indexPage = `"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function Home() {
  const router = useRouter()

  useEffect(() => {
    router.push("/dashboard")
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <p className="text-lg">Loading...</p>
    </div>
  )
}`;

// Write the index page
console.log("Writing simplified index page...");
fs.writeFileSync(path.join(tempDir, "app", "page.tsx"), indexPage, "utf8");

// Run the build in the temp directory
console.log("Running Next.js build in temp directory...");
try {
  const originalDir = process.cwd();
  process.chdir(tempDir);

  execSync("npm install --no-save", {
    stdio: "inherit",
    env: {
      ...process.env,
      NODE_OPTIONS: "--max-old-space-size=4096",
      NEXT_TELEMETRY_DISABLED: "1",
    },
  });

  execSync("npx next build", {
    stdio: "inherit",
    env: {
      ...process.env,
      NODE_OPTIONS: "--max-old-space-size=4096",
      NEXT_TELEMETRY_DISABLED: "1",
    },
  });

  // Copy the output to the original directory
  console.log("Copying output to original directory...");
  process.chdir(originalDir);

  if (fs.existsSync(path.join(originalDir, "out"))) {
    fs.rmSync(path.join(originalDir, "out"), { recursive: true, force: true });
  }

  fs.cpSync(
    path.join(tempDir, "out"),
    path.join(originalDir, "out"),
    { recursive: true },
  );

  console.log("Build completed successfully!");
  process.exit(0);
} catch (error) {
  console.error("Build failed:", error.message);
  process.exit(1);
}
