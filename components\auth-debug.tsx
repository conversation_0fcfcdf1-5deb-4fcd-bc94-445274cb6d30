'use client'

import { useEffect, useState } from 'react'
import { useSupabase } from '@/contexts/supabase-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'

export function AuthDebug() {
  const { supabase, user, session } = useSupabase()
  const [isVisible, setIsVisible] = useState(true)
  const { toast } = useToast()
  
  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.getSession()
      if (error) {
        console.error('Error refreshing session:', error)
        toast({
          title: 'Error refreshing session',
          description: error.message,
          variant: 'destructive',
        })
      } else {
        console.log('Session refreshed:', data)
        toast({
          title: 'Session refreshed',
          description: 'Session data updated',
        })
      }
    } catch (error: any) {
      console.error('Unexpected error refreshing session:', error)
    }
  }
  
  if (!isVisible) return null
  
  return (
    <Card className="mb-4 border-yellow-500 bg-yellow-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex justify-between">
          <span>Authentication Debug</span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsVisible(false)}
            className="h-6 px-2 text-xs"
          >
            Hide
          </Button>
        </CardTitle>
        <CardDescription>Current authentication state</CardDescription>
      </CardHeader>
      <CardContent className="pb-2 text-sm">
        <div className="space-y-2">
          <div>
            <strong>User:</strong> {user ? 'Authenticated' : 'Not authenticated'}
          </div>
          {user && (
            <>
              <div>
                <strong>Email:</strong> {user.email}
              </div>
              <div>
                <strong>User ID:</strong> {user.id}
              </div>
              <div>
                <strong>Metadata:</strong> {JSON.stringify(user.user_metadata)}
              </div>
            </>
          )}
          <div>
            <strong>Session:</strong> {session ? 'Active' : 'No session'}
          </div>
          {session && (
            <>
              <div>
                <strong>Expires:</strong> {new Date(session.expires_at * 1000).toLocaleString()}
              </div>
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button size="sm" onClick={refreshSession}>Refresh Session</Button>
      </CardFooter>
    </Card>
  )
}
