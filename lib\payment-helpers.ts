import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

/**
 * Creates a financial transaction for a parcel payment
 *
 * @param branchId - The ID of the branch where the payment was collected
 * @param amount - The payment amount
 * @param paymentMethod - The payment method (cash, online, upi, card)
 * @param lrNumber - The LR number of the parcel
 * @param description - Optional description for the transaction
 * @returns The created transaction or null if there was an error
 */
export async function createParcelPaymentTransaction(
  branchId: number,
  amount: number,
  paymentMethod: string,
  lrNumber: string,
  description?: string,
) {
  try {
    const supabase = createClientComponentClient();

    // Determine if this payment should be added to branch balance
    // Only cash payments are added to branch balance
    const isCashPayment = paymentMethod.toLowerCase() === "cash";

    // For online payments, we still create a transaction but mark it differently
    // This allows tracking all payments while only including cash in branch balance
    const transactionData = {
      branch_id: branchId,
      transaction_type: "Collection",
      amount: amount,
      payment_method: paymentMethod,
      reference_number: lrNumber,
      description: description ||
        `Payment for parcel ${lrNumber} via ${paymentMethod}`,
      approval_status: "Approved",
      status: "Approved", // Set both status and approval_status
      transaction_date: new Date().toISOString(),
      // Add a flag to indicate if this should be included in branch balance
      // This will be used when calculating branch balance
      metadata: JSON.stringify({
        include_in_branch_balance: isCashPayment,
        payment_type: isCashPayment ? "branch_collection" : "online_payment",
      }),
    };

    // Create the transaction
    // First try with metadata
    let data;
    let error;

    try {
      const result = await supabase
        .from("financial_transactions")
        .insert(transactionData)
        .select()
        .single();

      data = result.data;
      error = result.error;
    } catch (e) {
      console.error("Error with metadata, trying without:", e);
      error = e;
    }

    // If there's an error related to metadata column, try without it
    if (
      error &&
      (error.message?.includes("metadata") || error.code === "PGRST204")
    ) {
      console.warn("Metadata column might not exist, trying without metadata");

      // Remove metadata from transaction data
      const { metadata, ...transactionDataWithoutMetadata } = transactionData;

      // Try again without metadata
      const result = await supabase
        .from("financial_transactions")
        .insert(transactionDataWithoutMetadata)
        .select()
        .single();

      data = result.data;
      error = result.error;

      if (!error) {
        // Show a warning to run the migration
        console.warn(
          "Transaction created without metadata. Please run the migration to add the metadata column.",
        );
      }
    }

    if (error) {
      console.error("Error creating payment transaction:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error in createParcelPaymentTransaction:", error);
    return null;
  }
}

/**
 * Creates a financial transaction via the API route
 * Use this when you need to ensure server-side validation
 *
 * @param branchId - The ID of the branch where the payment was collected
 * @param amount - The payment amount
 * @param paymentMethod - The payment method (cash, online, upi, card)
 * @param lrNumber - The LR number of the parcel
 * @param description - Optional description for the transaction
 * @returns The created transaction or null if there was an error
 */
export async function createParcelPaymentTransactionViaAPI(
  branchId: number,
  amount: number,
  paymentMethod: string,
  lrNumber: string,
  description?: string,
) {
  try {
    // Determine if this payment should be added to branch balance
    const isCashPayment = paymentMethod.toLowerCase() === "cash";

    // First try with metadata
    let requestBody = {
      branch_id: branchId,
      transaction_type: "Collection",
      amount: amount,
      payment_mode: paymentMethod,
      reference_number: lrNumber,
      description: description ||
        `Payment for parcel ${lrNumber} via ${paymentMethod}`,
      approval_status: "Approved",
      status: "Approved", // Set both status and approval_status
      metadata: {
        include_in_branch_balance: isCashPayment,
        payment_type: isCashPayment ? "branch_collection" : "online_payment",
      },
    };

    let response = await fetch("/api/financial-transactions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    // If there's an error, try without metadata
    if (!response.ok && response.status === 400) {
      console.warn("Metadata column might not exist, trying without metadata");

      // Remove metadata from request body
      const { metadata, ...requestBodyWithoutMetadata } = requestBody;

      response = await fetch("/api/financial-transactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBodyWithoutMetadata),
      });

      if (response.ok) {
        console.warn(
          "Transaction created without metadata. Please run the migration to add the metadata column.",
        );
      }
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error || "Failed to create payment transaction",
      );
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in createParcelPaymentTransactionViaAPI:", error);
    return null;
  }
}
