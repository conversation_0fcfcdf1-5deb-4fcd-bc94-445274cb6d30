/**
 * Utility functions for working with branch data
 */

// Cache for branch and role data to avoid excessive API calls
let branchCache: { id: number | null; timestamp: number } = {
  id: null,
  timestamp: 0,
};
let roleCache: { role: string | null; timestamp: number } = {
  role: null,
  timestamp: 0,
};
const CACHE_TTL = 60000; // 1 minute cache TTL

/**
 * Get the user's branch ID from API or cache
 * @returns The user's branch ID or null if not available
 */
export async function getUserBranchIdAsync(): Promise<number | null> {
  // Check if we have a valid cached value
  const now = Date.now();
  if (branchCache.id !== null && now - branchCache.timestamp < CACHE_TTL) {
    return branchCache.id;
  }

  try {
    const response = await fetch("/api/auth/user-branch");

    if (!response.ok) {
      console.error("Error fetching user branch:", response.statusText);

      // Try to get from localStorage as fallback
      if (typeof window !== "undefined") {
        const localBranchId = localStorage.getItem("user_branch_id");
        if (localBranchId) {
          const branchId = parseInt(localBranchId);
          console.log("Using branch ID from localStorage:", branchId);

          // Update cache
          branchCache = {
            id: branchId,
            timestamp: now,
          };

          return branchId;
        }
      }

      return null;
    }

    const data = await response.json();

    // Update cache
    if (data.branch_id !== undefined) {
      branchCache = {
        id: data.branch_id,
        timestamp: now,
      };

      // Also update localStorage for future fallback
      if (typeof window !== "undefined" && data.branch_id !== null) {
        try {
          localStorage.setItem("user_branch_id", data.branch_id.toString());
        } catch (e) {
          console.warn("Failed to update localStorage with branch ID:", e);
        }
      }
    }

    return data.branch_id;
  } catch (error: any) {
    console.error("Error getting branch ID from API:", error);

    // Try to get from localStorage as fallback
    if (typeof window !== "undefined") {
      const localBranchId = localStorage.getItem("user_branch_id");
      if (localBranchId) {
        const branchId = parseInt(localBranchId);
        console.log(
          "Using branch ID from localStorage after API error:",
          branchId,
        );
        return branchId;
      }
    }

    return null;
  }
}

/**
 * Get the user's branch ID synchronously (for backward compatibility)
 * @returns The user's branch ID or null
 * @deprecated Use getUserBranchIdAsync instead for reliable results
 */
export function getUserBranchId(): number | null {
  // For server-side rendering, we can't access localStorage
  if (typeof window === "undefined") {
    return null;
  }

  // For client-side, try to get from cache first
  if (
    branchCache.id !== null && Date.now() - branchCache.timestamp < CACHE_TTL
  ) {
    return branchCache.id;
  }

  // Try to get from localStorage as fallback
  const localBranchId = localStorage.getItem("user_branch_id");
  if (localBranchId) {
    const branchId = parseInt(localBranchId);
    console.log(
      "Using branch ID from localStorage in sync function:",
      branchId,
    );

    // Update cache
    branchCache = {
      id: branchId,
      timestamp: Date.now(),
    };

    return branchId;
  }

  // Trigger an async refresh of the cache for future calls
  getUserBranchIdAsync().catch((err) =>
    console.error("Background refresh of branch ID failed:", err)
  );

  // Return null as we don't have a reliable value
  return null;
}

/**
 * Get the user's role from API or cache
 * @returns The user's role or null if not available
 */
export async function getUserRoleAsync(): Promise<string | null> {
  // Check if we have a valid cached value
  const now = Date.now();
  if (roleCache.role !== null && now - roleCache.timestamp < CACHE_TTL) {
    return roleCache.role;
  }

  try {
    const response = await fetch("/api/auth/user-branch");

    if (!response.ok) {
      console.error("Error fetching user role:", response.statusText);
      return null;
    }

    const data = await response.json();

    // Update cache
    if (data.role !== undefined) {
      roleCache = {
        role: data.role,
        timestamp: now,
      };
    }

    return data.role;
  } catch (error: any) {
    console.error("Error getting user role from API:", error);
    return null;
  }
}

/**
 * Get the user's role synchronously (for backward compatibility)
 * @returns The user's role or null
 * @deprecated Use getUserRoleAsync instead for reliable results
 */
export function getUserRole(): string | null {
  // For server-side rendering, we can't access localStorage
  if (typeof window === "undefined") {
    return null;
  }

  // For client-side, try to get from cache first
  if (roleCache.role !== null && Date.now() - roleCache.timestamp < CACHE_TTL) {
    return roleCache.role;
  }

  // Trigger an async refresh of the cache for future calls
  getUserRoleAsync().catch((err) =>
    console.error("Background refresh of user role failed:", err)
  );

  // Return null as we don't have a reliable value
  return null;
}
