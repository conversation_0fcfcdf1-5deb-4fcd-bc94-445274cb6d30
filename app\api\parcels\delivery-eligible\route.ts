import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/delivery-eligible
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      console.error("User lookup error:", userError);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    console.log("🔍 DEBUG: User branch ID:", userData.branch_id);

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20");
    const search = url.searchParams.get("search") || "";

    console.log(
      "🔍 DEBUG: Query params - page:",
      page,
      "pageSize:",
      pageSize,
      "search:",
      search,
    );

    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // First, try to use the delivery_eligible_parcels view
    console.log(
      "🔍 DEBUG: Attempting to query delivery_eligible_parcels view...",
    );

    let query = supabase
      .from("delivery_eligible_parcels")
      .select("*", { count: "exact" })
      .eq("delivery_branch_id", userData.branch_id)
      .eq("ready_for_delivery", true)
      .order("booking_datetime", { ascending: false })
      .range(from, to);

    // Add search filter if provided
    if (search) {
      query = query.or(
        `lr_number.ilike.%${search}%,sender_name.ilike.%${search}%,recipient_name.ilike.%${search}%`,
      );
    }

    const { data: parcels, error: parcelsError, count } = await query;

    if (parcelsError) {
      console.error(
        "❌ Error fetching from delivery_eligible_parcels view:",
        parcelsError,
      );
      console.log("🔄 Falling back to direct parcels table query...");

      // Fallback: Query parcels table directly with delivery logic
      return await getDeliveryEligibleParcelsFallback(
        userData.branch_id,
        page,
        pageSize,
        search,
      );
    }

    console.log("✅ Successfully fetched from delivery_eligible_parcels view");
    console.log(
      "🔍 DEBUG: Found",
      count,
      "total parcels,",
      parcels?.length,
      "on this page",
    );
    console.log("🔍 DEBUG: Sample parcel data:", parcels?.[0]);

    return NextResponse.json({
      parcels: parcels || [],
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
      },
    });
  } catch (error: any) {
    console.error("❌ Error in GET /api/parcels/delivery-eligible:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// Fallback function to query parcels directly
async function getDeliveryEligibleParcelsFallback(
  userBranchId: number,
  page: number,
  pageSize: number,
  search: string,
) {
  try {
    console.log("🔄 Using fallback method - querying parcels table directly");

    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Query parcels table directly with delivery criteria
    let query = supabase
      .from("parcels")
      .select(
        `
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        recipient_phone,
        delivery_branch_id,
        number_of_items,
        current_status,
        booking_datetime,
        weight,
        total_amount,
        payment_mode,
        sender_branch:branches!parcels_sender_branch_id_fkey(name, code),
        delivery_branch:branches!parcels_delivery_branch_id_fkey(name, code)
      `,
        { count: "exact" },
      )
      .eq("delivery_branch_id", userBranchId)
      .eq("current_status", "Received")
      .order("booking_datetime", { ascending: false })
      .range(from, to);

    // Add search filter if provided
    if (search) {
      query = query.or(
        `lr_number.ilike.%${search}%,sender_name.ilike.%${search}%,recipient_name.ilike.%${search}%`,
      );
    }

    const { data: parcels, error: parcelsError, count } = await query;

    if (parcelsError) {
      console.error("❌ Fallback query also failed:", parcelsError);
      return NextResponse.json({ error: "Failed to fetch parcels" }, {
        status: 500,
      });
    }

    console.log("✅ Fallback query successful");
    console.log(
      "🔍 DEBUG: Found",
      count,
      "total parcels,",
      parcels?.length,
      "on this page",
    );
    console.log("🔍 DEBUG: Sample parcel data:", parcels?.[0]);

    // For fallback, we'll assume all "Received" parcels at destination are ready for delivery
    // If parcel status is "Received", we trust that all items have been received
    const enhancedParcels = parcels?.map((parcel) => ({
      ...parcel,
      destination_branch_name: parcel.delivery_branch?.name,
      destination_branch_code: parcel.delivery_branch?.code,
      total_received_items: parcel.number_of_items, // If status is "Received", all items are considered received
      all_items_received: true, // Trust the parcel status
      ready_for_delivery: true, // Ready for delivery if at destination and status is "Received"
    }));

    return NextResponse.json({
      parcels: enhancedParcels || [],
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
      },
    });
  } catch (error: any) {
    console.error("❌ Error in fallback method:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
