import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET /api/branches/by-city
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const city_id = url.searchParams.get('city_id');
    const status = url.searchParams.get('status') || 'Active';
    
    // Build query
    let query = supabase
      .from('branches')
      .select('*');
    
    // Add filters if provided
    if (city_id) {
      query = query.eq('city_id', city_id);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    // Execute query
    const { data: branches, error } = await query.order('name');
    
    if (error) {
      console.error('Error fetching branches by city:', error);
      return NextResponse.json({ error: 'Failed to fetch branches' }, { status: 500 });
    }
    
    return NextResponse.json({ branches });
  } catch (error: any) {
    console.error('Error in GET /api/branches/by-city:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
