import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// POST /api/drivers/test-validation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { driver_number } = body;

    if (!driver_number) {
      return NextResponse.json(
        { error: "Missing required parameter: driver_number" },
        { status: 400 },
      );
    }

    // Log the request
    console.log("Testing driver validation for:", driver_number);

    // Step 1: Check if we can connect to Supabase
    const connectionTest = await supabase.from("drivers").select("count(*)");
    console.log("Connection test:", connectionTest);

    // Step 2: Try to get all drivers
    const { data: allDrivers, error: allDriversError } = await supabase
      .from("drivers")
      .select("driver_id, name, phone_number, driver_number, status")
      .limit(10);

    console.log("All drivers:", allDrivers);

    if (allDriversError) {
      console.error("Error fetching all drivers:", allDriversError);
      return NextResponse.json(
        {
          error: "Failed to fetch drivers",
          details: allDriversError.message,
          step: "all drivers query",
        },
        { status: 500 },
      );
    }

    // Step 3: Try direct phone_number search
    const { data: phoneData, error: phoneError } = await supabase
      .from("drivers")
      .select("*")
      .eq("phone_number", driver_number);

    console.log("Phone search:", {
      found: phoneData && phoneData.length > 0,
      count: phoneData?.length || 0,
      data: phoneData,
    });

    if (phoneError) {
      console.error("Error in phone search:", phoneError);
      return NextResponse.json(
        {
          error: "Failed in phone search",
          details: phoneError.message,
          step: "phone search",
        },
        { status: 500 },
      );
    }

    // Step 4: Try direct driver_number search
    const { data: driverNumData, error: driverNumError } = await supabase
      .from("drivers")
      .select("*")
      .eq("driver_number", driver_number);

    console.log("Driver number search:", {
      found: driverNumData && driverNumData.length > 0,
      count: driverNumData?.length || 0,
      data: driverNumData,
    });

    if (driverNumError) {
      console.error("Error in driver number search:", driverNumError);
      return NextResponse.json(
        {
          error: "Failed in driver number search",
          details: driverNumError.message,
          step: "driver number search",
        },
        { status: 500 },
      );
    }

    // Step 5: Try OR query
    const { data: orData, error: orError } = await supabase
      .from("drivers")
      .select("*")
      .or(`driver_number.eq.${driver_number},phone_number.eq.${driver_number}`);

    console.log("OR search:", {
      found: orData && orData.length > 0,
      count: orData?.length || 0,
      data: orData,
    });

    if (orError) {
      console.error("Error in OR search:", orError);
      return NextResponse.json(
        {
          error: "Failed in OR search",
          details: orError.message,
          step: "OR search",
        },
        { status: 500 },
      );
    }

    // Step 6: Try a raw SQL query
    const { data: rawData, error: rawError } = await supabase.rpc(
      "find_driver_by_number_or_phone",
      { search_value: driver_number },
    );

    console.log("Raw SQL search:", {
      found: rawData && rawData.length > 0,
      count: rawData?.length || 0,
      data: rawData,
    });

    if (rawError) {
      console.error("Error in raw SQL search:", rawError);
      // This might fail if the function doesn't exist, so don't return an error
    }

    // Return all the results
    return NextResponse.json({
      success: true,
      driver_number: driver_number,
      all_drivers: allDrivers,
      phone_search: {
        found: phoneData && phoneData.length > 0,
        count: phoneData?.length || 0,
        data: phoneData,
      },
      driver_number_search: {
        found: driverNumData && driverNumData.length > 0,
        count: driverNumData?.length || 0,
        data: driverNumData,
      },
      or_search: {
        found: orData && orData.length > 0,
        count: orData?.length || 0,
        data: orData,
      },
      raw_search: rawError ? { error: rawError.message } : {
        found: rawData && rawData.length > 0,
        count: rawData?.length || 0,
        data: rawData,
      },
    });
  } catch (error: any) {
    console.error("Error in test-validation:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error.message || String(error),
        stack: error.stack || "No stack trace available",
      },
      { status: 500 },
    );
  }
}
