# Cash Management System Documentation

## Overview

The Cash Management System tracks branch-level cash transactions, remittances to head office, and provides financial reporting capabilities. This document outlines the database schema, API endpoints, and implementation details for the admin application.

## Database Schema

### Key Tables

#### 1. financial_transactions
```sql
CREATE TABLE financial_transactions (
  transaction_id SERIAL PRIMARY KEY,
  branch_id INT REFERENCES branches(branch_id) ON DELETE CASCADE,
  transaction_type txn_type NOT NULL, -- 'Collection' or 'Expense'
  amount NUMERIC(10,2) NOT NULL,
  payment_method TEXT, -- 'Cash', 'UPI', 'Card', etc.
  transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reference_number VARCHAR(100), -- LR number for collections
  description TEXT,
  attachment_url TEXT,
  approval_status approval_status NOT NULL DEFAULT 'Pending', -- 'Pending', 'Approved', 'Rejected'
  status approval_status NOT NULL DEFAULT 'Pending', -- Same as approval_status (maintained by trigger)
  metadata JSONB -- Stores payment details like include_in_branch_balance, payment_type
);
```

#### 2. remittances
```sql
CREATE TABLE remittances (
  remittance_id SERIAL PRIMARY KEY,
  branch_id INT REFERENCES branches(branch_id) ON DELETE CASCADE,
  amount NUMERIC(10,2) NOT NULL,
  remittance_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  remittance_method remittance_method NOT NULL, -- 'Bank Transfer', 'UPI', 'Bank Deposit'
  reference_number VARCHAR(100),
  status remittance_status NOT NULL DEFAULT 'Submitted', -- 'Submitted', 'Verified'
  verified_by INT REFERENCES users(user_id),
  verified_at TIMESTAMP WITH TIME ZONE,
  receipt_url TEXT,
  notes TEXT
);
```

#### 3. accountsledger
```sql
CREATE TABLE accountsledger (
  ledger_id SERIAL PRIMARY KEY,
  branch_id INT REFERENCES branches(branch_id) ON DELETE CASCADE,
  ledger_date DATE NOT NULL,
  opening_balance NUMERIC(10,2) NOT NULL DEFAULT 0,
  total_collection NUMERIC(10,2) NOT NULL DEFAULT 0,
  total_expense NUMERIC(10,2) NOT NULL DEFAULT 0,
  remitted NUMERIC(10,2) NOT NULL DEFAULT 0,
  closing_balance NUMERIC(10,2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Recent Schema Changes

1. **Added metadata column to financial_transactions**
   ```sql
   ALTER TABLE financial_transactions ADD COLUMN metadata JSONB;
   CREATE INDEX idx_financial_transactions_metadata ON financial_transactions USING GIN (metadata);
   ```

2. **Added trigger to sync status and approval_status**
   ```sql
   CREATE OR REPLACE FUNCTION sync_approval_status_to_status()
   RETURNS TRIGGER AS $$
   BEGIN
     NEW.status = NEW.approval_status;
     RETURN NEW;
   END;
   $$ LANGUAGE plpgsql;

   CREATE TRIGGER sync_status_trigger
   BEFORE INSERT OR UPDATE ON financial_transactions
   FOR EACH ROW
   EXECUTE FUNCTION sync_approval_status_to_status();
   ```

## Cash Flow Logic

### Branch Balance Calculation

The branch balance is calculated as:
```
Branch Balance = Total Collections (cash only) - Total Expenses - Total Remittances
```

Key points:
- Only cash payments are included in branch balance
- Online payments go directly to head office and are not included in branch balance
- Both cash and online payments are counted in daily collections for reporting

### Payment Types

1. **Branch Collections (Cash)**
   - Stored with `metadata.include_in_branch_balance = true`
   - Stored with `metadata.payment_type = "branch_collection"`
   - Included in branch balance calculations

2. **Online Payments**
   - Stored with `metadata.include_in_branch_balance = false`
   - Stored with `metadata.payment_type = "online_payment"`
   - Not included in branch balance calculations
   - Still counted in total collections for reporting

## API Endpoints

### Branch Balance

- **GET /api/branch-balance**
  - Returns the current branch balance
  - Includes total_collection, total_expenses, remitted, and balance

- **GET /api/branch-balance/daily**
  - Returns daily statistics for a date range
  - Includes collections, expenses, remittances by day

### Remittances

- **POST /api/remittances**
  - Creates a new remittance
  - Requires amount, remittance_method, reference_number

- **GET /api/remittances**
  - Lists all remittances for a branch
  - Can be filtered by date range and status

- **PATCH /api/remittances/:id**
  - Updates a remittance (e.g., to mark as verified)
  - Admin only endpoint

## Admin Application Requirements

The admin application should provide the following functionality:

### 1. Financial Dashboard

- Overview of all branches' financial status
- Total cash in all branches
- Total online collections
- Pending remittances amount
- Recent transactions

### 2. Branch Financial Details

- Detailed view of each branch's financial status
- Daily collections breakdown (cash vs. online)
- Expense categories breakdown
- Remittance history

### 3. Remittance Management

- List of pending remittances from all branches
- Ability to verify remittances
- Ability to reject remittances with reason
- Historical view of all remittances

### 4. Financial Reports

- Generate reports by date range, branch, transaction type
- Export reports to CSV/Excel
- Visual charts for financial trends

### 5. Transaction Approval

- Review and approve/reject expense transactions
- Bulk approval capabilities
- Audit log of approval actions

## Implementation Guidelines

### Authentication & Authorization

- Only users with 'Admin' or 'Super Admin' roles should access the admin application
- Branch-specific data should only be accessible to admins and the branch's managers

### Data Synchronization

- Use Supabase realtime subscriptions for live updates
- Implement optimistic UI updates with proper error handling

### Error Handling

- Implement comprehensive error handling for all API calls
- Provide clear error messages to users
- Log errors for debugging

### Performance Considerations

- Implement pagination for large data sets
- Use efficient queries with proper indexing
- Consider caching for frequently accessed data

## Example Implementation

### Branch Balance Component

```tsx
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export function BranchBalanceSummary() {
  const [branches, setBranches] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchBranchBalances() {
      setLoading(true);

      // Fetch all branches
      const { data: branchesData, error: branchesError } = await supabase
        .from('branches')
        .select('branch_id, name, code');

      if (branchesError) {
        console.error('Error fetching branches:', branchesError);
        setLoading(false);
        return;
      }

      // Fetch balance for each branch
      const branchesWithBalance = await Promise.all(
        branchesData.map(async (branch) => {
          const response = await fetch(`/api/branch-balance?branch_id=${branch.branch_id}`);
          const balanceData = await response.json();

          return {
            ...branch,
            balance: balanceData.balance || 0,
            total_collection: balanceData.total_collection || 0,
            total_expenses: balanceData.total_expenses || 0,
            remitted: balanceData.remitted || 0
          };
        })
      );

      setBranches(branchesWithBalance);
      setLoading(false);
    }

    fetchBranchBalances();
  }, []);

  return (
    <div>
      <h2>Branch Balance Summary</h2>
      {loading ? (
        <p>Loading branch data...</p>
      ) : (
        <table>
          <thead>
            <tr>
              <th>Branch</th>
              <th>Collections</th>
              <th>Expenses</th>
              <th>Remitted</th>
              <th>Balance</th>
            </tr>
          </thead>
          <tbody>
            {branches.map((branch) => (
              <tr key={branch.branch_id}>
                <td>{branch.name} ({branch.code})</td>
                <td>₹{branch.total_collection.toFixed(2)}</td>
                <td>₹{branch.total_expenses.toFixed(2)}</td>
                <td>₹{branch.remitted.toFixed(2)}</td>
                <td>₹{branch.balance.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
```

## Implementation Details

### Payment Processing Flow

1. **Parcel Booking (Cash Payment)**
   - When a parcel is booked with cash payment:
     - Create a financial transaction with `transaction_type = 'Collection'`
     - Set `metadata.include_in_branch_balance = true`
     - Set `metadata.payment_type = 'branch_collection'`
     - Set `approval_status = 'Approved'` (cash payments are auto-approved)

2. **Parcel Booking (Online Payment)**
   - When a parcel is booked with online payment:
     - Create a financial transaction with `transaction_type = 'Collection'`
     - Set `metadata.include_in_branch_balance = false`
     - Set `metadata.payment_type = 'online_payment'`
     - Set `approval_status = 'Approved'` (online payments are auto-approved)

3. **Expense Creation**
   - When an expense is created:
     - Create a financial transaction with `transaction_type = 'Expense'`
     - Set `approval_status = 'Pending'`
     - Admin must approve the expense before it affects branch balance

4. **Remittance Creation**
   - When a branch remits money to head office:
     - Create a remittance record with `status = 'Submitted'`
     - Admin must verify the remittance before it's considered complete

### Daily Ledger Update

The system automatically updates the accounts ledger at the end of each day:

1. Calculate total collections (both cash and online) for the day
2. Calculate total expenses for the day
3. Calculate total remittances for the day
4. Calculate closing balance:
   ```
   Closing Balance = Opening Balance + Cash Collections - Expenses - Remittances
   ```
5. Create or update the ledger entry for the day

### Admin Verification Workflows

#### Expense Approval

1. Branch creates an expense with supporting documentation
2. Expense appears in admin dashboard with 'Pending' status
3. Admin reviews the expense details and attached receipts
4. Admin approves or rejects the expense
5. If approved:
   - Financial transaction status changes to 'Approved'
   - Branch balance is updated to reflect the expense
6. If rejected:
   - Financial transaction status changes to 'Rejected'
   - Branch balance is not affected

#### Remittance Verification

1. Branch submits a remittance with proof of transfer
2. Remittance appears in admin dashboard with 'Submitted' status
3. Admin verifies the remittance details and receipt
4. Admin marks the remittance as 'Verified'
5. Branch balance is updated to reflect the remittance

## UI Components and Mockups

### Admin Dashboard

The admin dashboard should include the following components:

```
+-----------------------------------------------------------------------+
|                         FINANCIAL DASHBOARD                           |
+------------------------+----------------------+----------------------+
|   TOTAL CASH IN        |   PENDING            |   TOTAL ONLINE       |
|   BRANCHES             |   REMITTANCES        |   COLLECTIONS        |
|   ₹XXX,XXX             |   ₹XX,XXX            |   ₹XXX,XXX           |
+------------------------+----------------------+----------------------+
|                                                                       |
|   BRANCH BALANCE SUMMARY                                              |
|   +----------------------------------------------------------------+ |
|   | BRANCH | COLLECTIONS | EXPENSES | REMITTED | BALANCE | ACTIONS  | |
|   |--------|-------------|----------|----------|---------|----------| |
|   | TCX    | ₹XX,XXX     | ₹X,XXX   | ₹XX,XXX  | ₹X,XXX  | Details  | |
|   | MUM    | ₹XX,XXX     | ₹X,XXX   | ₹XX,XXX  | ₹X,XXX  | Details  | |
|   | DEL    | ₹XX,XXX     | ₹X,XXX   | ₹XX,XXX  | ₹X,XXX  | Details  | |
|   +----------------------------------------------------------------+ |
|                                                                       |
|   PENDING APPROVALS                                                   |
|   +----------------------------------------------------------------+ |
|   | TYPE    | BRANCH | AMOUNT | DATE       | DESCRIPTION | ACTIONS  | |
|   |---------|--------|--------|------------|-------------|----------| |
|   | Expense | TCX    | ₹X,XXX | 2023-05-15 | Fuel        | Review   | |
|   | Remit   | MUM    | ₹X,XXX | 2023-05-14 | Bank Trans  | Verify   | |
|   +----------------------------------------------------------------+ |
|                                                                       |
+-----------------------------------------------------------------------+
```

### Branch Details View

When clicking on a branch's "Details" button, show a detailed view:

```
+-----------------------------------------------------------------------+
|                         BRANCH: TCX DETAILS                           |
+------------------------+----------------------+----------------------+
|   CASH BALANCE         |   PENDING            |   TOTAL              |
|                        |   EXPENSES           |   COLLECTIONS        |
|   ₹XXX,XXX             |   ₹XX,XXX            |   ₹XXX,XXX           |
+------------------------+----------------------+----------------------+
|                                                                       |
|   DAILY STATISTICS                                                    |
|   +----------------------------------------------------------------+ |
|   | DATE       | COLLECTIONS | EXPENSES | REMITTED | NET           | |
|   |------------|-------------|----------|----------|---------------| |
|   | 2023-05-15 | ₹XX,XXX     | ₹X,XXX   | ₹XX,XXX  | ₹X,XXX        | |
|   | 2023-05-14 | ₹XX,XXX     | ₹X,XXX   | ₹XX,XXX  | ₹X,XXX        | |
|   +----------------------------------------------------------------+ |
|                                                                       |
|   RECENT TRANSACTIONS                                                 |
|   +----------------------------------------------------------------+ |
|   | TYPE      | AMOUNT | DATE       | DESCRIPTION     | STATUS     | |
|   |-----------|--------|------------|-----------------|------------| |
|   | Collection| ₹X,XXX | 2023-05-15 | LR-12345        | Approved   | |
|   | Expense   | ₹X,XXX | 2023-05-15 | Fuel            | Pending    | |
|   | Remittance| ₹X,XXX | 2023-05-14 | Bank Transfer   | Submitted  | |
|   +----------------------------------------------------------------+ |
|                                                                       |
+-----------------------------------------------------------------------+
```

### Transaction Approval Interface

For reviewing and approving expenses:

```
+-----------------------------------------------------------------------+
|                         EXPENSE APPROVAL                              |
+-----------------------------------------------------------------------+
|                                                                       |
|   EXPENSE DETAILS                                                     |
|   Branch: TCX                                                         |
|   Amount: ₹X,XXX                                                      |
|   Date: 2023-05-15                                                    |
|   Category: Fuel                                                      |
|   Description: Diesel for vehicle TN-01-AB-1234                       |
|                                                                       |
|   ATTACHMENTS                                                         |
|   [View Receipt]                                                      |
|                                                                       |
|   APPROVAL DECISION                                                   |
|   [ ] Approve                                                         |
|   [ ] Reject                                                          |
|                                                                       |
|   Reason (required for rejection):                                    |
|   +--------------------------------+                                  |
|   |                                |                                  |
|   +--------------------------------+                                  |
|                                                                       |
|   [Cancel]                [Submit Decision]                           |
|                                                                       |
+-----------------------------------------------------------------------+
```

### Remittance Verification Interface

For verifying branch remittances:

```
+-----------------------------------------------------------------------+
|                         REMITTANCE VERIFICATION                       |
+-----------------------------------------------------------------------+
|                                                                       |
|   REMITTANCE DETAILS                                                  |
|   Branch: MUM                                                         |
|   Amount: ₹XX,XXX                                                     |
|   Date: 2023-05-14                                                    |
|   Method: Bank Transfer                                               |
|   Reference: UTIB23051412345                                          |
|                                                                       |
|   ATTACHMENTS                                                         |
|   [View Receipt]                                                      |
|                                                                       |
|   VERIFICATION DECISION                                               |
|   [ ] Verify                                                          |
|   [ ] Reject                                                          |
|                                                                       |
|   Reason (required for rejection):                                    |
|   +--------------------------------+                                  |
|   |                                |                                  |
|   +--------------------------------+                                  |
|                                                                       |
|   [Cancel]                [Submit Decision]                           |
|                                                                       |
+-----------------------------------------------------------------------+
```

## Next Steps

1. Implement the admin dashboard with the components outlined above
2. Create the remittance verification workflow
3. Develop financial reporting capabilities
4. Implement transaction approval system
5. Add data export functionality
6. Set up automated daily ledger updates
7. Implement notification system for pending approvals
