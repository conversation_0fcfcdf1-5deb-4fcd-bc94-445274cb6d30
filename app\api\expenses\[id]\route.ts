import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// Helper function to update memo status using raw SQL
async function updateMemoStatusToCompleted(
  memoId: number,
  totalExpense: number,
) {
  try {
    // Try using the RPC function first
    const { error: rpcError } = await supabase.rpc("exec_sql", {
      sql: `UPDATE memos SET status = 'Completed', total_expense = ${
        totalExpense || 0
      } WHERE memo_id = ${memoId}`,
    });

    if (rpcError) {
      console.error("RPC error in helper function:", rpcError);
      return { success: false, error: rpcError };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in updateMemoStatusToCompleted:", error);
    return { success: false, error };
  }
}

// GET /api/expenses/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Get expense by ID
    const { data: expense, error } = await supabase
      .from("expenses")
      .select(`
        *,
        branch:branches(name, code),
        submitter:users!submitted_by(name),
        approver:users!approved_by(name),
        rejecter:users!rejected_by(name)
      `)
      .eq("id", id)
      .single();

    if (error) {
      console.error("Error fetching expense:", error);
      return NextResponse.json({ error: "Failed to fetch expense" }, {
        status: 500,
      });
    }

    if (!expense) {
      return NextResponse.json({ error: "Expense not found" }, { status: 404 });
    }

    return NextResponse.json({ expense });
  } catch (error: any) {
    console.error(`Error in GET /api/expenses/${params.id}:`, error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// PATCH /api/expenses/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = params.id;
    const body = await request.json();

    // Check if expense exists
    const { data: existingExpense, error: fetchError } = await supabase
      .from("expenses")
      .select("*")
      .eq("id", id)
      .single();

    if (fetchError || !existingExpense) {
      return NextResponse.json({ error: "Expense not found" }, { status: 404 });
    }

    // Get user ID from the users table
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("user_id, role")
      .eq("auth_id", session.user.id)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      return NextResponse.json({ error: "Failed to fetch user" }, {
        status: 500,
      });
    }

    const userId = userData?.user_id;
    const userRole = userData?.role;

    // Only allow managers or admins to approve/reject expenses
    if (
      body.approval_status &&
      body.approval_status !== existingExpense.approval_status
    ) {
      if (
        userRole !== "Manager" && userRole !== "Admin" &&
        userRole !== "Super Admin"
      ) {
        return NextResponse.json(
          { error: "Only managers or admins can approve or reject expenses" },
          { status: 403 },
        );
      }

      // Handle approval
      if (body.approval_status === "Approved") {
        // Create financial transaction
        const { data: transaction, error: txnError } = await supabase
          .from("financialtransactions")
          .insert({
            branch_id: existingExpense.branch_id,
            transaction_type: "Expense",
            amount: existingExpense.amount,
            payment_mode: existingExpense.payment_method,
            description: existingExpense.description,
            reference_number: existingExpense.invoice_number || `EXP-${id}`,
            approval_status: "Approved",
          })
          .select()
          .single();

        if (txnError) {
          console.error("Error creating financial transaction:", txnError);
          return NextResponse.json(
            { error: "Failed to create financial transaction" },
            { status: 500 },
          );
        }

        // Update expense with financial transaction ID and approval info
        body.financial_tx_id = transaction.transaction_id;
        body.approved_at = new Date().toISOString();
        body.approved_by = userId;

        // If this expense is associated with a memo, update the memo status to "Completed"
        if (existingExpense.memo_number) {
          console.log(
            `Expense has memo_number: ${existingExpense.memo_number}`,
          );

          // Find the memo by memo_number
          const { data: memo, error: memoError } = await supabase
            .from("memos")
            .select("memo_id, status, memo_number")
            .eq("memo_number", existingExpense.memo_number)
            .single();

          if (memoError) {
            console.error("Error finding memo:", memoError);
            // Continue anyway, as the expense was approved successfully
          } else if (memo) {
            console.log(`Found memo: ${JSON.stringify(memo)}`);

            // Update memo status to "Completed" regardless of current status
            console.log(
              `Attempting to update memo ${memo.memo_id} from status "${memo.status}" to "Completed"`,
            );
            const { data: updatedMemo, error: updateMemoError } = await supabase
              .from("memos")
              .update({
                status: "Completed",
                // Also update total_expense field if it exists
                ...(existingExpense.amount
                  ? { total_expense: existingExpense.amount }
                  : {}),
              })
              .eq("memo_id", memo.memo_id)
              .select()
              .single();

            if (updateMemoError) {
              console.error("Error updating memo status:", updateMemoError);
              console.error("Error details:", JSON.stringify(updateMemoError));

              // Try a different approach - use a direct update without using the enum type
              const { data: directUpdateResult, error: directUpdateError } =
                await supabase
                  .from("memos")
                  .update({
                    status: "Completed",
                    total_expense: existingExpense.amount || 0,
                  })
                  .eq("memo_id", memo.memo_id)
                  .select();

              if (directUpdateError) {
                console.error("Direct update error:", directUpdateError);

                // As a last resort, try RPC to execute SQL directly
                const { error: rpcError } = await supabase.rpc("exec_sql", {
                  sql:
                    `UPDATE memos SET status = 'Completed', total_expense = ${
                      existingExpense.amount || 0
                    } WHERE memo_id = ${memo.memo_id}`,
                });

                if (rpcError) {
                  console.error("RPC error updating memo status:", rpcError);

                  // Try one last approach using our helper function
                  const result = await updateMemoStatusToCompleted(
                    memo.memo_id,
                    existingExpense.amount || 0,
                  );
                  if (result.success) {
                    console.log(
                      "Successfully updated memo status using helper function",
                    );

                    // Verify the helper function update
                    const {
                      data: verifiedHelperMemo,
                      error: verifyHelperError,
                    } = await supabase
                      .from("memos")
                      .select("memo_id, status, memo_number")
                      .eq("memo_id", memo.memo_id)
                      .single();

                    if (verifyHelperError) {
                      console.error(
                        "Error verifying helper memo update:",
                        verifyHelperError,
                      );
                    } else {
                      console.log(
                        `Verified memo status after helper update: ${
                          JSON.stringify(verifiedHelperMemo)
                        }`,
                      );
                    }
                  } else {
                    console.error(
                      "Helper function failed to update memo status:",
                      result.error,
                    );
                  }
                } else {
                  console.log(`Successfully updated memo status via RPC`);

                  // Verify the RPC update
                  const { data: verifiedRpcMemo, error: verifyRpcError } =
                    await supabase
                      .from("memos")
                      .select("memo_id, status, memo_number")
                      .eq("memo_id", memo.memo_id)
                      .single();

                  if (verifyRpcError) {
                    console.error(
                      "Error verifying RPC memo update:",
                      verifyRpcError,
                    );
                  } else {
                    console.log(
                      `Verified memo status after RPC update: ${
                        JSON.stringify(verifiedRpcMemo)
                      }`,
                    );
                  }
                }
              } else {
                console.log(
                  `Successfully updated memo status via direct update: ${
                    JSON.stringify(directUpdateResult)
                  }`,
                );

                // Verify the direct update
                const { data: verifiedDirectMemo, error: verifyDirectError } =
                  await supabase
                    .from("memos")
                    .select("memo_id, status, memo_number")
                    .eq("memo_id", memo.memo_id)
                    .single();

                if (verifyDirectError) {
                  console.error(
                    "Error verifying direct memo update:",
                    verifyDirectError,
                  );
                } else {
                  console.log(
                    `Verified memo status after direct update: ${
                      JSON.stringify(verifiedDirectMemo)
                    }`,
                  );
                }
              }

              // Continue anyway, as the expense was approved successfully
            } else {
              console.log(
                `Successfully updated memo status to Completed: ${
                  JSON.stringify(updatedMemo)
                }`,
              );

              // Verify the update by querying the memo again
              const { data: verifiedMemo, error: verifyError } = await supabase
                .from("memos")
                .select("memo_id, status, memo_number")
                .eq("memo_id", memo.memo_id)
                .single();

              if (verifyError) {
                console.error("Error verifying memo update:", verifyError);
              } else {
                console.log(
                  `Verified memo status after update: ${
                    JSON.stringify(verifiedMemo)
                  }`,
                );
              }
            }
          } else {
            console.log(
              `No memo found with memo_number: ${existingExpense.memo_number}`,
            );
          }
        }
      }

      // Handle rejection
      if (body.approval_status === "Rejected") {
        body.rejected_at = new Date().toISOString();
        body.rejected_by = userId;
      }
    }

    // Update expense
    const { data: updatedExpense, error: updateError } = await supabase
      .from("expenses")
      .update(body)
      .eq("id", id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating expense:", updateError);
      return NextResponse.json({ error: "Failed to update expense" }, {
        status: 500,
      });
    }

    return NextResponse.json({ expense: updatedExpense });
  } catch (error: any) {
    console.error(`Error in PATCH /api/expenses/${params.id}:`, error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// DELETE /api/expenses/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = params.id;

    // Check if expense exists
    const { data: existingExpense, error: fetchError } = await supabase
      .from("expenses")
      .select("*")
      .eq("id", id)
      .single();

    if (fetchError || !existingExpense) {
      return NextResponse.json({ error: "Expense not found" }, { status: 404 });
    }

    // Only allow deletion of pending expenses
    if (existingExpense.approval_status !== "Pending") {
      return NextResponse.json(
        { error: "Cannot delete expense that is not in Pending status" },
        { status: 400 },
      );
    }

    // Delete expense
    const { error: deleteError } = await supabase
      .from("expenses")
      .delete()
      .eq("id", id);

    if (deleteError) {
      console.error("Error deleting expense:", deleteError);
      return NextResponse.json({ error: "Failed to delete expense" }, {
        status: 500,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error in DELETE /api/expenses/${params.id}:`, error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
