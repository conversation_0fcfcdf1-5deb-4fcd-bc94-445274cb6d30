import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/test-search?lr_number=TBT-20250530-0903
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const lr_number = url.searchParams.get("lr_number") || "TBT-20250530-0903";

    console.log("🔍 Testing search for LR:", lr_number);

    // Test 1: Direct search in parcels table
    const { data: directSearch, error: directError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        current_status,
        delivery_branch_id,
        sender_branch:branches!parcels_sender_branch_id_fkey(name),
        delivery_branch:branches!parcels_delivery_branch_id_fkey(name)
      `)
      .eq("lr_number", lr_number);

    console.log("📦 Direct search result:", directSearch);

    // Test 2: Search with ilike (case insensitive)
    const { data: ilikeSearch, error: ilikeError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        current_status,
        delivery_branch_id
      `)
      .ilike("lr_number", `%${lr_number}%`);

    console.log("🔍 ilike search result:", ilikeSearch);

    // Test 3: Get all parcels with "Received" status
    const { data: receivedParcels, error: receivedError } = await supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        current_status,
        delivery_branch_id
      `)
      .eq("current_status", "Received")
      .limit(10);

    console.log("📋 All received parcels (first 10):", receivedParcels);

    // Test 4: Check delivery_eligible_parcels view
    const { data: viewSearch, error: viewError } = await supabase
      .from("delivery_eligible_parcels")
      .select("*")
      .eq("lr_number", lr_number);

    console.log("👁️ View search result:", viewSearch);
    console.log("❌ View error:", viewError);

    return NextResponse.json({
      test_lr_number: lr_number,
      results: {
        direct_search: {
          data: directSearch,
          error: directError,
          found: directSearch && directSearch.length > 0
        },
        ilike_search: {
          data: ilikeSearch,
          error: ilikeError,
          found: ilikeSearch && ilikeSearch.length > 0
        },
        received_parcels: {
          data: receivedParcels,
          error: receivedError,
          count: receivedParcels?.length || 0
        },
        view_search: {
          data: viewSearch,
          error: viewError,
          found: viewSearch && viewSearch.length > 0
        }
      },
      summary: {
        parcel_exists: !!(directSearch && directSearch.length > 0),
        parcel_status: directSearch?.[0]?.current_status,
        delivery_branch_id: directSearch?.[0]?.delivery_branch_id,
        appears_in_view: !!(viewSearch && viewSearch.length > 0),
        view_error_message: viewError?.message
      }
    });

  } catch (error: any) {
    console.error("❌ Error in test search:", error);
    return NextResponse.json({ 
      error: "Internal Server Error", 
      details: error.message 
    }, { status: 500 });
  }
}
