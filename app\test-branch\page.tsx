"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

export default function TestBranchPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [branchData, setBranchData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBranchData()
  }, [])

  const fetchBranchData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/auth/test-branch')
      const data = await response.json()

      if (response.ok) {
        setBranchData(data)
      } else {
        setError(data.error || 'Failed to fetch branch data')
      }
    } catch (error: any) {
      console.error('Error fetching branch data:', error)
      setError('Failed to fetch branch data')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Branch Test Page</CardTitle>
          <CardDescription>
            This page tests if your user account is correctly associated with a branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading branch data...</span>
            </div>
          ) : error ? (
            <div className="p-4 bg-destructive/10 text-destructive rounded-md">
              <h3 className="font-bold">Error</h3>
              <p>{error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">User Information</h3>
                <p><span className="font-medium">User ID:</span> {branchData.user_id || 'Not found'}</p>
                <p><span className="font-medium">Email:</span> {branchData.email || 'Not found'}</p>
                <p><span className="font-medium">Session User ID:</span> {branchData.session_user_id || 'Not found'}</p>
              </div>

              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Branch Information</h3>
                <p><span className="font-medium">Branch ID:</span> {branchData.branch_id || 'Not assigned to a branch'}</p>
                
                {branchData.branch_details ? (
                  <div className="mt-2">
                    <p><span className="font-medium">Branch Name:</span> {branchData.branch_details.name}</p>
                    <p><span className="font-medium">Branch Code:</span> {branchData.branch_details.code}</p>
                    <p><span className="font-medium">City ID:</span> {branchData.branch_details.city_id}</p>
                    <p><span className="font-medium">Status:</span> {branchData.branch_details.status}</p>
                  </div>
                ) : (
                  <p className="mt-2 text-destructive">No branch details found</p>
                )}
              </div>

              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-bold mb-2">Raw Response</h3>
                <pre className="text-xs overflow-auto max-h-[200px] bg-black/10 p-2 rounded">
                  {JSON.stringify(branchData, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={fetchBranchData} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Refreshing...
              </>
            ) : (
              'Refresh Data'
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
