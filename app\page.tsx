"use client"

import { DashboardShell } from '@/components/dashboard-shell'
import { Overview } from '@/components/overview'
import { PageLayout } from '@/components/page-layout'
import { AuthDebug } from '@/components/auth-debug'

export default function Home() {
  return (
    <PageLayout
      title="Branch Dashboard"
      subtitle="Overview of today's operations and activities"
      showActions={true}
    >
      <AuthDebug />
      <DashboardShell>
        <Overview />
      </DashboardShell>
    </PageLayout>
  )
}