import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";
import fs from 'fs';
import path from 'path';

// POST /api/migrations/expenses-table
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is an admin
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("id", session.user.id)
      .single();

    if (userError || !user || user.role !== 'Admin') {
      return NextResponse.json({ error: "Only admins can run migrations" }, { status: 403 });
    }

    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'db', 'expenses_table_migration.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error: migrationError } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });

    if (migrationError) {
      console.error("Error running expenses table migration:", migrationError);
      return NextResponse.json({ error: "Failed to run migration" }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: "Expenses table migration completed successfully" 
    });
  } catch (error: any) {
    console.error("Error in POST /api/migrations/expenses-table:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
