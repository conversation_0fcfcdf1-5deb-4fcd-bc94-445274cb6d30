export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      cities: {
        Row: {
          city_id: number;
          name: string;
          state: string | null;
          country: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          city_id?: number;
          name: string;
          state?: string | null;
          country?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          city_id?: number;
          name?: string;
          state?: string | null;
          country?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      branches: {
        Row: {
          branch_id: number;
          name: string;
          code: string;
          address: string | null;
          phone: string | null;
          email: string | null;
          operating_hours: Json | null;
          status: Database["public"]["Enums"]["branch_status"];
          location_coordinates: Json | null;
          branch_type: Database["public"]["Enums"]["branch_type"];
          manager_id: number | null;
          service_area: string | null;
          city_id: number | null;
        };
        Insert: {
          branch_id?: number;
          name: string;
          code: string;
          address?: string | null;
          phone?: string | null;
          email?: string | null;
          operating_hours?: Json | null;
          status?: Database["public"]["Enums"]["branch_status"];
          location_coordinates?: Json | null;
          branch_type?: Database["public"]["Enums"]["branch_type"];
          manager_id?: number | null;
          service_area?: string | null;
          city_id?: number | null;
        };
        Update: {
          branch_id?: number;
          name?: string;
          code?: string;
          address?: string | null;
          phone?: string | null;
          email?: string | null;
          operating_hours?: Json | null;
          status?: Database["public"]["Enums"]["branch_status"];
          location_coordinates?: Json | null;
          branch_type?: Database["public"]["Enums"]["branch_type"];
          manager_id?: number | null;
          service_area?: string | null;
          city_id?: number | null;
        };
      };
      users: {
        Row: {
          user_id: number;
          name: string;
          email: string;
          phone: string | null;
          role: Database["public"]["Enums"]["user_role"];
          branch_id: number | null;
          notification_preferences: Json | null;
          two_fa_enabled: boolean;
          login_history: Json | null;
        };
        Insert: {
          user_id?: number;
          name: string;
          email: string;
          phone?: string | null;
          role: Database["public"]["Enums"]["user_role"];
          branch_id?: number | null;
          notification_preferences?: Json | null;
          two_fa_enabled?: boolean;
          login_history?: Json | null;
        };
        Update: {
          user_id?: number;
          name?: string;
          email?: string;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          branch_id?: number | null;
          notification_preferences?: Json | null;
          two_fa_enabled?: boolean;
          login_history?: Json | null;
        };
      };
      parceltypes: {
        Row: {
          type_id: number;
          type_name: string;
          base_price: number | null;
          per_kg_rate: number | null;
          weight_restriction: number | null;
          special_handling_instructions: string | null;
        };
        Insert: {
          type_id?: number;
          type_name: string;
          base_price?: number | null;
          per_kg_rate?: number | null;
          weight_restriction?: number | null;
          special_handling_instructions?: string | null;
        };
        Update: {
          type_id?: number;
          type_name?: string;
          base_price?: number | null;
          per_kg_rate?: number | null;
          weight_restriction?: number | null;
          special_handling_instructions?: string | null;
        };
      };
      parcels: {
        Row: {
          parcel_id: number;
          lr_number: string;
          booking_datetime: string;
          sender_name: string;
          sender_phone: string | null;
          sender_branch_id: number | null;
          recipient_name: string;
          recipient_phone: string | null;
          delivery_branch_id: number | null;
          number_of_items: number | null;
          item_type: number | null;
          weight: number | null;
          payment_mode: Database["public"]["Enums"]["payment_mode"];
          delivery_charges: number | null;
          base_price: number | null;
          tax_details: Json | null;
          total_amount: number | null;
          current_status: Database["public"]["Enums"]["parcel_status"];
          expected_delivery_date: string | null;
          actual_delivery_date: string | null;
          proof_of_delivery_url: string | null;
          collector_details: Json | null;
          recipient_id_details: Json | null;
        };
        Insert: {
          parcel_id?: number;
          lr_number: string;
          booking_datetime?: string;
          sender_name: string;
          sender_phone?: string | null;
          sender_branch_id?: number | null;
          recipient_name: string;
          recipient_phone?: string | null;
          delivery_branch_id?: number | null;
          number_of_items?: number | null;
          item_type?: number | null;
          weight?: number | null;
          payment_mode?: Database["public"]["Enums"]["payment_mode"];
          delivery_charges?: number | null;
          base_price?: number | null;
          tax_details?: Json | null;
          total_amount?: number | null;
          current_status?: Database["public"]["Enums"]["parcel_status"];
          expected_delivery_date?: string | null;
          actual_delivery_date?: string | null;
          proof_of_delivery_url?: string | null;
          collector_details?: Json | null;
          recipient_id_details?: Json | null;
        };
        Update: {
          parcel_id?: number;
          lr_number?: string;
          booking_datetime?: string;
          sender_name?: string;
          sender_phone?: string | null;
          sender_branch_id?: number | null;
          recipient_name?: string;
          recipient_phone?: string | null;
          delivery_branch_id?: number | null;
          number_of_items?: number | null;
          item_type?: number | null;
          weight?: number | null;
          payment_mode?: Database["public"]["Enums"]["payment_mode"];
          delivery_charges?: number | null;
          base_price?: number | null;
          tax_details?: Json | null;
          total_amount?: number | null;
          current_status?: Database["public"]["Enums"]["parcel_status"];
          expected_delivery_date?: string | null;
          actual_delivery_date?: string | null;
          proof_of_delivery_url?: string | null;
          collector_details?: Json | null;
          recipient_id_details?: Json | null;
        };
      };
      vehicles: {
        Row: {
          vehicle_id: number;
          registration_number: string;
          vehicle_type: string | null;
          make_model: string | null;
          year: number | null;
          capacity: number | null;
          current_status: Database["public"]["Enums"]["vehicle_status"];
          maintenance_history: Json | null;
          branch_id: number | null;
          insurance_provider: string | null;
          insurance_policy_number: string | null;
          insurance_expiry: string | null;
        };
        Insert: {
          vehicle_id?: number;
          registration_number: string;
          vehicle_type?: string | null;
          make_model?: string | null;
          year?: number | null;
          capacity?: number | null;
          current_status?: Database["public"]["Enums"]["vehicle_status"];
          maintenance_history?: Json | null;
          branch_id?: number | null;
          insurance_provider?: string | null;
          insurance_policy_number?: string | null;
          insurance_expiry?: string | null;
        };
        Update: {
          vehicle_id?: number;
          registration_number?: string;
          vehicle_type?: string | null;
          make_model?: string | null;
          year?: number | null;
          capacity?: number | null;
          current_status?: Database["public"]["Enums"]["vehicle_status"];
          maintenance_history?: Json | null;
          branch_id?: number | null;
          insurance_provider?: string | null;
          insurance_policy_number?: string | null;
          insurance_expiry?: string | null;
        };
      };
      drivers: {
        Row: {
          driver_id: number;
          name: string;
          contact_number: string | null;
          status: Database["public"]["Enums"]["driver_status"];
        };
        Insert: {
          driver_id?: number;
          name: string;
          contact_number?: string | null;
          status?: Database["public"]["Enums"]["driver_status"];
        };
        Update: {
          driver_id?: number;
          name?: string;
          contact_number?: string | null;
          status?: Database["public"]["Enums"]["driver_status"];
        };
      };
      memos: {
        Row: {
          memo_id: number;
          memo_number: string;
          vehicle_id: number | null;
          driver_ids: Json | null;
          from_branch_id: number | null;
          to_branch_id: number | null;
          created_by: number | null;
          status: Database["public"]["Enums"]["memo_status"];
          created_at: string;
        };
        Insert: {
          memo_id?: number;
          memo_number: string;
          vehicle_id?: number | null;
          driver_ids?: Json | null;
          from_branch_id?: number | null;
          to_branch_id?: number | null;
          created_by?: number | null;
          status?: Database["public"]["Enums"]["memo_status"];
          created_at?: string;
        };
        Update: {
          memo_id?: number;
          memo_number?: string;
          vehicle_id?: number | null;
          driver_ids?: Json | null;
          from_branch_id?: number | null;
          to_branch_id?: number | null;
          created_by?: number | null;
          status?: Database["public"]["Enums"]["memo_status"];
          created_at?: string;
        };
      };
      operations: {
        Row: {
          operation_id: number;
          operation_type: Database["public"]["Enums"]["op_type"];
          vehicle_id: number;
          parcel_id: number;
          confirmed_item_count: number | null;
          total_item_count: number | null;
          operator_id: number | null;
          operation_timestamp: string;
        };
        Insert: {
          operation_id?: number;
          operation_type: Database["public"]["Enums"]["op_type"];
          vehicle_id: number;
          parcel_id: number;
          confirmed_item_count?: number | null;
          total_item_count?: number | null;
          operator_id?: number | null;
          operation_timestamp?: string;
        };
        Update: {
          operation_id?: number;
          operation_type?: Database["public"]["Enums"]["op_type"];
          vehicle_id?: number;
          parcel_id?: number;
          confirmed_item_count?: number | null;
          total_item_count?: number | null;
          operator_id?: number | null;
          operation_timestamp?: string;
        };
      };
      financialtransactions: {
        Row: {
          transaction_id: number;
          branch_id: number;
          transaction_type: Database["public"]["Enums"]["txn_type"];
          amount: number;
          payment_mode: string | null;
          date_time: string;
          reference_number: string | null;
          description: string | null;
          attachment_url: string | null;
          approval_status: Database["public"]["Enums"]["approval_status"];
        };
        Insert: {
          transaction_id?: number;
          branch_id: number;
          transaction_type: Database["public"]["Enums"]["txn_type"];
          amount: number;
          payment_mode?: string | null;
          date_time?: string;
          reference_number?: string | null;
          description?: string | null;
          attachment_url?: string | null;
          approval_status?: Database["public"]["Enums"]["approval_status"];
        };
        Update: {
          transaction_id?: number;
          branch_id?: number;
          transaction_type?: Database["public"]["Enums"]["txn_type"];
          amount?: number;
          payment_mode?: string | null;
          date_time?: string;
          reference_number?: string | null;
          description?: string | null;
          attachment_url?: string | null;
          approval_status?: Database["public"]["Enums"]["approval_status"];
        };
      };
      remittance: {
        Row: {
          remittance_id: number;
          branch_id: number;
          total_collections: number | null;
          approved_expenses: number | null;
          float_balance: number | null;
          remittable_amount: number | null;
          method: Database["public"]["Enums"]["remittance_method"];
          proof_url: string | null;
          reference_id: string | null;
          status: Database["public"]["Enums"]["remittance_status"];
          submitted_by: number | null;
          created_at: string;
        };
        Insert: {
          remittance_id?: number;
          branch_id: number;
          total_collections?: number | null;
          approved_expenses?: number | null;
          float_balance?: number | null;
          remittable_amount?: number | null;
          method: Database["public"]["Enums"]["remittance_method"];
          proof_url?: string | null;
          reference_id?: string | null;
          status?: Database["public"]["Enums"]["remittance_status"];
          submitted_by?: number | null;
          created_at?: string;
        };
        Update: {
          remittance_id?: number;
          branch_id?: number;
          total_collections?: number | null;
          approved_expenses?: number | null;
          float_balance?: number | null;
          remittable_amount?: number | null;
          method?: Database["public"]["Enums"]["remittance_method"];
          proof_url?: string | null;
          reference_id?: string | null;
          status?: Database["public"]["Enums"]["remittance_status"];
          submitted_by?: number | null;
          created_at?: string;
        };
      };
      accountsledger: {
        Row: {
          ledger_id: number;
          branch_id: number;
          ledger_date: string;
          opening_balance: number;
          total_collection: number | null;
          total_expense: number | null;
          remitted: number | null;
          closing_balance: number;
        };
        Insert: {
          ledger_id?: number;
          branch_id: number;
          ledger_date: string;
          opening_balance: number;
          total_collection?: number | null;
          total_expense?: number | null;
          remitted?: number | null;
          closing_balance: number;
        };
        Update: {
          ledger_id?: number;
          branch_id?: number;
          ledger_date?: string;
          opening_balance?: number;
          total_collection?: number | null;
          total_expense?: number | null;
          remitted?: number | null;
          closing_balance?: number;
        };
      };
      auditlogs: {
        Row: {
          log_id: number;
          timestamp: string;
          user_id: number;
          action_type: string | null;
          action_details: string | null;
          ip_address: string | null;
          device_info: string | null;
        };
        Insert: {
          log_id?: number;
          timestamp?: string;
          user_id: number;
          action_type?: string | null;
          action_details?: string | null;
          ip_address?: string | null;
          device_info?: string | null;
        };
        Update: {
          log_id?: number;
          timestamp?: string;
          user_id?: number;
          action_type?: string | null;
          action_details?: string | null;
          ip_address?: string | null;
          device_info?: string | null;
        };
      };
      notifications: {
        Row: {
          notification_id: number;
          target_user_id: number;
          notification_type: string | null;
          message: string | null;
          read_status: boolean;
          timestamp: string;
        };
        Insert: {
          notification_id?: number;
          target_user_id: number;
          notification_type?: string | null;
          message?: string | null;
          read_status?: boolean;
          timestamp?: string;
        };
        Update: {
          notification_id?: number;
          target_user_id?: number;
          notification_type?: string | null;
          message?: string | null;
          read_status?: boolean;
          timestamp?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      branch_status: "Active" | "Inactive";
      branch_type: "Main" | "Sub";
      user_role: "Super Admin" | "Admin" | "Manager";
      payment_mode: "Paid" | "To Pay";
      parcel_status:
        | "Booked"
        | "To Be Received"
        | "To Be Delivered"
        | "Delivered";
      vehicle_status: "Active" | "Maintenance" | "Retired";
      driver_status: "Active" | "On Leave" | "Inactive";
      memo_status: "Created" | "Received" | "Completed";
      op_type: "Load" | "Receive";
      txn_type: "Collection" | "Expense";
      approval_status: "Pending" | "Approved" | "Rejected";
      remittance_method: "Bank Transfer" | "UPI" | "Bank Deposit";
      remittance_status: "Submitted" | "Verified";
    };
  };
}
