import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/memos/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Get memo by ID
    const { data: memo, error } = await supabase
      .from("memos")
      .select(`
        *,
        vehicle:vehicles(registration_number, vehicle_type, make_model),
        from_branch:branches!from_branch_id(name, code),
        to_branch:branches!to_branch_id(name, code),
        creator:users!created_by(name)
      `)
      .eq("memo_id", id)
      .single();

    if (error) {
      console.error("Error fetching memo:", error);
      return NextResponse.json({ error: "Failed to fetch memo" }, {
        status: 500,
      });
    }

    if (!memo) {
      return NextResponse.json({ error: "Memo not found" }, { status: 404 });
    }

    return NextResponse.json({ memo });
  } catch (error: any) {
    console.error(`Error in GET /api/memos/${params.id}:`, error);
    return NextResponse.json({
      error: "Internal server error",
      details: error.message || String(error),
    }, {
      status: 500,
    });
  }
}

// PATCH /api/memos/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;
    let body;

    try {
      body = await request.json();
      console.log(
        "PATCH /api/memos/[id] - Request body:",
        JSON.stringify(body),
      );
    } catch (parseError: any) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json({ error: "Invalid JSON in request body" }, {
        status: 400,
      });
    }

    // Check if memo exists
    const { data: existingMemo, error: fetchError } = await supabase
      .from("memos")
      .select("*")
      .eq("memo_id", id)
      .single();

    if (fetchError || !existingMemo) {
      return NextResponse.json({ error: "Memo not found" }, { status: 404 });
    }

    // Extract driver_expenses from body to handle separately
    const { driver_expenses, ...memoUpdateData } = body;

    console.log("Memo update data:", JSON.stringify(memoUpdateData));

    try {
      // Update memo
      const { data: updatedMemo, error: updateError } = await supabase
        .from("memos")
        .update({
          ...memoUpdateData,
          ...(body.status === "Received"
            ? {
              received_at: new Date().toISOString(),
              received_by: body.received_by || null,
            }
            : {}),
        })
        .eq("memo_id", id)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating memo:", updateError);
        return NextResponse.json({
          error: "Failed to update memo",
          details: updateError.message,
          code: updateError.code,
        }, {
          status: 500,
        });
      }

      console.log("Memo updated successfully:", updatedMemo ? "yes" : "no");
    } catch (updateException: any) {
      console.error("Exception during memo update:", updateException);
      return NextResponse.json({
        error: "Exception during memo update",
        details: updateException.message || String(updateException),
      }, {
        status: 500,
      });
    }

    // If memo is being completed, create expense entries
    if (
      body.status === "Completed" &&
      (body.bata_amount || body.salary_amount)
    ) {
      const total_expense = (parseFloat(body.bata_amount) || 0) +
        (parseFloat(body.salary_amount) || 0);

      // Create financial transaction for the expense
      const { error: txnError } = await supabase
        .from("financialtransactions")
        .insert({
          branch_id: existingMemo.to_branch_id,
          transaction_type: "Expense",
          amount: total_expense,
          payment_mode: "Cash",
          description: `Expense for memo ${existingMemo.memo_number} - BATA: ${
            body.bata_amount || 0
          }, Salary: ${body.salary_amount || 0}`,
          reference_number: existingMemo.memo_number,
          approval_status: "Approved",
        });

      if (txnError) {
        console.error("Error creating expense transaction:", txnError);
        // Continue anyway, don't fail the memo update
      }

      // Store driver expenses in a separate table or as JSON
      if (
        driver_expenses && Array.isArray(driver_expenses) &&
        driver_expenses.length > 0
      ) {
        try {
          // First, check if we have a driver_expenses table
          const { error: tableCheckError } = await supabase
            .from("driver_expenses")
            .select("expense_id")
            .limit(1);

          if (tableCheckError && tableCheckError.code === "42P01") {
            // Table doesn't exist, store as JSON in the memo
            const { error: jsonUpdateError } = await supabase
              .from("memos")
              .update({
                driver_expenses_json: driver_expenses,
              })
              .eq("memo_id", id);

            if (jsonUpdateError) {
              console.error(
                "Error storing driver expenses as JSON:",
                jsonUpdateError,
              );
            }
          } else {
            // Table exists, store records there
            const expenseRecords = driver_expenses.map((expense) => ({
              memo_id: id,
              driver_id: expense.driver_id,
              driver_number: expense.driver_number,
              bata_amount: expense.bata_amount || 0,
              salary_amount: expense.salary_amount || 0,
              created_at: new Date().toISOString(),
            }));

            const { error: insertError } = await supabase
              .from("driver_expenses")
              .insert(expenseRecords);

            if (insertError) {
              console.error("Error storing driver expenses:", insertError);
            }
          }
        } catch (error: any) {
          console.error("Error handling driver expenses:", error);
          console.error("Details:", error.message || String(error));
        }
      }
    }

    // Get the updated memo to return
    const { data: finalMemo, error: finalError } = await supabase
      .from("memos")
      .select(`
        *,
        vehicle:vehicles(registration_number, vehicle_type),
        from_branch:branches!from_branch_id(name, code),
        to_branch:branches!to_branch_id(name, code),
        creator:users!created_by(name)
      `)
      .eq("memo_id", id)
      .single();

    if (finalError) {
      console.error("Error fetching updated memo:", finalError);
      return NextResponse.json({
        error: "Memo was updated but could not be retrieved",
        details: finalError.message,
      }, { status: 500 });
    }

    return NextResponse.json({ memo: finalMemo });
  } catch (error: any) {
    console.error(`Error in PATCH /api/memos/${params.id}:`, error);
    return NextResponse.json({
      error: "Internal server error",
      details: error.message || String(error),
    }, {
      status: 500,
    });
  }
}

// DELETE /api/memos/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Check if memo exists
    const { data: existingMemo, error: fetchError } = await supabase
      .from("memos")
      .select("*")
      .eq("memo_id", id)
      .single();

    if (fetchError || !existingMemo) {
      return NextResponse.json({ error: "Memo not found" }, { status: 404 });
    }

    // Only allow deletion of memos in 'Created' status
    if (existingMemo.status !== "Created") {
      return NextResponse.json(
        { error: "Cannot delete memo that is not in Created status" },
        { status: 400 },
      );
    }

    // Delete memo
    const { error: deleteError } = await supabase
      .from("memos")
      .delete()
      .eq("memo_id", id);

    if (deleteError) {
      console.error("Error deleting memo:", deleteError);
      return NextResponse.json({ error: "Failed to delete memo" }, {
        status: 500,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error in DELETE /api/memos/${params.id}:`, error);
    return NextResponse.json({
      error: "Internal server error",
      details: error.message || String(error),
    }, {
      status: 500,
    });
  }
}
