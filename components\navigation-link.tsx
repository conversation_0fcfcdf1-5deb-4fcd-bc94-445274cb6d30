"use client"

import { <PERSON>actNode } from "react"
import { useRouter } from "next/navigation"

interface NavigationLinkProps {
  href: string
  children: ReactNode
  className?: string
  onClick?: () => void
}

export function NavigationLink({ href, children, className = "", onClick }: NavigationLinkProps) {
  const router = useRouter()
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    
    // Call the onClick handler if provided
    if (onClick) {
      onClick()
    }
    
    // Use Next.js router for navigation
    console.log(`NavigationLink: Navigating to ${href}`)
    router.push(href)
  }
  
  return (
    <a href={href} className={className} onClick={handleClick}>
      {children}
    </a>
  )
}
