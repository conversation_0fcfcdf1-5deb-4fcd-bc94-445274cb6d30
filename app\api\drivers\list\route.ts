import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/drivers/list
export async function GET(request: NextRequest) {
  try {
    // Get active drivers with their IDs and phone numbers
    const { data, error } = await supabase
      .from("drivers")
      .select("driver_id, name, phone_number, driver_number")
      .eq("status", "Active")
      .order("name");

    if (error) {
      console.error("Error fetching drivers:", error);
      return NextResponse.json(
        { error: "Failed to fetch drivers" },
        { status: 500 },
      );
    }

    return NextResponse.json({ drivers: data || [] });
  } catch (error: any) {
    console.error("Error in GET /api/drivers/list:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
