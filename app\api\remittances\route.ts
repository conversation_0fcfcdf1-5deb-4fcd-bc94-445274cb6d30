import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/remittances
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const branch_id = url.searchParams.get("branch_id");
    const status = url.searchParams.get("status");
    const from_date = url.searchParams.get("from_date");
    const to_date = url.searchParams.get("to_date");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    // Build query
    let query = supabase
      .from("remittance")
      .select(`
        *,
        branch:branches(name, code),
        submitter:users!submitted_by(name)
      `);

    // Add filters if provided
    if (branch_id) {
      query = query.eq("branch_id", branch_id);
    }

    if (status) {
      query = query.eq("status", status);
    }

    if (from_date) {
      try {
        // Validate date
        const fromDateObj = new Date(from_date);
        if (!isNaN(fromDateObj.getTime())) {
          query = query.gte("created_at", fromDateObj.toISOString());
        } else {
          console.warn(`Invalid from_date: ${from_date}, ignoring this filter`);
        }
      } catch (error) {
        console.warn(
          `Error parsing from_date: ${from_date}, ignoring this filter`,
          error,
        );
      }
    }

    if (to_date) {
      try {
        // Validate date
        const toDateObj = new Date(to_date);
        if (!isNaN(toDateObj.getTime())) {
          query = query.lte("created_at", toDateObj.toISOString());
        } else {
          console.warn(`Invalid to_date: ${to_date}, ignoring this filter`);
        }
      } catch (error) {
        console.warn(
          `Error parsing to_date: ${to_date}, ignoring this filter`,
          error,
        );
      }
    }

    // Add pagination
    query = query.order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Execute query
    const { data: remittances, error, count } = await query;

    if (error) {
      console.error("Error fetching remittances:", error);
      return NextResponse.json({ error: "Failed to fetch remittances" }, {
        status: 500,
      });
    }

    return NextResponse.json({ remittances, count });
  } catch (error: any) {
    console.error("Error in GET /api/remittances:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/remittances
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.branch_id || !body.remittable_amount || !body.method) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: branch_id, remittable_amount, method",
        },
        { status: 400 },
      );
    }

    // Get user ID from the users table
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("user_id")
      .eq("auth_id", session.user.id)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      return NextResponse.json({ error: "Failed to fetch user" }, {
        status: 500,
      });
    }

    const userId = userData?.user_id;

    // Create remittance
    const { data: remittance, error } = await supabase
      .from("remittance")
      .insert({
        branch_id: body.branch_id,
        total_collections: body.total_collections || null,
        approved_expenses: body.approved_expenses || null,
        float_balance: body.float_balance || null,
        remittable_amount: body.remittable_amount,
        method: body.method,
        proof_url: body.proof_url || null,
        reference_id: body.reference_id || null,
        status: "Submitted",
        submitted_by: userId,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating remittance:", error);
      return NextResponse.json({ error: "Failed to create remittance" }, {
        status: 500,
      });
    }

    // Create financial transaction for the remittance
    const { data: transaction, error: txnError } = await supabase
      .from("financial_transactions")
      .insert({
        branch_id: body.branch_id,
        transaction_type: "Remittance",
        amount: body.remittable_amount,
        payment_method: body.method,
        reference_number: `RMT-${remittance.remittance_id}`,
        description: body.notes || "Branch remittance to head office",
        status: "Pending",
        transaction_date: new Date().toISOString(),
      })
      .select()
      .single();

    if (txnError) {
      console.error(
        "Error creating financial transaction for remittance:",
        txnError,
      );
      // Continue anyway, as the remittance was created successfully
    }

    return NextResponse.json({ remittance, transaction: transaction || null });
  } catch (error: any) {
    console.error("Error in POST /api/remittances:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
