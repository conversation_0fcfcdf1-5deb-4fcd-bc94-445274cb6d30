import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/financial-transactions/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = params.id;

    // Get transaction by ID
    const { data: transaction, error } = await supabase
      .from("financial_transactions")
      .select(`
        *,
        branch:branches(name, code)
      `)
      .eq("transaction_id", id)
      .single();

    if (error) {
      console.error("Error fetching financial transaction:", error);
      return NextResponse.json({
        error: "Failed to fetch financial transaction",
      }, {
        status: 500,
      });
    }

    if (!transaction) {
      return NextResponse.json({ error: "Financial transaction not found" }, {
        status: 404,
      });
    }

    return NextResponse.json({ transaction });
  } catch (error: any) {
    console.error(
      `Error in GET /api/financial-transactions/${params.id}:`,
      error,
    );
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// PATCH /api/financial-transactions/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = params.id;
    const body = await request.json();

    // Get existing transaction
    const { data: existingTransaction, error: fetchError } = await supabase
      .from("financial_transactions")
      .select("*")
      .eq("transaction_id", id)
      .single();

    if (fetchError) {
      console.error("Error fetching financial transaction:", fetchError);
      return NextResponse.json({
        error: "Failed to fetch financial transaction",
      }, {
        status: 500,
      });
    }

    if (!existingTransaction) {
      return NextResponse.json({ error: "Financial transaction not found" }, {
        status: 404,
      });
    }

    // Update transaction
    const { data: updatedTransaction, error: updateError } = await supabase
      .from("financial_transactions")
      .update({
        status: body.approval_status ||
          existingTransaction.status,
        description: body.description || existingTransaction.description,
        attachment_url: body.attachment_url ||
          existingTransaction.attachment_url,
      })
      .eq("transaction_id", id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating financial transaction:", updateError);
      return NextResponse.json({
        error: "Failed to update financial transaction",
      }, {
        status: 500,
      });
    }

    return NextResponse.json({ transaction: updatedTransaction });
  } catch (error: any) {
    console.error(
      `Error in PATCH /api/financial-transactions/${params.id}:`,
      error,
    );
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
