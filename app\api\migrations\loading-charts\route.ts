import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";
import {
  CREATE_LOADING_CHART_ITEMS_TABLE,
  CREATE_LOADING_CHARTS_TABLE,
  CREATE_PARCEL_MOVEMENT_TRIGGER,
} from "./sql";

// POST /api/migrations/loading-charts
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is an admin
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("id", session.user.id)
      .single();

    if (userError || !user || user.role !== "Admin") {
      return NextResponse.json({ error: "Only admins can run migrations" }, {
        status: 403,
      });
    }

    // Create the stored procedures
    const { error: createFnError1 } = await supabase.rpc("exec_sql", {
      sql: CREATE_LOADING_CHARTS_TABLE,
    });
    if (createFnError1) {
      console.error(
        "Error creating function create_loading_charts_table:",
        createFnError1,
      );
      return NextResponse.json({ error: "Failed to create function" }, {
        status: 500,
      });
    }

    const { error: createFnError2 } = await supabase.rpc("exec_sql", {
      sql: CREATE_LOADING_CHART_ITEMS_TABLE,
    });
    if (createFnError2) {
      console.error(
        "Error creating function create_loading_chart_items_table:",
        createFnError2,
      );
      return NextResponse.json({ error: "Failed to create function" }, {
        status: 500,
      });
    }

    const { error: createFnError3 } = await supabase.rpc("exec_sql", {
      sql: CREATE_PARCEL_MOVEMENT_TRIGGER,
    });
    if (createFnError3) {
      console.error(
        "Error creating function create_parcel_movement_trigger:",
        createFnError3,
      );
      return NextResponse.json({ error: "Failed to create function" }, {
        status: 500,
      });
    }

    // Execute the functions to create tables
    const { error: chartsError } = await supabase.rpc(
      "create_loading_charts_table",
    );
    if (chartsError) {
      console.error("Error creating loading_charts table:", chartsError);
      return NextResponse.json({
        error: "Failed to create loading_charts table",
      }, { status: 500 });
    }

    const { error: itemsError } = await supabase.rpc(
      "create_loading_chart_items_table",
    );
    if (itemsError) {
      console.error("Error creating loading_chart_items table:", itemsError);
      return NextResponse.json({
        error: "Failed to create loading_chart_items table",
      }, { status: 500 });
    }

    const { error: triggerError } = await supabase.rpc(
      "create_parcel_movement_trigger",
    );
    if (triggerError) {
      console.error("Error creating parcel movement trigger:", triggerError);
      return NextResponse.json({
        error: "Failed to create parcel movement trigger",
      }, { status: 500 });
    }

    return NextResponse.json({
      message: "Migration completed successfully",
    });
  } catch (error: any) {
    console.error("Error in POST /api/migrations/loading-charts:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
