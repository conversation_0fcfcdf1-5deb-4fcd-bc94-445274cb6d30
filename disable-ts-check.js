/**
 * Script to disable TypeScript checking in tsconfig.json
 */

const fs = require('fs');
const path = require('path');

console.log('Disabling TypeScript strict checking...');

// Path to tsconfig.json
const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');

try {
  // Read the current tsconfig.json
  const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
  
  // Disable strict mode
  tsconfig.compilerOptions.strict = false;
  
  // Add noImplicitAny: false
  tsconfig.compilerOptions.noImplicitAny = false;
  
  // Add strictNullChecks: false
  tsconfig.compilerOptions.strictNullChecks = false;
  
  // Write the updated tsconfig.json
  fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2), 'utf8');
  
  console.log('TypeScript strict checking disabled successfully!');
} catch (error) {
  console.error('Error updating tsconfig.json:', error.message);
  process.exit(1);
}
