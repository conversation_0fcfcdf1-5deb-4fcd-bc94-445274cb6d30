"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { ArrowDownToLine, CheckCircle, Filter, Search, Loader2, AlertCircle, MessageSquare } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format } from "date-fns"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { getUserBranchIdAsync } from "@/lib/branch-utils"

export function MemoList() {
  const { toast } = useToast()
  const [memos, setMemos] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedMemo, setSelectedMemo] = useState<any>(null)
  const [isReceiveDialogOpen, setIsReceiveDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [fuelLitres, setFuelLitres] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [error, setError] = useState<string | null>(null)

  const [userBranchId, setUserBranchId] = useState<string | null>(null)

  // Fetch user's branch ID and memos on component mount
  useEffect(() => {
    const getUserBranch = async () => {
      try {
        // Get branch ID from API
        const branchId = await getUserBranchIdAsync();

        if (branchId !== null) {
          // Set the branch ID from API
          setUserBranchId(branchId.toString());

          // Fetch memos with the branch ID
          fetchMemos(branchId.toString());
        } else {
          console.error('Failed to get branch ID');
          // Show a toast notification to inform the user
          toast({
            title: "Authentication Issue",
            description: "Could not determine your branch. Please try logging in again.",
            variant: "destructive",
          });
          setError("Authentication issue. Please try logging in again.");
        }
      } catch (error) {
        console.error('Error in getUserBranch:', error);
        setError("Failed to get branch information. Please try refreshing the page.");
      }
    };

    getUserBranch();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps



  // Fetch memos from API
  const fetchMemos = async (branchId?: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // Build URL with user's branch ID
      const url = new URL('/api/memos', window.location.origin)
      if (branchId) {
        // Use branch_id parameter instead of user_branch_id
        url.searchParams.append('branch_id', branchId)
      }

      // Make sure we're using the correct origin
      console.log('Using origin:', window.location.origin)

      console.log('Fetching memos with URL:', url.toString())
      const response = await fetch(url.toString())
      const data = await response.json()

      if (response.ok) {
        setMemos(data.memos || [])
      } else {
        setError(data.error || 'Failed to fetch memos')
        toast({
          title: "Error",
          description: data.error || "Failed to fetch memos",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error fetching memos:', error)
      setError('Failed to fetch memos')
      toast({
        title: "Error",
        description: "Failed to fetch memos. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch driver numbers for a memo
  const fetchDriverNumbers = async (memo: any) => {
    if (!memo.driver_ids || !Array.isArray(memo.driver_ids) || memo.driver_ids.length === 0) {
      setSelectedMemo(memo)
      return
    }

    try {
      const response = await fetch(`${window.location.origin}/api/memos/drivers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          driver_ids: memo.driver_ids
        }),
      })

      const data = await response.json()

      if (response.ok && data.driver_numbers) {
        setSelectedMemo({
          ...memo,
          driver_numbers: data.driver_numbers
        })
      } else {
        console.error('Error fetching driver numbers:', data.error)
        setSelectedMemo(memo)
      }
    } catch (error) {
      console.error('Error fetching driver numbers:', error)
      setSelectedMemo(memo)
    }
  }

  // Filter memos based on status and search query
  const filteredMemos = memos.filter(memo => {
    // Filter by status
    if (statusFilter !== "all" && memo.status !== statusFilter) {
      return false
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        memo.memo_number.toLowerCase().includes(query) ||
        memo.vehicle?.registration_number?.toLowerCase().includes(query) ||
        memo.from_branch?.name?.toLowerCase().includes(query) ||
        memo.to_branch?.name?.toLowerCase().includes(query)
      )
    }

    return true
  })

  // Handle receiving a memo
  const handleReceive = async () => {
    if (!selectedMemo || !fuelLitres) {
      toast({
        title: "Missing Information",
        description: "Please enter fuel consumption details",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Get current user ID
      const userResponse = await fetch(`${window.location.origin}/api/auth/session`)
      const userData = await userResponse.json()
      const userId = userData?.user?.id

      const response = await fetch(`${window.location.origin}/api/memos/${selectedMemo.memo_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'Received',
          fuel_consumed: parseFloat(fuelLitres),
          received_by: userId
        }),
      })

      const data = await response.json()

      if (response.ok) {
        // Update the memo in the local state
        setMemos(prevMemos =>
          prevMemos.map(memo =>
            memo.memo_id === selectedMemo.memo_id
              ? { ...memo, ...data.memo }
              : memo
          )
        )

        toast({
          title: "Memo Received",
          description: `Successfully received memo ${selectedMemo.memo_number} with fuel consumption of ${fuelLitres}L.`,
        })

        setIsReceiveDialogOpen(false)
        setSelectedMemo(null)
        setFuelLitres("")
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to receive memo",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error receiving memo:', error)
      toast({
        title: "Error",
        description: "Failed to receive memo. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
