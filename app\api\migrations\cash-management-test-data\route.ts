import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";
import fs from "fs";
import path from "path";

// POST /api/migrations/cash-management-test-data
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("role")
      .eq("auth_id", session.user.id)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      return NextResponse.json({ error: "Failed to fetch user" }, { status: 500 });
    }

    if (userData.role !== "Super Admin" && userData.role !== "Admin") {
      return NextResponse.json({ error: "Unauthorized. Admin access required." }, { status: 403 });
    }

    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'db', 'cash_management_test_data.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error: migrationError } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });

    if (migrationError) {
      console.error("Error running cash management test data script:", migrationError);
      return NextResponse.json({ error: "Failed to run test data script" }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: "Cash management test data loaded successfully" 
    });
  } catch (error: any) {
    console.error("Error in POST /api/migrations/cash-management-test-data:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
