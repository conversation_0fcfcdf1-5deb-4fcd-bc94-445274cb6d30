import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/branch-balance/daily
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const branch_id = url.searchParams.get("branch_id");
    const from_date = url.searchParams.get("from_date");
    const to_date = url.searchParams.get("to_date");
    const limit = parseInt(url.searchParams.get("limit") || "7"); // Default to 7 days

    if (!branch_id) {
      return NextResponse.json({ error: "Branch ID is required" }, {
        status: 400,
      });
    }

    // Calculate date range if not provided
    let dateFrom, dateTo;
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    try {
      if (from_date && to_date) {
        // Validate dates
        const fromDateObj = new Date(from_date);
        const toDateObj = new Date(to_date);

        if (isNaN(fromDateObj.getTime()) || isNaN(toDateObj.getTime())) {
          throw new Error("Invalid date format");
        }

        // Ensure dates are not in the future
        if (fromDateObj > today) {
          console.warn("From date is in the future, using today instead");
          dateFrom = new Date();
          dateFrom.setHours(0, 0, 0, 0); // Start of today
        } else {
          dateFrom = fromDateObj;
        }

        if (toDateObj > today) {
          console.warn("To date is in the future, using today instead");
          dateTo = new Date();
          dateTo.setHours(23, 59, 59, 999); // End of today
        } else {
          dateTo = toDateObj;
        }
      } else {
        // Default to last 7 days
        dateTo = new Date();
        dateFrom = new Date();
        dateFrom.setDate(dateTo.getDate() - (limit - 1));
      }
    } catch (error) {
      console.warn("Error parsing date range, using default range", error);
      // Default to last 7 days
      dateTo = new Date();
      dateFrom = new Date();
      dateFrom.setDate(dateTo.getDate() - (limit - 1));
    }

    // Format dates for query
    const fromDateStr = dateFrom.toISOString().split("T")[0];
    const toDateStr = dateTo.toISOString().split("T")[0];

    // Get ledger entries for the date range (newest first)
    const { data: ledgerEntries, error: ledgerError } = await supabase
      .from("accountsledger")
      .select("*")
      .eq("branch_id", branch_id)
      .gte("ledger_date", fromDateStr)
      .lte("ledger_date", toDateStr)
      .order("ledger_date", { ascending: false });

    if (ledgerError) {
      console.error("Error fetching ledger entries:", ledgerError);
      return NextResponse.json({ error: "Failed to fetch ledger entries" }, {
        status: 500,
      });
    }

    // If no ledger entries, we need to calculate them from transactions
    if (ledgerEntries.length === 0) {
      // Generate an array of dates in the range (excluding future dates)
      const dates = [];
      const now = new Date();
      now.setHours(23, 59, 59, 999); // End of today

      // Determine the end date (either dateTo or today, whichever is earlier)
      const endDate = new Date(dateTo <= now ? dateTo : now);
      let currentDate = new Date(endDate);

      // Generate dates in descending order (newest first)
      while (currentDate >= dateFrom) {
        dates.push(new Date(currentDate).toISOString().split("T")[0]);
        currentDate.setDate(currentDate.getDate() - 1);
      }

      // Format dates with time for query
      const fromDateTimeStr = `${fromDateStr}T00:00:00Z`;
      const toDateTimeStr = `${toDateStr}T23:59:59Z`;

      // Get all transactions in the date range
      const { data: transactions, error: txnError } = await supabase
        .from("financial_transactions")
        .select("*")
        .eq("branch_id", branch_id)
        .gte("transaction_date", fromDateTimeStr)
        .lte("transaction_date", toDateTimeStr);

      if (txnError) {
        console.error("Error fetching transactions:", txnError);
        return NextResponse.json({ error: "Failed to fetch transactions" }, {
          status: 500,
        });
      }

      // Group transactions by date
      const txnsByDate: Record<string, any[]> = {};
      transactions.forEach((txn) => {
        if (!txn.transaction_date) {
          console.warn("Transaction missing transaction_date:", txn);
          return; // Skip transactions without a date
        }

        try {
          const txnDate =
            new Date(txn.transaction_date).toISOString().split("T")[0];
          if (!txnsByDate[txnDate]) {
            txnsByDate[txnDate] = [];
          }
          txnsByDate[txnDate].push(txn);
        } catch (error) {
          console.warn(
            `Invalid transaction_date format: ${txn.transaction_date}`,
            error,
          );
          // Skip transactions with invalid dates
        }
      });

      // Calculate daily stats for each date
      const dailyStats = await Promise.all(dates.map(async (date) => {
        const dayTxns = txnsByDate[date] || [];

        // Calculate totals
        const income = dayTxns
          .filter((t) => {
            // Only include collections that are approved
            if (
              t.transaction_type !== "Collection" ||
              t.approval_status !== "Approved"
            ) {
              return false;
            }

            // Check if this transaction should be included in branch balance
            // Parse metadata if it exists
            let metadata = {};
            try {
              if (t.metadata) {
                metadata = typeof t.metadata === "string"
                  ? JSON.parse(t.metadata)
                  : t.metadata;
              }
            } catch (e) {
              console.error("Error parsing transaction metadata:", e);
            }

            // If metadata explicitly says not to include in branch balance, exclude it
            if (metadata && metadata.include_in_branch_balance === false) {
              return false;
            }

            // If it's an online payment (not cash), exclude it from branch balance
            // This is a fallback for transactions without metadata
            if (
              t.payment_method &&
              ["online", "upi", "card", "bank_transfer"].includes(
                t.payment_method.toLowerCase(),
              ) &&
              !metadata.include_in_branch_balance
            ) {
              return false;
            }

            return true;
          })
          .reduce((sum, t) => sum + parseFloat(t.amount), 0);

        // Get the date range for this day
        const dayStart = `${date}T00:00:00Z`;
        const dayEnd = `${date}T23:59:59Z`;

        // Get approved expenses from the expenses table for this day
        const { data: approvedExpenses, error: approvedExpError } =
          await supabase
            .from("expenses")
            .select("amount")
            .eq("branch_id", branch_id)
            .eq("approval_status", "Approved")
            .gte("approved_at", dayStart)
            .lte("approved_at", dayEnd);

        if (approvedExpError) {
          console.error(
            `Error fetching approved expenses for ${date}:`,
            approvedExpError,
          );
        }

        // Calculate expenses from either the expenses table or financial transactions
        const expenses = approvedExpenses && approvedExpenses.length > 0
          ? approvedExpenses.reduce((sum, e) => sum + parseFloat(e.amount), 0)
          : dayTxns
            .filter((t) =>
              t.transaction_type === "Expense" &&
              t.approval_status === "Approved"
            )
            .reduce((sum, t) => sum + parseFloat(t.amount), 0);

        const remittances = dayTxns
          .filter((t) =>
            t.transaction_type === "Remittance" &&
            t.approval_status === "Approved"
          )
          .reduce((sum, t) => sum + parseFloat(t.amount), 0);

        // Group expenses by category
        const expensesByCategory: Record<string, number> = {};

        // Get approved expenses with category from the expenses table for this day
        const { data: expensesWithCategory, error: expCategoryError } =
          await supabase
            .from("expenses")
            .select("amount, category")
            .eq("branch_id", branch_id)
            .eq("approval_status", "Approved")
            .gte("approved_at", dayStart)
            .lte("approved_at", dayEnd);

        if (expCategoryError) {
          console.error(
            `Error fetching expense categories for ${date}:`,
            expCategoryError,
          );

          // Fallback to financial transactions if expenses table query fails
          dayTxns
            .filter((t) =>
              t.transaction_type === "Expense" &&
              t.approval_status === "Approved"
            )
            .forEach((t) => {
              // Extract category from description or use "Other"
              const category = t.description?.split(":")[0]?.trim() || "Other";
              if (!expensesByCategory[category]) {
                expensesByCategory[category] = 0;
              }
              expensesByCategory[category] += parseFloat(t.amount);
            });
        } else if (expensesWithCategory && expensesWithCategory.length > 0) {
          // Use expenses table data
          expensesWithCategory.forEach((e) => {
            const category = e.category || "Other";
            if (!expensesByCategory[category]) {
              expensesByCategory[category] = 0;
            }
            expensesByCategory[category] += parseFloat(e.amount);
          });
        } else {
          // Fallback to financial transactions if no expenses found
          dayTxns
            .filter((t) =>
              t.transaction_type === "Expense" &&
              t.approval_status === "Approved"
            )
            .forEach((t) => {
              // Extract category from description or use "Other"
              const category = t.description?.split(":")[0]?.trim() || "Other";
              if (!expensesByCategory[category]) {
                expensesByCategory[category] = 0;
              }
              expensesByCategory[category] += parseFloat(t.amount);
            });
        }

        return {
          date,
          branch_id: parseInt(branch_id),
          total_collection: income,
          total_expenses: expenses,
          remitted: remittances,
          net_amount: income - expenses - remittances,
          transaction_count: dayTxns.length,
          expenses_breakdown: expensesByCategory,
          pending_collections: dayTxns
            .filter((t) =>
              t.transaction_type === "Collection" &&
              t.approval_status === "Pending"
            )
            .length,
        };
      }));

      return NextResponse.json({ daily_stats: dailyStats });
    }

    // If we have ledger entries, enhance them with transaction details
    const enhancedLedgerEntries = await Promise.all(
      ledgerEntries.map(async (entry) => {
        // Get transactions for this date
        const entryDate = new Date(entry.ledger_date);
        const nextDate = new Date(entryDate);
        nextDate.setDate(nextDate.getDate() + 1);

        // Format dates with time for query
        const entryDateTimeStr = entryDate.toISOString();
        const nextDateTimeStr = nextDate.toISOString();

        const { data: dayTxns, error: dayTxnError } = await supabase
          .from("financial_transactions")
          .select("*")
          .eq("branch_id", branch_id)
          .gte("transaction_date", entryDateTimeStr)
          .lt("transaction_date", nextDateTimeStr);

        if (dayTxnError) {
          console.error(
            `Error fetching transactions for ${entry.ledger_date}:`,
            dayTxnError,
          );
          return entry;
        }

        // Group expenses by category
        const expensesByCategory: Record<string, number> = {};

        // Get the date range for this day
        const dayStart = `${entry.ledger_date}T00:00:00Z`;
        const dayEnd = `${entry.ledger_date}T23:59:59Z`;

        // Get approved expenses with category from the expenses table for this day
        const { data: expensesWithCategory, error: expCategoryError } =
          await supabase
            .from("expenses")
            .select("amount, category")
            .eq("branch_id", branch_id)
            .eq("approval_status", "Approved")
            .gte("approved_at", dayStart)
            .lte("approved_at", dayEnd);

        if (expCategoryError) {
          console.error(
            `Error fetching expense categories for ${entry.ledger_date}:`,
            expCategoryError,
          );

          // Fallback to financial transactions if expenses table query fails
          dayTxns
            .filter((t) =>
              t.transaction_type === "Expense" &&
              t.approval_status === "Approved"
            )
            .forEach((t) => {
              // Extract category from description or use "Other"
              const category = t.description?.split(":")[0]?.trim() || "Other";
              if (!expensesByCategory[category]) {
                expensesByCategory[category] = 0;
              }
              expensesByCategory[category] += parseFloat(t.amount);
            });
        } else if (expensesWithCategory && expensesWithCategory.length > 0) {
          // Use expenses table data
          expensesWithCategory.forEach((e) => {
            const category = e.category || "Other";
            if (!expensesByCategory[category]) {
              expensesByCategory[category] = 0;
            }
            expensesByCategory[category] += parseFloat(e.amount);
          });
        } else {
          // Fallback to financial transactions if no expenses found
          dayTxns
            .filter((t) =>
              t.transaction_type === "Expense" &&
              t.approval_status === "Approved"
            )
            .forEach((t) => {
              // Extract category from description or use "Other"
              const category = t.description?.split(":")[0]?.trim() || "Other";
              if (!expensesByCategory[category]) {
                expensesByCategory[category] = 0;
              }
              expensesByCategory[category] += parseFloat(t.amount);
            });
        }

        return {
          ...entry,
          net_amount: entry.total_collection - entry.total_expense -
            entry.remitted,
          transaction_count: dayTxns.length,
          expenses_breakdown: expensesByCategory,
          pending_collections: dayTxns
            .filter((t) =>
              t.transaction_type === "Collection" &&
              t.approval_status === "Pending"
            )
            .length,
        };
      }),
    );

    return NextResponse.json({ daily_stats: enhancedLedgerEntries });
  } catch (error: any) {
    console.error("Error in GET /api/branch-balance/daily:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
