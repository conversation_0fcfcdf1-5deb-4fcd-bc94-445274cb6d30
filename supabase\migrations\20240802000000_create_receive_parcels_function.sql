-- Function to update parcel status when a parcel is received
CREATE OR REPLACE FUNCTION update_parcel_status_on_receiving()
RETURNS TRIGGER AS $$
DECLARE
  v_delivery_branch_id INT;
  v_current_branch_id INT;
  v_new_status TEXT;
  v_user_id INT;
BEGIN
  -- Get the delivery branch ID for the parcel
  SELECT delivery_branch_id INTO v_delivery_branch_id
  FROM parcels
  WHERE lr_number = NEW.lr_number;

  -- Get the current branch ID (destination of the loading chart)
  SELECT destination_branch_id INTO v_current_branch_id
  FROM loading_charts
  WHERE chart_id = NEW.chart_id;

  -- Determine the new status based on whether the parcel is at its final destination
  IF v_delivery_branch_id = v_current_branch_id THEN
    -- <PERSON><PERSON><PERSON> has reached its final destination
    v_new_status := 'To Be Delivered';
  ELSE
    -- <PERSON><PERSON><PERSON> is at an intermediate branch
    v_new_status := 'In Transit';
  END IF;

  -- Update parcel status
  UPDATE parcels
  SET current_status = v_new_status
  WHERE lr_number = NEW.lr_number;

  -- Get the user ID of the operator
  SELECT user_id INTO v_user_id
  FROM users
  WHERE auth_id = NEW.received_by;

  -- Create an operation record
  INSERT INTO operations (
    operation_type,
    vehicle_id,
    parcel_id,
    confirmed_item_count,
    total_item_count,
    operator_id
  )
  SELECT
    'Unload'::op_type,
    lc.vehicle_id,
    p.parcel_id,
    NEW.received_quantity,
    p.number_of_items,
    v_user_id
  FROM
    loading_charts lc
    JOIN parcels p ON p.lr_number = NEW.lr_number
  WHERE
    lc.chart_id = NEW.chart_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add a received_by column to loading_chart_items if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'loading_chart_items'
    AND column_name = 'received_by'
  ) THEN
    ALTER TABLE loading_chart_items ADD COLUMN received_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Add a received_quantity column to loading_chart_items if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'loading_chart_items'
    AND column_name = 'received_quantity'
  ) THEN
    ALTER TABLE loading_chart_items ADD COLUMN received_quantity INT;
  END IF;
END $$;

-- Add a received_at column to loading_chart_items if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'loading_chart_items'
    AND column_name = 'received_at'
  ) THEN
    ALTER TABLE loading_chart_items ADD COLUMN received_at TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Create a trigger to update parcel status when a loading chart item is received
DROP TRIGGER IF EXISTS update_parcel_status_on_receiving_trigger ON loading_chart_items;

CREATE TRIGGER update_parcel_status_on_receiving_trigger
AFTER UPDATE OF status ON loading_chart_items
FOR EACH ROW
WHEN (NEW.status = 'Received' AND OLD.status = 'Pending')
EXECUTE FUNCTION update_parcel_status_on_receiving();
