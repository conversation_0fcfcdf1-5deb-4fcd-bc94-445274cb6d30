-- Add driver_expenses_json column to memos table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'memos' AND column_name = 'driver_expenses_json'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN driver_expenses_json JSONB;
  END IF;
END
$$;

-- Create driver_expenses table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.driver_expenses (
  expense_id SERIAL PRIMARY KEY,
  memo_id INT REFERENCES memos(memo_id) ON DELETE CASCADE,
  driver_id INT NOT NULL,
  driver_number VARCHAR(50),
  bata_amount NUMERIC(10, 2) DEFAULT 0,
  salary_amount NUMERIC(10, 2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS driver_expenses_memo_id_idx ON driver_expenses(memo_id);
CREATE INDEX IF NOT EXISTS driver_expenses_driver_id_idx ON driver_expenses(driver_id);
