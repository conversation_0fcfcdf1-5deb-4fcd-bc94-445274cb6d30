@media print {
  /* Hide everything by default */
  body * {
    visibility: hidden;
  }

  /* Only show the print container and its children */
  .print-container,
  .print-container * {
    visibility: visible;
  }

  /* Position the print container at the top */
  .print-container {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    padding: 20mm !important;
    margin: 0 !important;
  }

  /* Hide non-printable elements */
  .no-print {
    display: none !important;
  }

  /* Reset all height/scroll constraints */
  .print-container,
  .print-container * {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    transform: none !important;
  }

  /* Reset ScrollArea styles for printing */
  [role="region"][tabindex] {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    position: static !important;
    transform: none !important;
  }

  /* Dialog specific print resets */
  .dialog-content {
    position: static !important;
    transform: none !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }

  /* Ensure proper page breaks */
  .print-container {
    page-break-inside: avoid;
  }

  /* Reset any fixed positioning */
  .fixed {
    position: static !important;
  }
}

/* Hide scrollbars during print mode */
body.print-mode {
  overflow: hidden !important;
}

