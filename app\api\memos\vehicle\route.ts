import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/memos/vehicle
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const registration_number = url.searchParams.get("registration_number");
    const user_branch_id = url.searchParams.get("user_branch_id");

    if (!registration_number) {
      return NextResponse.json(
        { error: "Vehicle registration number is required" },
        { status: 400 },
      );
    }

    // First, get the vehicle ID - normalize registration number by removing spaces
    const normalizedRegNumber = registration_number.replace(/\s+/g, "");

    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select("vehicle_id")
      .or(
        `registration_number.ilike.${normalizedRegNumber},registration_number.ilike.%${registration_number}%`,
      )
      .single();

    if (vehicleError) {
      console.error("Error finding vehicle:", vehicleError);
      return NextResponse.json(
        { error: "Vehicle not found" },
        { status: 404 },
      );
    }

    // Then, get the active memo for this vehicle
    let query = supabase
      .from("memos")
      .select(`
        *,
        vehicle:vehicles(registration_number, vehicle_type),
        from_branch:branches!from_branch_id(name, code),
        to_branch:branches!to_branch_id(name, code),
        creator:users!created_by(name)
      `)
      .eq("vehicle_id", vehicle.vehicle_id)
      .in("status", ["Created", "Received"])
      .order("created_at", { ascending: false });

    // If user_branch_id is provided, filter memos by the user's branch
    if (user_branch_id) {
      query = query.eq("from_branch_id", user_branch_id);
    }

    const { data: memos, error: memoError } = await query;

    if (memoError) {
      console.error("Error finding memo:", memoError);
      return NextResponse.json(
        { error: "Failed to find memo for this vehicle" },
        { status: 500 },
      );
    }

    if (!memos || memos.length === 0) {
      // If no memos found with user's branch filter, check if there are any memos at all
      if (user_branch_id) {
        const { data: allMemos, error: allMemosError } = await supabase
          .from("memos")
          .select(`
            *,
            vehicle:vehicles(registration_number, vehicle_type),
            from_branch:branches!from_branch_id(name, code),
            to_branch:branches!to_branch_id(name, code),
            creator:users!created_by(name)
          `)
          .eq("vehicle_id", vehicle.vehicle_id)
          .in("status", ["Created", "Received"])
          .order("created_at", { ascending: false });

        if (!allMemosError && allMemos && allMemos.length > 0) {
          // There are memos, but not from the user's branch
          return NextResponse.json(
            {
              error:
                "This vehicle has an active memo, but it's not from your branch. Only memos from your branch can be loaded.",
              memo: allMemos[0],
              wrong_branch: true,
            },
            { status: 403 },
          );
        }
      }

      return NextResponse.json(
        { error: "No active memo found for this vehicle" },
        { status: 404 },
      );
    }

    // Return the most recent active memo
    return NextResponse.json({ memo: memos[0] });
  } catch (error: any) {
    console.error("Error in GET /api/memos/vehicle:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
