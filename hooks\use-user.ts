"use client";

import { useState, useEffect } from "react";
import { useSupabase } from "@/contexts/supabase-provider";
import { User } from "@/lib/user-helpers";

interface UserDetails extends User {
  // Add any additional fields we need
}

export function useUser() {
  const { supabase, user: authUser } = useSupabase();
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadUserDetails() {
      if (!authUser?.email) {
        setUserDetails(null);
        setLoading(false);
        return;
      }

      try {
        // Get user details from the public users table
        const { data, error } = await supabase
          .from("users")
          .select("*")
          .eq("email", authUser.email)
          .single();

        if (error) {
          console.error("Error fetching user details:", error);
          setUserDetails(null);
        } else {
          setUserDetails(data as UserDetails);
        }
      } catch (error: any) {
        console.error("Exception in loadUserDetails:", error);
        setUserDetails(null);
      } finally {
        setLoading(false);
      }
    }

    loadUserDetails();
  }, [authUser, supabase]);

  return {
    user: authUser,
    userDetails,
    loading,
    isAdmin: userDetails?.role === "Admin" || userDetails?.role === "Super Admin",
    isManager: userDetails?.role === "Manager",
    isSuperAdmin: userDetails?.role === "Super Admin",
  };
}
