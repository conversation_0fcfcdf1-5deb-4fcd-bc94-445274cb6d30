import { NextRequest, NextResponse } from 'next/server';
import { getParcelTypeById, updateParcelType, deleteParcelType } from '@/lib/db-helpers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

// GET /api/parceltypes/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid parcel type ID' }, { status: 400 });
    }
    
    const parcelType = await getParcelTypeById(id);
    
    if (!parcelType) {
      return NextResponse.json({ error: 'Parcel type not found' }, { status: 404 });
    }
    
    return NextResponse.json(parcelType);
  } catch (error: any) {
    console.error(`Error in GET /api/parceltypes/${params.id}:`, error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/parceltypes/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid parcel type ID' }, { status: 400 });
    }
    
    const body = await request.json();
    
    // Update parcel type
    const updatedParcelType = await updateParcelType(id, body);
    
    if (!updatedParcelType) {
      return NextResponse.json({ error: 'Failed to update parcel type' }, { status: 500 });
    }
    
    return NextResponse.json(updatedParcelType);
  } catch (error: any) {
    console.error(`Error in PUT /api/parceltypes/${params.id}:`, error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// DELETE /api/parceltypes/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid parcel type ID' }, { status: 400 });
    }
    
    // Delete parcel type
    const success = await deleteParcelType(id);
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to delete parcel type' }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error(`Error in DELETE /api/parceltypes/${params.id}:`, error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
