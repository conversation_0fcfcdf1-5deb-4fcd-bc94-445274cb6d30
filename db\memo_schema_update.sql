-- Update the memos table to support driver expenses

-- First, add the driver_numbers array to store driver numbers
ALTER TABLE memos
ADD COLUMN driver_numbers TEXT[] DEFAULT NULL;

-- Add a JSONB column to store detailed driver expenses
ALTER TABLE memos
ADD COLUMN driver_expenses JSONB DEFAULT NULL;

-- Create a function to update driver_numbers when a memo is created or updated
CREATE OR REPLACE FUNCTION update_memo_driver_numbers()
RETURNS TRIGGER AS $$
DECLARE
  driver_number_record RECORD;
  driver_numbers TEXT[] := '{}';
  i INTEGER := 0;
BEGIN
  -- Only proceed if driver_ids is not null
  IF NEW.driver_ids IS NOT NULL THEN
    -- Initialize the array with the same length as driver_ids
    driver_numbers := array_fill(NULL::TEXT, ARRAY[array_length(NEW.driver_ids, 1)]);
    
    -- Loop through each driver_id and get the driver_number
    FOR i IN 1..array_length(NEW.driver_ids, 1) LOOP
      SELECT driver_number INTO driver_number_record
      FROM drivers
      WHERE driver_id = NEW.driver_ids[i];
      
      IF FOUND THEN
        driver_numbers[i] := driver_number_record.driver_number;
      END IF;
    END LOOP;
    
    -- Set the driver_numbers array
    NEW.driver_numbers := driver_numbers;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update driver_numbers when a memo is inserted or updated
CREATE TRIGGER update_memo_driver_numbers_trigger
BEFORE INSERT OR UPDATE ON memos
FOR EACH ROW
EXECUTE FUNCTION update_memo_driver_numbers();

-- Sample query to get memos with driver details
/*
SELECT 
  m.memo_id,
  m.memo_number,
  m.status,
  m.driver_ids,
  m.driver_numbers,
  m.driver_expenses,
  m.bata_amount,
  m.salary_amount,
  m.total_expense
FROM memos m
WHERE m.status = 'Completed'
ORDER BY m.created_at DESC;
*/

-- Sample query to get driver details for a specific memo
/*
SELECT 
  d.driver_id,
  d.driver_number,
  d.name,
  d.phone_number,
  d.dl_number,
  d.dl_category
FROM drivers d
JOIN LATERAL unnest(m.driver_ids) WITH ORDINALITY AS driver_id(id, idx) ON d.driver_id = driver_id.id
WHERE m.memo_id = [memo_id]
ORDER BY driver_id.idx;
*/
