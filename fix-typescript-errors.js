const fs = require('fs');
const path = require('path');

// Function to recursively find all TypeScript files in a directory
function findTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !filePath.includes('node_modules') && !filePath.includes('.next')) {
      fileList = findTsFiles(filePath, fileList);
    } else if (
      (file.endsWith('.ts') || file.endsWith('.tsx')) && 
      !file.endsWith('.d.ts')
    ) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix catch blocks in a file
function fixCatchBlocks(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Replace catch blocks without type annotation
  const catchRegex = /catch\s*\(\s*([a-zA-Z0-9_]+)\s*\)\s*\{/g;
  const newContent = content.replace(catchRegex, (match, varName) => {
    modified = true;
    return `catch (${varName}: any) {`;
  });

  if (modified) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Fixed catch blocks in ${filePath}`);
  }
}

// Function to fix empty object initializations
function fixEmptyObjects(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Find patterns like "let results = {};" and replace with proper type annotation
  const emptyObjRegex = /(let|const|var)\s+([a-zA-Z0-9_]+)\s*=\s*\{\}\s*;/g;
  const newContent = content.replace(emptyObjRegex, (match, declType, varName) => {
    modified = true;
    return `${declType} ${varName}: Record<string, any> = {};`;
  });

  if (modified) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Fixed empty object initializations in ${filePath}`);
  }
}

// Function to fix array initializations
function fixArrayInitializations(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Find patterns like "let results = [];" and replace with proper type annotation
  const emptyArrayRegex = /(let|const|var)\s+([a-zA-Z0-9_]+)\s*=\s*\[\]\s*;/g;
  const newContent = content.replace(emptyArrayRegex, (match, declType, varName) => {
    modified = true;
    return `${declType} ${varName}: any[] = [];`;
  });

  if (modified) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Fixed array initializations in ${filePath}`);
  }
}

// Function to fix reduce initializations
function fixReduceInitializations(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Find patterns like ".reduce((map, item) => {" with empty object initialization
  const reduceRegex = /\.reduce\(\(([a-zA-Z0-9_]+),\s*([a-zA-Z0-9_]+)\)\s*=>\s*\{/g;
  const newContent = content.replace(reduceRegex, (match, accumName, itemName) => {
    modified = true;
    return `.reduce<Record<string, any>>((${accumName}, ${itemName}) => {`;
  });

  if (modified) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`Fixed reduce initializations in ${filePath}`);
  }
}

// Main function
function main() {
  const rootDir = process.cwd();
  const tsFiles = findTsFiles(rootDir);
  
  console.log(`Found ${tsFiles.length} TypeScript files`);
  
  tsFiles.forEach(file => {
    fixCatchBlocks(file);
    fixEmptyObjects(file);
    fixArrayInitializations(file);
    fixReduceInitializations(file);
  });
  
  console.log('Done fixing TypeScript errors');
}

main();
