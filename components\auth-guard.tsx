'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/contexts/supabase-provider'
import { Loader2 } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter()
  const { user, session } = useSupabase()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is authenticated
    const checkAuth = async () => {
      try {
        // Check if we're on a public path
        const path = window.location.pathname
        const isPublicPath = path === '/login' ||
                            path.startsWith('/api/auth/callback') ||
                            path.startsWith('/_next') ||
                            path === '/favicon.ico' ||
                            path === '/test-branch' ||
                            path === '/assign-branch' ||
                            path === '/check-branch'

        // If we're on a public path, don't check auth
        if (isPublicPath) {
          setIsLoading(false)
          return
        }

        // For all app paths, check if we have a session or localStorage flag
        const hasLocalAuth = localStorage.getItem('kpn_auth_active') === 'true'

        if (!user && !session && !hasLocalAuth) {
          // Only redirect if we have no session, no user, and no localStorage flag
          router.push('/login')
        } else {
          // Otherwise, proceed with the app
          setIsLoading(false)
        }
      } catch (error: any) {
        console.error('Error checking authentication:', error)
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [user, session, router])

  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return <>{children}</>
}
