import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// Default parcel types to seed the database
const defaultParcelTypes = [
  {
    type_name: "Document",
    base_price: 50,
    special_handling_instructions: "Handle with care. Keep dry.",
  },
  {
    type_name: "Package",
    base_price: 100,
    special_handling_instructions: "Standard handling.",
  },
  {
    type_name: "Electronics",
    base_price: 150,
    special_handling_instructions: "Handle with care. Avoid moisture.",
  },
  {
    type_name: "Fragile",
    base_price: 200,
    special_handling_instructions: "Extremely fragile. Handle with extra care.",
  },
  {
    type_name: "Perishable",
    base_price: 180,
    special_handling_instructions: "Keep refrigerated. Deliver quickly.",
  },
  {
    type_name: "Clothing",
    base_price: 120,
    special_handling_instructions: "Keep dry.",
  },
  {
    type_name: "Books",
    base_price: 100,
    special_handling_instructions: "Keep dry. Stack flat.",
  },
  {
    type_name: "Food",
    base_price: 150,
    special_handling_instructions: "Keep refrigerated. Handle with care.",
  },
  {
    type_name: "Medicine",
    base_price: 180,
    special_handling_instructions:
      "Keep at room temperature. Handle with care.",
  },
  {
    type_name: "Other",
    base_price: 100,
    special_handling_instructions: "Standard handling.",
  },
];

// POST /api/parceltypes/seed
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client for the route handler
    const routeHandlerClient = createRouteHandlerClient({ cookies });

    // Check if we already have parcel types
    const { data: existingTypes, error: checkError } = await supabase
      .from("parceltypes")
      .select("type_id")
      .limit(1);

    if (checkError) {
      console.error("Error checking existing parcel types:", checkError);
      return NextResponse.json({
        error: "Failed to check existing parcel types",
      }, { status: 500 });
    }

    // If we already have parcel types, don't seed again
    if (existingTypes && existingTypes.length > 0) {
      return NextResponse.json({
        message: "Parcel types already exist, skipping seed",
        count: existingTypes.length,
      });
    }

    // Insert default parcel types
    const { data, error } = await supabase
      .from("parceltypes")
      .insert(defaultParcelTypes)
      .select();

    if (error) {
      console.error("Error seeding parcel types:", error);
      return NextResponse.json({ error: "Failed to seed parcel types" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      message: "Successfully seeded parcel types",
      count: data ? data.length : 0,
      data,
    });
  } catch (error: any) {
    console.error("Error in POST /api/parceltypes/seed:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
