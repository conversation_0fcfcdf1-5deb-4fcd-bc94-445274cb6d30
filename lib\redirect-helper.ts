'use client';

/**
 * Force a redirection to the specified URL
 * This is a last resort when other redirection methods fail
 */
export function forceRedirect(url: string, delay: number = 0): void {
  console.log(`Force redirect to ${url} scheduled in ${delay}ms`);
  
  if (delay > 0) {
    setTimeout(() => {
      console.log(`Executing force redirect to ${url}`);
      window.location.href = url;
    }, delay);
  } else {
    console.log(`Executing immediate force redirect to ${url}`);
    window.location.href = url;
  }
}

/**
 * Check if the user is authenticated and redirect if needed
 */
export async function checkAuthAndRedirect(
  supabase: any,
  authenticatedRedirectTo: string = '/',
  unauthenticatedRedirectTo: string = '/login'
): Promise<void> {
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error checking authentication:', error);
      return;
    }
    
    const isAuthenticated = !!data.session;
    const currentPath = window.location.pathname;
    
    // If authenticated and on login page, redirect to home
    if (isAuthenticated && currentPath === '/login') {
      console.log('User is authenticated but on login page, redirecting to home');
      forceRedirect(authenticatedRedirectTo);
    }
    
    // If not authenticated and on protected page, redirect to login
    if (!isAuthenticated && currentPath !== '/login' && 
        !currentPath.startsWith('/forgot-password') && 
        !currentPath.startsWith('/reset-password')) {
      console.log('User is not authenticated and on protected page, redirecting to login');
      forceRedirect(unauthenticatedRedirectTo);
    }
  } catch (error: any) {
    console.error('Error in checkAuthAndRedirect:', error);
  }
}
