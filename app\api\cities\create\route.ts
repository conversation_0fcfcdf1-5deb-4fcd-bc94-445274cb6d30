import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// Initialize the Supabase client with fallback values
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL ||
  "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Cities to add
const initialCities = [
  { name: "Chennai", state: "Tamil Nadu", country: "India" },
  { name: "Coimbatore", state: "Tamil Nadu", country: "India" },
  { name: "Madurai", state: "Tamil Nadu", country: "India" },
  { name: "Salem", state: "Tamil Nadu", country: "India" },
  { name: "Trichy", state: "Tamil Nadu", country: "India" },
];

export async function GET() {
  try {
    // Create cities table directly
    const { data: createData, error: createError } = await supabase
      .from("cities")
      .insert([initialCities[0]])
      .select();

    if (createError) {
      if (createError.code === "42P01") {
        // Table doesn't exist, return instructions
        return NextResponse.json({
          error: "Cities table doesn't exist",
          instructions:
            "Please create the cities table in the Supabase dashboard using the SQL provided in the response",
          sql: `
            CREATE TABLE IF NOT EXISTS public.cities (
              city_id SERIAL PRIMARY KEY,
              name TEXT NOT NULL UNIQUE,
              state TEXT,
              country TEXT DEFAULT 'India',
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
            );

            -- Add city_id column to branches table
            ALTER TABLE public.branches ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES public.cities(city_id);

            -- Create index for faster lookups
            CREATE INDEX IF NOT EXISTS branches_city_id_idx ON public.branches (city_id);

            -- Insert initial cities
            INSERT INTO public.cities (name, state, country) VALUES
              ('Chennai', 'Tamil Nadu', 'India'),
              ('Coimbatore', 'Tamil Nadu', 'India'),
              ('Madurai', 'Tamil Nadu', 'India'),
              ('Salem', 'Tamil Nadu', 'India'),
              ('Trichy', 'Tamil Nadu', 'India')
            ON CONFLICT (name) DO NOTHING;

            -- Update existing branches with city_id based on address
            UPDATE public.branches
            SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Chennai')
            WHERE address LIKE '%Chennai%';

            UPDATE public.branches
            SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Coimbatore')
            WHERE address LIKE '%Coimbatore%';

            UPDATE public.branches
            SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Madurai')
            WHERE address LIKE '%Madurai%';

            UPDATE public.branches
            SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Salem')
            WHERE address LIKE '%Salem%';

            UPDATE public.branches
            SET city_id = (SELECT city_id FROM public.cities WHERE name = 'Trichy')
            WHERE address LIKE '%Trichy%';
          `,
        });
      }

      return NextResponse.json(
        { error: "Failed to create cities table", details: createError },
        { status: 500 },
      );
    }

    // If we got here, the table exists, so add the rest of the cities
    for (let i = 1; i < initialCities.length; i++) {
      const { error: insertError } = await supabase
        .from("cities")
        .insert([initialCities[i]]);

      if (insertError && insertError.code !== "23505") { // Ignore unique violation errors
        console.error(
          `Error inserting city ${initialCities[i].name}:`,
          insertError,
        );
      }
    }

    // Get all cities
    const { data: cities, error: fetchError } = await supabase
      .from("cities")
      .select("*");

    if (fetchError) {
      return NextResponse.json(
        { error: "Failed to fetch cities", details: fetchError },
        { status: 500 },
      );
    }

    // Update branches with city_id
    for (const city of cities || []) {
      const { error: updateError } = await supabase.rpc("exec_sql", {
        sql: `
          UPDATE public.branches
          SET city_id = ${city.city_id}
          WHERE address LIKE '%${city.name}%' AND (city_id IS NULL OR city_id = 0);
        `,
      });

      if (updateError && updateError.code !== "PGRST202") {
        console.error(
          `Error updating branches for city ${city.name}:`,
          updateError,
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: "Cities table created and populated successfully",
      cities,
    });
  } catch (error: any) {
    console.error("Error in create cities API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
