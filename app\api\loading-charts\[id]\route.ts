import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/loading-charts/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // Get loading chart by ID
    const { data: chart, error } = await supabase
      .from("loading_charts")
      .select(`
        *,
        memo:memos(memo_number),
        vehicle:vehicles(registration_number, vehicle_type),
        destination_branch:branches(name, code)
      `)
      .eq("chart_id", id)
      .single();

    if (error) {
      console.error("Error fetching loading chart:", error);
      return NextResponse.json({ error: "Failed to fetch loading chart" }, {
        status: 500,
      });
    }

    if (!chart) {
      return NextResponse.json({ error: "Loading chart not found" }, { status: 404 });
    }

    // Get loading chart items
    const { data: items, error: itemsError } = await supabase
      .from("loading_chart_items")
      .select("*")
      .eq("chart_id", id)
      .order("created_at", { ascending: true });

    if (itemsError) {
      console.error("Error fetching loading chart items:", itemsError);
      return NextResponse.json({ error: "Failed to fetch loading chart items" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      chart,
      items: items || [],
    });
  } catch (error: any) {
    console.error(`Error in GET /api/loading-charts/${params.id}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}

// PATCH /api/loading-charts/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const id = params.id;
    const body = await request.json();

    // Update loading chart
    const { data: chart, error } = await supabase
      .from("loading_charts")
      .update({
        status: body.status || undefined,
        notes: body.notes || undefined,
      })
      .eq("chart_id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating loading chart:", error);
      return NextResponse.json({ error: "Failed to update loading chart" }, {
        status: 500,
      });
    }

    return NextResponse.json({
      message: "Loading chart updated successfully",
      chart,
    });
  } catch (error: any) {
    console.error(`Error in PATCH /api/loading-charts/${params.id}:`, error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
