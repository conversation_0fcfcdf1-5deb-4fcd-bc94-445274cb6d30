"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Loader2, CheckCircle2, Download, Home } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [bookingDetails, setBookingDetails] = useState<any>(null)
  const [hasDownloaded, setHasDownloaded] = useState(false)

  useEffect(() => {
    // Get booking ID from URL parameters
    const bookingId = searchParams.get("bookingId") || "UNKNOWN"
    
    // Simulate API call to fetch booking details
    setTimeout(() => {
      // Mock booking details - in a real app, this would come from your API
      setBookingDetails({
        id: bookingId,
        date: new Date().toLocaleDateString(),
        amount: "4,500.00",
        customerName: "John Doe",
        customerEmail: "<EMAIL>",
        bookingType: "Standard Booking"
      })
      setLoading(false)
      
      // Show success toast
      toast({
        title: "Payment Successful!",
        description: "Your payment has been processed successfully.",
      })
    }, 1500)
  }, [searchParams, toast])

  const handleDownloadInvoice = () => {
    // In a real app, this would generate and download a PDF invoice
    toast({
      title: "Invoice Downloaded",
      description: "Your invoice has been downloaded successfully.",
    })
    setHasDownloaded(true)
  }

  const handleBackToHome = () => {
    if (!hasDownloaded) {
      // Show confirmation dialog
      if (confirm("You haven't downloaded your invoice yet. Would you like to download it before going back?")) {
        handleDownloadInvoice()
      }
    }
    router.push("/")
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-primary to-accent flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-lg">
        {loading ? (
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Loader2 className="h-16 w-16 animate-spin text-primary mb-4" />
            <h2 className="text-xl font-semibold">Processing Payment</h2>
            <p className="text-muted-foreground mt-2 text-center">
              Please wait while we confirm your payment...
            </p>
          </CardContent>
        ) : (
          <>
            <CardHeader className="text-center pb-2">
              <div className="flex justify-center mb-4">
                <CheckCircle2 className="h-16 w-16 text-green-500" />
              </div>
              <CardTitle className="text-2xl">Payment Successful!</CardTitle>
              <CardDescription>
                Your booking has been confirmed
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-semibold text-lg mb-2">Booking Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Booking ID:</span>
                    <span className="font-medium">{bookingDetails?.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Date:</span>
                    <span className="font-medium">{bookingDetails?.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Amount:</span>
                    <span className="font-medium">₹{bookingDetails?.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Customer:</span>
                    <span className="font-medium">{bookingDetails?.customerName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="font-medium">{bookingDetails?.customerEmail}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Type:</span>
                    <span className="font-medium">{bookingDetails?.bookingType}</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <p className="text-green-800 text-sm">
                  A confirmation email has been sent to {bookingDetails?.customerEmail} with all the details.
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row gap-3">
              <Button 
                variant="outline" 
                onClick={handleBackToHome}
                className="w-full sm:w-auto"
              >
                <Home className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
              <Button 
                onClick={handleDownloadInvoice}
                className="w-full sm:w-auto bg-primary hover:bg-primary/90"
              >
                <Download className="mr-2 h-4 w-4" />
                {hasDownloaded ? "Download Again" : "Download Invoice"}
              </Button>
            </CardFooter>
          </>
        )}
      </Card>
    </div>
  )
}
