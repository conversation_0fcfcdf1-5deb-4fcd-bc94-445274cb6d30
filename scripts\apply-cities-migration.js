// Script to apply the cities migration and update branches
const { createClient } = require("@supabase/supabase-js");
const fs = require('fs');
const path = require('path');

// Initialize the Supabase client
const supabaseUrl = "https://nekjeqxlwhfwyekeinnc.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5la2plcXhsd2hmd3lla2Vpbm5jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MTU2NDcsImV4cCI6MjA2MTM5MTY0N30.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y";
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function applyCitiesMigration() {
  try {
    console.log("Applying cities migration...");

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'supabase', 'migrations', '20240701000000_create_cities_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error("Error applying migration:", error);
      return;
    }

    console.log("Migration applied successfully");

    // List cities
    const { data: cities, error: citiesError } = await supabase
      .from("cities")
      .select("*");

    if (citiesError) {
      console.error("Error fetching cities:", citiesError);
      return;
    }

    console.log("Cities:", cities);

    // List branches with city_id
    const { data: branches, error: branchesError } = await supabase
      .from("branches")
      .select("branch_id, name, code, address, city_id");

    if (branchesError) {
      console.error("Error fetching branches:", branchesError);
      return;
    }

    console.log("Branches with city_id:", branches);

    // For branches without city_id, try to update them
    const branchesWithoutCity = branches.filter(branch => !branch.city_id);
    
    if (branchesWithoutCity.length > 0) {
      console.log(`Found ${branchesWithoutCity.length} branches without city_id. Attempting to update...`);
      
      for (const branch of branchesWithoutCity) {
        // Try to extract city from address
        let cityName = null;
        
        for (const city of cities) {
          if (branch.address && branch.address.includes(city.name)) {
            cityName = city.name;
            break;
          }
        }
        
        if (cityName) {
          const city = cities.find(c => c.name === cityName);
          
          if (city) {
            console.log(`Updating branch ${branch.name} with city ${cityName}`);
            
            const { data: updateData, error: updateError } = await supabase
              .from("branches")
              .update({ city_id: city.city_id })
              .eq("branch_id", branch.branch_id)
              .select();
              
            if (updateError) {
              console.error(`Error updating branch ${branch.name}:`, updateError);
            } else {
              console.log(`Branch ${branch.name} updated successfully:`, updateData);
            }
          }
        } else {
          console.log(`Could not determine city for branch ${branch.name}`);
        }
      }
    }
  } catch (error) {
    console.error("Unexpected error:", error);
  }
}

async function main() {
  console.log("Starting script...");

  try {
    await applyCitiesMigration();
    console.log("Script completed successfully!");
  } catch (error) {
    console.error("Error in main function:", error);
  }
}

main();
