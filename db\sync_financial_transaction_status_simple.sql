-- SQL script to sync approval_status to status in financial_transactions table (simplified version)

-- Create a function to sync approval_status to status
CREATE OR REPLACE FUNCTION sync_approval_status_to_status()
RETURNS TRIGGER AS $func$
BEGIN
  NEW.status = NEW.approval_status;
  RETURN NEW;
END;
$func$ LANGUAGE plpgsql;

-- Create a trigger to automatically sync the columns
DROP TRIGGER IF EXISTS sync_status_trigger ON financial_transactions;
CREATE TRIGGER sync_status_trigger
BEFORE INSERT OR UPDATE ON financial_transactions
FOR EACH ROW
EXECUTE FUNCTION sync_approval_status_to_status();

-- Update existing records to sync the columns
UPDATE financial_transactions
SET status = approval_status
WHERE status IS NULL OR status != approval_status;
