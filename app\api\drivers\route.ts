import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/drivers
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get("status");

    // Build query
    let query = supabase
      .from("drivers")
      .select("*");

    // Add filters if provided
    if (status) {
      query = query.eq("status", status);
    }

    // Execute query
    const { data: drivers, error } = await query.order("name");

    if (error) {
      console.error("Error fetching drivers:", error);
      return NextResponse.json({ error: "Failed to fetch drivers" }, {
        status: 500,
      });
    }

    return NextResponse.json({ drivers });
  } catch (error: any) {
    console.error("Error in GET /api/drivers:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/drivers
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.contact_number) {
      return NextResponse.json(
        { error: "Missing required fields: name, contact_number" },
        { status: 400 },
      );
    }

    // Create driver
    const { data: driver, error } = await supabase
      .from("drivers")
      .insert({
        name: body.name,
        contact_number: body.contact_number,
        status: body.status || "Active",
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating driver:", error);
      return NextResponse.json({ error: "Failed to create driver" }, {
        status: 500,
      });
    }

    return NextResponse.json({ driver });
  } catch (error: any) {
    console.error("Error in POST /api/drivers:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
