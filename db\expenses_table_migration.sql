-- Create expenses table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.expenses (
  id                SERIAL    PRIMARY KEY,
  branch_id         INT       NOT NULL REFERENCES branches(branch_id),
  financial_tx_id   INT       REFERENCES financialtransactions(transaction_id),
  category          VARCHAR(100)   NOT NULL,
  vendor_name       VARCHAR(255),
  invoice_number    VARCHAR(100),
  description       TEXT,
  receipt_url       VARCHAR(255),
  amount            NUMERIC(10,2)  NOT NULL,
  payment_method    TEXT,
  approval_status   TEXT      DEFAULT 'Pending',  -- 'Pending' | 'Approved' | 'Rejected'
  submitted_by      INT       REFERENCES users(user_id),
  submitted_at      TIMESTAMPTZ   DEFAULT now(),
  approved_at       TIMESTAMPTZ,
  approved_by       INT       REFERENCES users(user_id),
  rejected_at       TIMESTAMPTZ,
  rejected_by       INT       REFERENCES users(user_id),
  rejection_reason  TEXT,
  memo_number       VARCHAR(50)
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS expenses_branch_id_idx ON expenses(branch_id);
CREATE INDEX IF NOT EXISTS expenses_submitted_by_idx ON expenses(submitted_by);
CREATE INDEX IF NOT EXISTS expenses_approval_status_idx ON expenses(approval_status);
CREATE INDEX IF NOT EXISTS expenses_memo_number_idx ON expenses(memo_number);
