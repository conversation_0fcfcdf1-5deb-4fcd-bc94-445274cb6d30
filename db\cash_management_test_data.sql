-- Cash Management Test Data
-- This script populates test data for the cash management system

-- Create some financial transactions for a branch
INSERT INTO financial_transactions (
  branch_id,
  transaction_type,
  amount,
  payment_method,
  transaction_date,
  reference_number,
  description,
  status
) VALUES
-- Collection transactions for today
(1, 'Collection', 5000.00, 'Cash', NOW(), 'LR-12345', 'Payment for booking LR-12345', 'Approved'),
(1, 'Collection', 3500.00, 'UPI', NOW(), 'LR-12346', 'Payment for booking LR-12346', 'Approved'),
(1, 'Collection', 2800.00, 'Cash', NOW(), 'LR-12347', 'COD collection for LR-12347', 'Approved'),
(1, 'Collection', 4200.00, 'Card', NOW(), 'LR-12348', 'Payment for booking LR-12348', 'Approved'),

-- Expense transactions for today
(1, 'Expense', 1200.00, 'Cash', NOW(), 'EXP-001', 'Fuel: Diesel for delivery vehicles', 'Approved'),
(1, 'Expense', 800.00, 'Cash', NOW(), 'EXP-002', 'Loading: Labor charges', 'Approved'),
(1, 'Expense', 500.00, 'Cash', NOW(), 'EXP-003', 'Unloading: Labor charges', 'Approved'),
(1, 'Expense', 1500.00, 'Cash', NOW(), 'EXP-004', 'Maintenance: Office equipment repair', 'Approved'),

-- Pending expense transactions
(1, 'Expense', 2000.00, 'Cash', NOW(), 'EXP-005', 'Maintenance: Vehicle repair', 'Pending'),
(1, 'Expense', 1000.00, 'Cash', NOW(), 'EXP-006', 'Utilities: Electricity bill', 'Pending'),

-- Collection transactions for yesterday
(1, 'Collection', 4500.00, 'Cash', NOW() - INTERVAL '1 day', 'LR-12340', 'Payment for booking LR-12340', 'Approved'),
(1, 'Collection', 3000.00, 'UPI', NOW() - INTERVAL '1 day', 'LR-12341', 'Payment for booking LR-12341', 'Approved'),
(1, 'Collection', 2500.00, 'Cash', NOW() - INTERVAL '1 day', 'LR-12342', 'COD collection for LR-12342', 'Approved'),

-- Expense transactions for yesterday
(1, 'Expense', 1000.00, 'Cash', NOW() - INTERVAL '1 day', 'EXP-101', 'Fuel: Diesel for delivery vehicles', 'Approved'),
(1, 'Expense', 700.00, 'Cash', NOW() - INTERVAL '1 day', 'EXP-102', 'Loading: Labor charges', 'Approved'),
(1, 'Expense', 400.00, 'Cash', NOW() - INTERVAL '1 day', 'EXP-103', 'Unloading: Labor charges', 'Approved'),

-- Collection transactions for 2 days ago
(1, 'Collection', 5200.00, 'Cash', NOW() - INTERVAL '2 days', 'LR-12330', 'Payment for booking LR-12330', 'Approved'),
(1, 'Collection', 3800.00, 'UPI', NOW() - INTERVAL '2 days', 'LR-12331', 'Payment for booking LR-12331', 'Approved'),

-- Expense transactions for 2 days ago
(1, 'Expense', 1100.00, 'Cash', NOW() - INTERVAL '2 days', 'EXP-201', 'Fuel: Diesel for delivery vehicles', 'Approved'),
(1, 'Expense', 600.00, 'Cash', NOW() - INTERVAL '2 days', 'EXP-202', 'Maintenance: Office cleaning', 'Approved');

-- Create some remittances
INSERT INTO remittance (
  branch_id,
  total_collections,
  approved_expenses,
  float_balance,
  remittable_amount,
  method,
  reference_id,
  status,
  submitted_by,
  created_at
) VALUES
(1, 10000.00, 4000.00, 1000.00, 5000.00, 'bank_transfer', 'NEFT123456', 'Approved', 1, NOW() - INTERVAL '1 day'),
(1, 9000.00, 3000.00, 1000.00, 5000.00, 'bank_transfer', 'NEFT789012', 'Approved', 1, NOW() - INTERVAL '2 days'),
(1, 12000.00, 4500.00, 1000.00, 6500.00, 'upi', 'UPI123456789', 'Submitted', 1, NOW());

-- Create ledger entries
INSERT INTO accountsledger (
  branch_id,
  ledger_date,
  opening_balance,
  total_collection,
  total_expense,
  remitted,
  closing_balance
) VALUES
(1, CURRENT_DATE - INTERVAL '2 days', 5000.00, 9000.00, 1700.00, 5000.00, 7300.00),
(1, CURRENT_DATE - INTERVAL '1 day', 7300.00, 10000.00, 2100.00, 5000.00, 10200.00);

-- Create some expenses
INSERT INTO expenses (
  branch_id,
  category,
  vendor_name,
  invoice_number,
  description,
  amount,
  payment_method,
  approval_status,
  submitted_by,
  submitted_at
) VALUES
(1, 'fuel', 'Indian Oil', 'INV-F001', 'Diesel for delivery vehicles', 1200.00, 'Cash', 'Approved', 1, NOW()),
(1, 'maintenance', 'City Repairs', 'INV-M001', 'Office equipment repair', 1500.00, 'Cash', 'Approved', 1, NOW()),
(1, 'maintenance', 'Auto Garage', 'INV-M002', 'Vehicle repair', 2000.00, 'Cash', 'Pending', 1, NOW()),
(1, 'utilities', 'Power Corp', 'INV-U001', 'Electricity bill', 1000.00, 'Cash', 'Pending', 1, NOW());
