@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Primary Colors */
    --deep-blue: 212 100% 27%; /* #003A8C */
    --bright-yellow: 50 100% 50%; /* #FFD100 */
    --bright-red: 0 75% 52%; /* #E72222 */

    /* Supporting Colors */
    --sky-blue: 215 100% 38%; /* #0054C1 */
    --white: 0 0% 100%; /* #FFFFFF */
    --light-gray: 220 20% 97%; /* #F2F4F7 */
    --dark-gray: 0 0% 20%; /* #333333 */
    --medium-gray: 0 0% 50%; /* #808080 */

    /* System Variables */
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 20%; /* Dark Gray */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 20%; /* Dark Gray */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 20%; /* Dark Gray */
    --primary: 212 100% 27%; /* Deep Blue */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 50 100% 50%; /* Bright Yellow */
    --secondary-foreground: 0 0% 20%; /* Dark Gray */
    --muted: 220 20% 97%; /* Light Gray */
    --muted-foreground: 0 0% 50%; /* Medium Gray */
    --accent: 215 100% 38%; /* Sky Blue */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 75% 52%; /* Bright Red */
    --destructive-foreground: 0 0% 100%; /* White */
    --border: 220 13% 91%; /* Lighter Gray for borders */
    --input: 220 13% 91%; /* Lighter Gray for inputs */
    --ring: 212 100% 27%; /* Deep Blue */

    /* Custom Variables for Diagonal Elements */
    --diagonal-blue: 212 100% 27%; /* Deep Blue */
    --diagonal-yellow: 50 100% 50%; /* Bright Yellow */

    /* Chart Colors */
    --chart-1: 212 100% 27%; /* Deep Blue */
    --chart-2: 215 100% 38%; /* Sky Blue */
    --chart-3: 50 100% 50%; /* Bright Yellow */
    --chart-4: 0 75% 52%; /* Bright Red */
    --chart-5: 220 20% 97%; /* Light Gray */

    --radius: 0.5rem;
  }

  /* We're using a white theme only, so dark mode is the same as light mode */
  .dark {
    --background: 0 0% 100%; /* White */
    --foreground: 0 0% 20%; /* Dark Gray */
    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 20%; /* Dark Gray */
    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 20%; /* Dark Gray */
    --primary: 212 100% 27%; /* Deep Blue */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 50 100% 50%; /* Bright Yellow */
    --secondary-foreground: 0 0% 20%; /* Dark Gray */
    --muted: 220 20% 97%; /* Light Gray */
    --muted-foreground: 0 0% 50%; /* Medium Gray */
    --accent: 215 100% 38%; /* Sky Blue */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 75% 52%; /* Bright Red */
    --destructive-foreground: 0 0% 100%; /* White */
    --border: 220 13% 91%; /* Lighter Gray for borders */
    --input: 220 13% 91%; /* Lighter Gray for inputs */
    --ring: 212 100% 27%; /* Deep Blue */

    /* Chart Colors */
    --chart-1: 212 100% 27%; /* Deep Blue */
    --chart-2: 215 100% 38%; /* Sky Blue */
    --chart-3: 50 100% 50%; /* Bright Yellow */
    --chart-4: 0 75% 52%; /* Bright Red */
    --chart-5: 220 20% 97%; /* Light Gray */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.container {
  @apply mx-auto max-w-7xl;
}