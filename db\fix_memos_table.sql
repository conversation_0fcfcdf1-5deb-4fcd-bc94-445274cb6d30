-- Add missing columns to memos table
DO $$
BEGIN
  -- Add driver_expenses_json column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'driver_expenses_json'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN driver_expenses_json JSONB;
  END IF;

  -- Add received_at column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'received_at'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN received_at TIMESTAMP WITH TIME ZONE;
  END IF;

  -- Add received_by column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'received_by'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN received_by UUID REFERENCES auth.users(id);
  END IF;

  -- Add fuel_consumed column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'fuel_consumed'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN fuel_consumed NUMERIC(10, 2);
  END IF;

  -- Add bata_amount column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'bata_amount'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN bata_amount NUMERIC(10, 2);
  END IF;

  -- Add salary_amount column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'salary_amount'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN salary_amount NUMERIC(10, 2);
  END IF;

  -- Add total_expense column if it doesn't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'memos' AND column_name = 'total_expense'
  ) THEN
    ALTER TABLE public.memos ADD COLUMN total_expense NUMERIC(10, 2);
  END IF;
END
$$;