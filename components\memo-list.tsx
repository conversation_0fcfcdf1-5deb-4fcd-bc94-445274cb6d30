"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { ArrowDownToLine, Filter, Search, Loader2, AlertCircle, MessageSquare } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { format } from "date-fns"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { getUserBranchIdAsync } from "@/lib/branch-utils"

export function MemoList() {
  const { toast } = useToast()
  const [memos, setMemos] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedMemo, setSelectedMemo] = useState<any>(null)
  const [isReceiveDialogOpen, setIsReceiveDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [fuelLitres, setFuelLitres] = useState("")
  const [bataAmount, setBataAmount] = useState("")
  const [salaryAmount, setSalaryAmount] = useState("")
  const [bataAmount2, setBataAmount2] = useState("")
  const [salaryAmount2, setSalaryAmount2] = useState("")
  const [splitExpenses, setSplitExpenses] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [error, setError] = useState<string | null>(null)

  const [userBranchId, setUserBranchId] = useState<string | null>(null)

  // Fetch user's branch ID and memos on component mount
  useEffect(() => {
    const getUserBranch = async () => {
      try {
        // Get branch ID from API
        const branchId = await getUserBranchIdAsync();

        if (branchId !== null) {
          // Set the branch ID from API
          setUserBranchId(branchId.toString());

          // Fetch memos with the branch ID
          fetchMemos(branchId.toString());
        } else {
          console.error('Failed to get branch ID');
          // Show a toast notification to inform the user
          toast({
            title: "Authentication Issue",
            description: "Could not determine your branch. Please try logging in again.",
            variant: "destructive",
          });
          setError("Authentication issue. Please try logging in again.");
        }
      } catch (error: any) {
        console.error('Error in getUserBranch:', error);
        setError("Failed to get branch information. Please try refreshing the page.");
      }
    };

    getUserBranch();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps



  // Fetch memos from API
  const fetchMemos = async (branchId?: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // Build URL with user's branch ID
      const url = new URL('/api/memos', window.location.origin)
      if (branchId) {
        // Use branch_id parameter instead of user_branch_id
        url.searchParams.append('branch_id', branchId)
      }

      // Make sure we're using the correct origin
      console.log('Using origin:', window.location.origin)

      console.log('Fetching memos with URL:', url.toString())
      const response = await fetch(url.toString())
      const data = await response.json()

      if (response.ok) {
        setMemos(data.memos || [])
      } else {
        setError(data.error || 'Failed to fetch memos')
        toast({
          title: "Error",
          description: data.error || "Failed to fetch memos",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error fetching memos:', error)
      setError('Failed to fetch memos')
      toast({
        title: "Error",
        description: "Failed to fetch memos. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch driver numbers for a memo
  const fetchDriverNumbers = async (memo: any) => {
    if (!memo.driver_ids || !Array.isArray(memo.driver_ids) || memo.driver_ids.length === 0) {
      setSelectedMemo(memo)
      return
    }

    try {
      const response = await fetch(`${window.location.origin}/api/memos/drivers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          driver_ids: memo.driver_ids
        }),
      })

      const data = await response.json()

      if (response.ok && data.driver_numbers) {
        setSelectedMemo({
          ...memo,
          driver_numbers: data.driver_numbers
        })
      } else {
        console.error('Error fetching driver numbers:', data.error)
        setSelectedMemo(memo)
      }
    } catch (error: any) {
      console.error('Error fetching driver numbers:', error)
      setSelectedMemo(memo)
    }
  }

  // Filter memos based on status and search query
  const filteredMemos = memos.filter(memo => {
    // Filter by status
    if (statusFilter !== "all" && memo.status !== statusFilter) {
      return false
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        memo.memo_number.toLowerCase().includes(query) ||
        memo.vehicle?.registration_number?.toLowerCase().includes(query) ||
        memo.from_branch?.name?.toLowerCase().includes(query) ||
        memo.to_branch?.name?.toLowerCase().includes(query)
      )
    }

    return true
  })

  // Handle receiving a memo
  const handleReceive = async () => {
    if (!selectedMemo || !fuelLitres) {
      toast({
        title: "Missing Information",
        description: "Please enter fuel consumption details",
        variant: "destructive",
      })
      return
    }

    // Check if at least one driver has BATA amount
    if (!bataAmount) {
      toast({
        title: "Missing Information",
        description: "Please enter BATA amount for at least one driver",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Get current user ID
      const userResponse = await fetch(`${window.location.origin}/api/auth/session`)
      const userData = await userResponse.json()
      const userId = userData?.user?.id

      // Calculate total expenses
      const bataValue1 = bataAmount ? parseFloat(bataAmount) : 0
      const salaryValue1 = salaryAmount ? parseFloat(salaryAmount) : 0
      const bataValue2 = bataAmount2 ? parseFloat(bataAmount2) : 0
      const salaryValue2 = salaryAmount2 ? parseFloat(salaryAmount2) : 0

      const totalBata = bataValue1 + bataValue2
      const totalSalary = salaryValue1 + salaryValue2
      const totalExpense = totalBata + totalSalary

      // Prepare driver expense details
      const driverExpenses = []

      if (splitExpenses) {
        // Split expenses mode - each driver gets their own expenses

        // First driver
        if (selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && selectedMemo.driver_ids.length > 0) {
          driverExpenses.push({
            driver_id: selectedMemo.driver_ids[0],
            driver_number: selectedMemo.driver_numbers?.[0] || null,
            bata_amount: bataValue1,
            salary_amount: salaryValue1
          })
        }

        // Second driver (if applicable)
        if (selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && selectedMemo.driver_ids.length > 1 && (bataValue2 > 0 || salaryValue2 > 0)) {
          driverExpenses.push({
            driver_id: selectedMemo.driver_ids[1],
            driver_number: selectedMemo.driver_numbers?.[1] || null,
            bata_amount: bataValue2,
            salary_amount: salaryValue2
          })
        }
      } else {
        // Combined expenses mode - all expenses assigned to all drivers
        if (selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids)) {
          // Calculate total expenses
          const totalBataPerDriver = totalBata / selectedMemo.driver_ids.length;
          const totalSalaryPerDriver = totalSalary / selectedMemo.driver_ids.length;

          // Add all drivers with equal split of expenses
          selectedMemo.driver_ids.forEach((driverId: any, index: number) => {
            driverExpenses.push({
              driver_id: driverId,
              driver_number: selectedMemo.driver_numbers?.[index] || null,
              bata_amount: totalBataPerDriver,
              salary_amount: totalSalaryPerDriver
            });
          });
        }
      }

      const response = await fetch(`${window.location.origin}/api/memos/${selectedMemo.memo_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'Received',
          fuel_consumed: parseFloat(fuelLitres),
          received_by: userId,
          bata_amount: totalBata,
          salary_amount: totalSalary,
          total_expense: totalExpense,
          driver_expenses: driverExpenses // Store detailed breakdown by driver
        }),
      })

      const data = await response.json()

      if (response.ok) {
        // Update the memo in the local state
        setMemos(prevMemos =>
          prevMemos.map(memo =>
            memo.memo_id === selectedMemo.memo_id
              ? { ...memo, ...data.memo }
              : memo
          )
        )

        toast({
          title: "Memo Received",
          description: `Successfully received memo ${selectedMemo.memo_number} with fuel consumption of ${fuelLitres}L and total expense of ₹${totalExpense}.`,
        })

        setIsReceiveDialogOpen(false)
        setSelectedMemo(null)
        setFuelLitres("")
        setBataAmount("")
        setSalaryAmount("")
        setBataAmount2("")
        setSalaryAmount2("")
        setSplitExpenses(false)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to receive memo",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error receiving memo:', error)
      toast({
        title: "Error",
        description: "Failed to receive memo. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }



  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search memos..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Created">Created</SelectItem>
              <SelectItem value="Received">Received</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => fetchMemos(userBranchId || undefined)}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className="flex items-center justify-center h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading memos...</span>
        </div>
      ) : filteredMemos.length === 0 ? (
        <Card className="p-6">
          <div className="flex flex-col items-center justify-center h-[200px]">
            <p className="text-muted-foreground">No memos found</p>
            {searchQuery && (
              <Button
                variant="link"
                onClick={() => setSearchQuery("")}
                className="mt-2"
              >
                Clear search
              </Button>
            )}
          </div>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredMemos.map((memo) => (
            <Card key={memo.memo_id} className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="space-y-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <h3 className="font-medium">{memo.memo_number}</h3>
                    <Badge variant={
                      memo.status === 'Completed' ? 'default' :
                      memo.status === 'Received' ? 'secondary' :
                      memo.status === 'Created' ? 'destructive' : 'default'
                    }>
                      {memo.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Vehicle: {memo.vehicle?.registration_number || 'N/A'} •
                      Drivers: {memo.driver_ids && Array.isArray(memo.driver_ids)
                        ? memo.driver_ids.length
                        : 0}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    From: {memo.from_branch?.name || 'N/A'} ({memo.from_branch?.code || 'N/A'}) →
                    To: {memo.to_branch?.name || 'N/A'} ({memo.to_branch?.code || 'N/A'})
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Created: {memo.created_at ? format(new Date(memo.created_at), 'dd MMM yyyy, HH:mm') : 'N/A'}
                    {memo.received_at && ` • Received: ${format(new Date(memo.received_at), 'dd MMM yyyy, HH:mm')}`}
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      fetchDriverNumbers(memo)
                      setIsViewDialogOpen(true)
                    }}
                  >
                    View Details
                  </Button>

                  {memo.status === 'Created' && (
                    <>
                      {/* Load button removed as requested */}
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => {
                          fetchDriverNumbers(memo)
                          setIsReceiveDialogOpen(true)
                          setFuelLitres("")
                        }}
                      >
                        <ArrowDownToLine className="mr-2 h-4 w-4" />
                        Receive
                      </Button>
                    </>
                  )}



                  {memo.status === 'Completed' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Format the WhatsApp message
                        const driverCount = memo.driver_ids && Array.isArray(memo.driver_ids)
                          ? memo.driver_ids.length
                          : 1;

                        let message = `Memo ${memo.memo_number} has been completed.\n` +
                          `From: ${memo.from_branch?.name || 'N/A'} → To: ${memo.to_branch?.name || 'N/A'}\n` +
                          `Vehicle: ${memo.vehicle?.registration_number || 'N/A'}\n` +
                          `Drivers: ${driverCount}\n` +
                          `Fuel Consumed: ${memo.fuel_consumed || 0} L\n` +
                          `BATA Amount: ₹${memo.bata_amount || 0}\n` +
                          `Salary Amount: ₹${memo.salary_amount || 0}\n` +
                          `Total Expense: ₹${memo.total_expense || 0}`;

                        // Add driver expense details if available
                        if (memo.driver_expenses && Array.isArray(memo.driver_expenses) && memo.driver_expenses.length > 0) {
                          message += "\n\nDriver Details:";
                          memo.driver_expenses.forEach((driver: any, index: number) => {
                            const driverNumber = driver.driver_number || memo.driver_numbers?.[index];
                            message += `\nDriver ${index + 1}${driverNumber ? ` (ID: ${driverNumber})` : ` (ID: ${driver.driver_id})`}:\n` +
                              `  BATA: ₹${driver.bata_amount || 0}\n` +
                              `  Salary: ₹${driver.salary_amount || 0}`;
                          });
                        }

                        // Encode the message for WhatsApp
                        const encodedMessage = encodeURIComponent(message);

                        // Open WhatsApp with the pre-filled message
                        window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                      }}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Send WhatsApp
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* View Memo Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Memo Details</DialogTitle>
            <DialogDescription>
              Complete information about the selected memo
            </DialogDescription>
          </DialogHeader>
          {selectedMemo && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p className="text-sm font-medium">Memo Number</p>
                  <p className="text-sm">{selectedMemo.memo_number}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Status</p>
                  <Badge variant={
                    selectedMemo.status === 'Completed' ? 'default' :
                    selectedMemo.status === 'Received' ? 'secondary' :
                    selectedMemo.status === 'Created' ? 'destructive' : 'default'
                  }>
                    {selectedMemo.status}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium">Vehicle</p>
                  <p className="text-sm">{selectedMemo.vehicle?.registration_number || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Vehicle Type</p>
                  <p className="text-sm">{selectedMemo.vehicle?.vehicle_type || 'N/A'}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-sm font-medium">Drivers</p>
                  <p className="text-sm">
                    {selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids)
                      ? selectedMemo.driver_ids.map((id: any, index: number) => {
                          const driverNumber = selectedMemo.driver_numbers?.[index];
                          return `Driver ${index + 1}: ${driverNumber ? `ID ${driverNumber}` : `ID ${id}`}`;
                        }).join(', ')
                      : 'N/A'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">From Branch</p>
                  <p className="text-sm">{selectedMemo.from_branch?.name || 'N/A'} ({selectedMemo.from_branch?.code || 'N/A'})</p>
                </div>
                <div>
                  <p className="text-sm font-medium">To Branch</p>
                  <p className="text-sm">{selectedMemo.to_branch?.name || 'N/A'} ({selectedMemo.to_branch?.code || 'N/A'})</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Created At</p>
                  <p className="text-sm">{selectedMemo.created_at ? format(new Date(selectedMemo.created_at), 'dd MMM yyyy, HH:mm') : 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Created By</p>
                  <p className="text-sm">{selectedMemo.creator?.name || 'N/A'}</p>
                </div>

                {selectedMemo.status === 'Received' || selectedMemo.status === 'Completed' ? (
                  <>
                    <div>
                      <p className="text-sm font-medium">Received At</p>
                      <p className="text-sm">{selectedMemo.received_at ? format(new Date(selectedMemo.received_at), 'dd MMM yyyy, HH:mm') : 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Fuel Consumed</p>
                      <p className="text-sm">{selectedMemo.fuel_consumed || 0} L</p>
                    </div>

                    {/* Show BATA and Salary for Received status too */}
                    <div>
                      <p className="text-sm font-medium">BATA Amount</p>
                      <p className="text-sm">₹{selectedMemo.bata_amount || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Salary Amount</p>
                      <p className="text-sm">₹{selectedMemo.salary_amount || 0}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Total Expense</p>
                      <p className="text-sm">₹{selectedMemo.total_expense || (
                        parseFloat(selectedMemo.bata_amount || "0") +
                        parseFloat(selectedMemo.salary_amount || "0")
                      ).toFixed(2)}</p>
                    </div>

                    {/* Show driver expense details if available */}
                    {selectedMemo.driver_expenses && Array.isArray(selectedMemo.driver_expenses) && selectedMemo.driver_expenses.length > 0 && (
                      <div className="col-span-2 mt-2">
                        <p className="text-sm font-medium mb-1">Driver Expense Details</p>
                        <div className="space-y-2 bg-muted/30 p-2 rounded-md">
                          {selectedMemo.driver_expenses.map((driver: any, index: number) => {
                            const driverNumber = driver.driver_number || selectedMemo.driver_numbers?.[index];
                            return (
                              <div key={index} className="text-sm">
                                <p className="font-medium">Driver {index + 1}{driverNumber ? ` (ID: ${driverNumber})` : ` (ID: ${driver.driver_id})`}</p>
                                <p>BATA: ₹{driver.bata_amount || 0}</p>
                                <p>Salary: ₹{driver.salary_amount || 0}</p>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </>
                ) : null}

                {selectedMemo.status === 'Completed' && (
                  <div className="col-span-2 mt-2">
                    <p className="text-sm font-medium">Expense Status</p>
                    <Badge variant="default" className="bg-green-500">Approved</Badge>
                    <p className="text-sm mt-1">The expense for this memo has been approved and processed.</p>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            {selectedMemo && selectedMemo.status === 'Completed' && (
              <Button
                variant="outline"
                onClick={() => {
                  // Format the WhatsApp message
                  const driverCount = selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids)
                    ? selectedMemo.driver_ids.length
                    : 1;

                  let message = `Memo ${selectedMemo.memo_number} has been completed.\n` +
                    `From: ${selectedMemo.from_branch?.name || 'N/A'} → To: ${selectedMemo.to_branch?.name || 'N/A'}\n` +
                    `Vehicle: ${selectedMemo.vehicle?.registration_number || 'N/A'}\n` +
                    `Drivers: ${driverCount}\n` +
                    `Fuel Consumed: ${selectedMemo.fuel_consumed || 0} L\n` +
                    `BATA Amount: ₹${selectedMemo.bata_amount || 0}\n` +
                    `Salary Amount: ₹${selectedMemo.salary_amount || 0}\n` +
                    `Total Expense: ₹${selectedMemo.total_expense || 0}`;

                  // Add driver expense details if available
                  if (selectedMemo.driver_expenses && Array.isArray(selectedMemo.driver_expenses) && selectedMemo.driver_expenses.length > 0) {
                    message += "\n\nDriver Details:";
                    selectedMemo.driver_expenses.forEach((driver: any, index: number) => {
                      const driverNumber = driver.driver_number || selectedMemo.driver_numbers?.[index];
                      message += `\nDriver ${index + 1}${driverNumber ? ` (ID: ${driverNumber})` : ` (ID: ${driver.driver_id})`}:\n` +
                        `  BATA: ₹${driver.bata_amount || 0}\n` +
                        `  Salary: ₹${driver.salary_amount || 0}`;
                    });
                  }

                  // Encode the message for WhatsApp
                  const encodedMessage = encodeURIComponent(message);

                  // Open WhatsApp with the pre-filled message
                  window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                }}
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Send WhatsApp
              </Button>
            )}
            <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Receive Memo Dialog */}
      <Dialog open={isReceiveDialogOpen} onOpenChange={setIsReceiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Receive Memo</DialogTitle>
            <DialogDescription>
              Enter fuel consumption and expense details to receive memo {selectedMemo?.memo_number}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fuel">Fuel Used (Litres)</Label>
              <Input
                id="fuel"
                placeholder="Enter fuel used in litres"
                value={fuelLitres}
                onChange={(e) => setFuelLitres(e.target.value)}
                type="number"
              />
            </div>
            <div className="pt-4 border-t mt-4">
              <p className="text-sm text-muted-foreground mb-2">Enter BATA and salary details for the driver(s):</p>

              {/* Driver information */}
              {selectedMemo && selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && (
                <div className="mb-4 p-3 bg-muted rounded-md">
                  <p className="font-medium mb-2">Driver Information:</p>
                  {selectedMemo.driver_ids.map((driverId: any, index: number) => {
                    const driverNumber = selectedMemo.driver_numbers?.[index];
                    return (
                      <p key={driverId} className="text-sm">
                        Driver {index + 1}: {driverNumber ? `ID ${driverNumber}` : `ID ${driverId}`}
                      </p>
                    );
                  })}

                  {/* Split expenses toggle - only show if there are multiple drivers */}
                  {selectedMemo.driver_ids.length > 1 && (
                    <div className="mt-3 pt-2 border-t flex items-center justify-between">
                      <span className="text-sm font-medium">Split expenses between drivers</span>
                      <div className="flex items-center space-x-2">
                        <button
                          type="button"
                          className={`px-3 py-1 text-xs rounded-md ${!splitExpenses ? 'bg-primary text-white' : 'bg-muted-foreground/20'}`}
                          onClick={() => setSplitExpenses(false)}
                        >
                          Combined
                        </button>
                        <button
                          type="button"
                          className={`px-3 py-1 text-xs rounded-md ${splitExpenses ? 'bg-primary text-white' : 'bg-muted-foreground/20'}`}
                          onClick={() => setSplitExpenses(true)}
                        >
                          Split
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Combined expenses mode */}
              {!splitExpenses && (
                <div className="space-y-2 p-3 border rounded-md">
                  <p className="font-medium">Combined Expenses for All Drivers</p>
                  <div className="space-y-2">
                    <Label htmlFor="receive-bata-combined">BATA Amount (Total)</Label>
                    <Input
                      id="receive-bata-combined"
                      placeholder="Enter total BATA amount"
                      value={bataAmount}
                      onChange={(e) => setBataAmount(e.target.value)}
                      type="number"
                      required
                    />
                  </div>
                  <div className="space-y-2 mt-2">
                    <Label htmlFor="receive-salary-combined">Salary Amount (Total)</Label>
                    <Input
                      id="receive-salary-combined"
                      placeholder="Enter total salary amount"
                      value={salaryAmount}
                      onChange={(e) => setSalaryAmount(e.target.value)}
                      type="number"
                    />
                  </div>
                </div>
              )}

              {/* Split expenses mode */}
              {splitExpenses && (
                <>
                  {/* First driver */}
                  <div className="space-y-2 p-3 border rounded-md">
                    <p className="font-medium">
                      Driver 1 {selectedMemo && selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && selectedMemo.driver_ids.length > 0 &&
                        (selectedMemo.driver_numbers?.[0] ? `(ID: ${selectedMemo.driver_numbers[0]})` : `(ID: ${selectedMemo.driver_ids[0]})`)
                      }
                    </p>
                    <div className="space-y-2">
                      <Label htmlFor="receive-bata-1">BATA Amount</Label>
                      <Input
                        id="receive-bata-1"
                        placeholder="Enter BATA amount"
                        value={bataAmount}
                        onChange={(e) => setBataAmount(e.target.value)}
                        type="number"
                        required
                      />
                    </div>
                    <div className="space-y-2 mt-2">
                      <Label htmlFor="receive-salary-1">Salary Amount</Label>
                      <Input
                        id="receive-salary-1"
                        placeholder="Enter salary amount"
                        value={salaryAmount}
                        onChange={(e) => setSalaryAmount(e.target.value)}
                        type="number"
                      />
                    </div>
                  </div>

                  {/* Second driver (if applicable) */}
                  {selectedMemo && selectedMemo.driver_ids && Array.isArray(selectedMemo.driver_ids) && selectedMemo.driver_ids.length > 1 && (
                    <div className="space-y-2 p-3 border rounded-md mt-4">
                      <p className="font-medium">
                        Driver 2 {selectedMemo.driver_numbers?.[1] ? `(ID: ${selectedMemo.driver_numbers[1]})` : `(ID: ${selectedMemo.driver_ids[1]})`}
                      </p>
                      <div className="space-y-2">
                        <Label htmlFor="receive-bata-2">BATA Amount</Label>
                        <Input
                          id="receive-bata-2"
                          placeholder="Enter BATA amount"
                          value={bataAmount2}
                          onChange={(e) => setBataAmount2(e.target.value)}
                          type="number"
                        />
                      </div>
                      <div className="space-y-2 mt-2">
                        <Label htmlFor="receive-salary-2">Salary Amount</Label>
                        <Input
                          id="receive-salary-2"
                          placeholder="Enter salary amount"
                          value={salaryAmount2}
                          onChange={(e) => setSalaryAmount2(e.target.value)}
                          type="number"
                        />
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Total expense calculation */}
              <div className="pt-2 mt-4 border-t">
                <div className="flex justify-between font-medium">
                  <span>Total Expense:</span>
                  <span>₹{(
                    parseFloat(bataAmount || "0") +
                    parseFloat(salaryAmount || "0") +
                    parseFloat(bataAmount2 || "0") +
                    parseFloat(salaryAmount2 || "0")
                  ).toFixed(2)}</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Note: This only records the expense in the memo. The actual expense entry will be created in the Expense module.
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReceiveDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleReceive}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Receive Memo"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>


    </div>
  )
}

