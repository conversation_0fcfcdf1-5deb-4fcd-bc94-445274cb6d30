<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KPN Branch Management - Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    :root {
      --primary: #003A8C;
      --primary-hover: #002a66;
      --background: #f9f9f9;
      --card: #ffffff;
      --border: #e2e8f0;
      --muted: #64748b;
      --muted-foreground: #94a3b8;
      --destructive: #e11d48;
      --accent: #FFD100;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background);
      color: #1e293b;
      line-height: 1.5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 1rem;
    }
    
    .header {
      background-color: var(--primary);
      color: white;
      padding: 1rem;
      margin-bottom: 2rem;
    }
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: bold;
    }
    
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.25rem;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 0.5rem 1rem;
      cursor: pointer;
      transition: background-color 0.2s, color 0.2s;
      border: none;
    }
    
    .button-primary {
      background-color: var(--primary);
      color: white;
    }
    
    .button-primary:hover {
      background-color: var(--primary-hover);
    }
    
    .button-secondary {
      background-color: white;
      color: var(--primary);
    }
    
    .button-secondary:hover {
      background-color: #f1f5f9;
    }
    
    .button-outline {
      background-color: transparent;
      color: var(--primary);
      border: 1px solid var(--primary);
    }
    
    .button-outline:hover {
      background-color: rgba(0, 58, 140, 0.1);
    }
    
    .card {
      background-color: var(--card);
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      overflow: hidden;
      margin-bottom: 1rem;
    }
    
    .card-header {
      padding: 1rem;
      border-bottom: 1px solid var(--border);
    }
    
    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary);
    }
    
    .card-description {
      font-size: 0.875rem;
      color: var(--muted);
      margin-top: 0.25rem;
    }
    
    .card-content {
      padding: 1rem;
    }
    
    .card-footer {
      padding: 1rem;
      border-top: 1px solid var(--border);
      display: flex;
      justify-content: flex-end;
    }
    
    .grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 1rem;
    }
    
    @media (min-width: 640px) {
      .grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (min-width: 1024px) {
      .grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
    
    .info-item {
      margin-bottom: 0.5rem;
    }
    
    .info-label {
      font-weight: 500;
      margin-right: 0.5rem;
    }
    
    .info-value {
      color: var(--muted);
    }
    
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 200px;
    }
    
    .spinner {
      border: 3px solid rgba(0, 0, 0, 0.1);
      border-top-color: var(--primary);
      border-radius: 50%;
      width: 2rem;
      height: 2rem;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .alert {
      padding: 0.75rem;
      border-radius: 0.25rem;
      margin-bottom: 1rem;
      font-size: 0.875rem;
    }
    
    .alert-error {
      background-color: rgba(225, 29, 72, 0.1);
      color: var(--destructive);
    }
    
    .alert-success {
      background-color: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }
    
    .stat-card {
      background-color: var(--card);
      border-radius: 0.5rem;
      padding: 1.5rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }
    
    .stat-title {
      font-size: 0.875rem;
      color: var(--muted);
      margin-bottom: 0.5rem;
    }
    
    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--primary);
    }
    
    .stat-description {
      font-size: 0.75rem;
      color: var(--muted-foreground);
      margin-top: 0.5rem;
    }
    
    .badge {
      display: inline-flex;
      align-items: center;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 500;
      padding: 0.25rem 0.75rem;
    }
    
    .badge-success {
      background-color: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }
    
    .badge-warning {
      background-color: rgba(245, 158, 11, 0.1);
      color: #f59e0b;
    }
    
    .badge-error {
      background-color: rgba(225, 29, 72, 0.1);
      color: var(--destructive);
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-content">
      <div class="logo">KPN Branch Management</div>
      <button id="logout-button" class="button button-secondary">Sign Out</button>
    </div>
  </header>
  
  <div class="container">
    <div id="loading" class="loading">
      <div class="spinner"></div>
    </div>
    
    <div id="content" style="display: none;">
      <div class="grid" style="grid-template-columns: repeat(3, 1fr); margin-bottom: 2rem;">
        <div class="stat-card">
          <div class="stat-title">Total Parcels</div>
          <div class="stat-value">0</div>
          <div class="stat-description">Parcels managed by your branch</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-title">Branch Status</div>
          <div class="stat-value" id="branch-status">-</div>
          <div class="stat-description">Current operational status</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-title">Your Role</div>
          <div class="stat-value" id="user-role">-</div>
          <div class="stat-description">Your position in the branch</div>
        </div>
      </div>
      
      <div class="grid" style="grid-template-columns: repeat(2, 1fr);">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">User Information</h2>
            <p class="card-description">Your account details</p>
          </div>
          <div class="card-content">
            <div id="user-info">
              <!-- User info will be populated here -->
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">Branch Information</h2>
            <p class="card-description">Your assigned branch</p>
          </div>
          <div class="card-content">
            <div id="branch-info">
              <!-- Branch info will be populated here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize Supabase client
      const supabaseUrl = 'https://nekjeqxlwhfwyekeinnc.supabase.co';
      const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1J2ULLARZksO9wOv_Vd9xNvnPtz7APjfFfRMa5d388Y';
      
      if (typeof supabase === 'undefined') {
        alert('Supabase library not loaded. Please refresh the page.');
        return;
      }
      
      const client = supabase.createClient(supabaseUrl, supabaseKey);
      
      // DOM elements
      const loadingEl = document.getElementById('loading');
      const contentEl = document.getElementById('content');
      const userInfoEl = document.getElementById('user-info');
      const branchInfoEl = document.getElementById('branch-info');
      const branchStatusEl = document.getElementById('branch-status');
      const userRoleEl = document.getElementById('user-role');
      const logoutButton = document.getElementById('logout-button');
      
      // Check if user is logged in
      checkAuth();
      
      // Handle logout
      logoutButton.addEventListener('click', async function() {
        try {
          await client.auth.signOut();
          window.location.href = '/standalone-login.html';
        } catch (err) {
          console.error('Error signing out:', err);
          alert('Error signing out. Please try again.');
        }
      });
      
      async function checkAuth() {
        try {
          const { data, error } = await client.auth.getSession();
          
          if (error) {
            console.error('Error getting session:', error);
            redirectToLogin();
            return;
          }
          
          if (!data.session) {
            console.log('No active session found');
            redirectToLogin();
            return;
          }
          
          // User is authenticated, load dashboard data
          loadDashboardData(data.session.user);
        } catch (err) {
          console.error('Error checking authentication:', err);
          redirectToLogin();
        }
      }
      
      function redirectToLogin() {
        window.location.href = '/standalone-login.html';
      }
      
      async function loadDashboardData(user) {
        try {
          // Get user profile from public users table
          const { data: profile, error: profileError } = await client
            .from('users')
            .select('*')
            .eq('email', user.email)
            .single();
          
          if (profileError) {
            console.error('Error fetching user profile:', profileError);
            showError(userInfoEl, 'Error loading user profile');
            return;
          }
          
          // Display user info
          displayUserInfo(user, profile);
          
          // If user has a branch, get branch info
          if (profile.branch_id) {
            const { data: branch, error: branchError } = await client
              .from('branches')
              .select('*')
              .eq('branch_id', profile.branch_id)
              .single();
            
            if (branchError) {
              console.error('Error fetching branch:', branchError);
              showError(branchInfoEl, 'Error loading branch information');
            } else {
              displayBranchInfo(branch);
            }
          } else {
            branchInfoEl.innerHTML = '<p>You are not assigned to any branch.</p>';
          }
          
          // Show content
          loadingEl.style.display = 'none';
          contentEl.style.display = 'block';
        } catch (err) {
          console.error('Error loading dashboard data:', err);
          showError(document.body, 'Error loading dashboard data');
        }
      }
      
      function displayUserInfo(user, profile) {
        userInfoEl.innerHTML = `
          <div class="info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">${profile.name || user.user_metadata?.name || 'Not set'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email:</span>
            <span class="info-value">${user.email}</span>
          </div>
          <div class="info-item">
            <span class="info-label">User ID:</span>
            <span class="info-value">${profile.user_id}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Role:</span>
            <span class="info-value">${profile.role}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Last Login:</span>
            <span class="info-value">${getLastLoginTime(profile.login_history)}</span>
          </div>
        `;
        
        // Update role in stats
        userRoleEl.textContent = profile.role;
      }
      
      function displayBranchInfo(branch) {
        branchInfoEl.innerHTML = `
          <div class="info-item">
            <span class="info-label">Branch Name:</span>
            <span class="info-value">${branch.name}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Branch Code:</span>
            <span class="info-value">${branch.code}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Branch Type:</span>
            <span class="info-value">${branch.branch_type}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Status:</span>
            <span class="info-value">
              <span class="badge ${getBadgeClass(branch.status)}">${branch.status}</span>
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">Address:</span>
            <span class="info-value">${branch.address || 'Not set'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Phone:</span>
            <span class="info-value">${branch.phone || 'Not set'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Email:</span>
            <span class="info-value">${branch.email || 'Not set'}</span>
          </div>
        `;
        
        // Update branch status in stats
        branchStatusEl.textContent = branch.status;
        branchStatusEl.innerHTML = `<span class="badge ${getBadgeClass(branch.status)}">${branch.status}</span>`;
      }
      
      function showError(element, message) {
        element.innerHTML = `<div class="alert alert-error">${message}</div>`;
      }
      
      function getLastLoginTime(loginHistory) {
        if (!loginHistory) return 'Never';
        
        try {
          const history = typeof loginHistory === 'string' 
            ? JSON.parse(loginHistory) 
            : loginHistory;
          
          if (history.last_login) {
            const date = new Date(history.last_login);
            return date.toLocaleString();
          }
        } catch (err) {
          console.error('Error parsing login history:', err);
        }
        
        return 'Unknown';
      }
      
      function getBadgeClass(status) {
        switch (status) {
          case 'Active':
            return 'badge-success';
          case 'Inactive':
            return 'badge-error';
          default:
            return 'badge-warning';
        }
      }
    });
  </script>
</body>
</html>
