-- Create parcel_status_history table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.parcel_status_history (
  id                SERIAL    PRIMARY KEY,
  parcel_id         INT       NOT NULL REFERENCES parcels(parcel_id) ON DELETE CASCADE,
  status            parcel_status NOT NULL, -- Using the same enum type as parcels.current_status
  timestamp         TIMESTAMPTZ DEFAULT now(),
  location          TEXT,
  remarks           TEXT,
  updated_by        UUID      REFERENCES auth.users(id) ON DELETE SET NULL,
  branch_id         INT       REFERENCES branches(branch_id) ON DELETE SET NULL,
  vehicle_id        INT       REFERENCES vehicles(vehicle_id) ON DELETE SET NULL
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS parcel_status_history_parcel_id_idx ON parcel_status_history(parcel_id);
CREATE INDEX IF NOT EXISTS parcel_status_history_timestamp_idx ON parcel_status_history(timestamp);

-- Create a trigger function to automatically record status changes
CREATE OR REPLACE FUNCTION record_parcel_status_change()
RETURNS TRIGGER AS $$
DECLARE
  v_branch_id INT;
BEGIN
  -- The trigger condition already checks if status has changed
  -- so we don't need to check again here

  -- Try to determine the branch where the status change occurred
  -- For now, we'll use the delivery_branch_id for simplicity
  v_branch_id := NEW.delivery_branch_id;

  -- Insert a record into the status history table
  INSERT INTO parcel_status_history (
    parcel_id,
    status,
    timestamp,
    location,
    branch_id,
    remarks
  ) VALUES (
    NEW.parcel_id,
    NEW.current_status, -- Both are parcel_status type, so no cast needed
    NOW(),
    CASE
      WHEN NEW.current_status = 'Booked'::parcel_status THEN
        (SELECT name FROM branches WHERE branch_id = NEW.sender_branch_id)
      WHEN NEW.current_status = 'In Transit'::parcel_status THEN
        'En route from ' || (SELECT name FROM branches WHERE branch_id = NEW.sender_branch_id) ||
        ' to ' || (SELECT name FROM branches WHERE branch_id = NEW.delivery_branch_id)
      WHEN NEW.current_status = 'To Be Delivered'::parcel_status THEN
        (SELECT name FROM branches WHERE branch_id = NEW.delivery_branch_id)
      WHEN NEW.current_status = 'Delivered'::parcel_status THEN
        (SELECT name FROM branches WHERE branch_id = NEW.delivery_branch_id)
      ELSE 'System update'
    END,
    v_branch_id,
    'Status updated to ' || NEW.current_status::text -- Cast to text for concatenation
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically record status changes
DROP TRIGGER IF EXISTS record_parcel_status_change_trigger ON parcels;

CREATE TRIGGER record_parcel_status_change_trigger
AFTER UPDATE OF current_status ON parcels
FOR EACH ROW
WHEN (OLD.current_status IS DISTINCT FROM NEW.current_status)
EXECUTE FUNCTION record_parcel_status_change();

-- Backfill existing parcels with at least a "Booked" status entry
INSERT INTO parcel_status_history (
  parcel_id,
  status,
  timestamp,
  location,
  branch_id,
  remarks
)
SELECT
  p.parcel_id,
  'Booked'::parcel_status,
  p.booking_datetime,
  b.name,
  p.sender_branch_id,
  'Initial booking'
FROM
  parcels p
  LEFT JOIN branches b ON p.sender_branch_id = b.branch_id
WHERE
  NOT EXISTS (
    SELECT 1 FROM parcel_status_history psh
    WHERE psh.parcel_id = p.parcel_id AND psh.status = 'Booked'::parcel_status
  );

-- Add current status entries for parcels that aren't in "Booked" status
INSERT INTO parcel_status_history (
  parcel_id,
  status,
  timestamp,
  location,
  branch_id,
  remarks
)
SELECT
  p.parcel_id,
  p.current_status,
  COALESCE(p.actual_delivery_date, NOW()),
  CASE
    WHEN p.current_status = 'In Transit'::parcel_status THEN
      'En route from ' || (SELECT name FROM branches WHERE branch_id = p.sender_branch_id) ||
      ' to ' || (SELECT name FROM branches WHERE branch_id = p.delivery_branch_id)
    WHEN p.current_status = 'To Be Delivered'::parcel_status THEN
      (SELECT name FROM branches WHERE branch_id = p.delivery_branch_id)
    WHEN p.current_status = 'Delivered'::parcel_status THEN
      (SELECT name FROM branches WHERE branch_id = p.delivery_branch_id)
    ELSE 'System update'
  END,
  p.delivery_branch_id,
  'Status updated to ' || p.current_status
FROM
  parcels p
WHERE
  p.current_status != 'Booked'::parcel_status AND
  NOT EXISTS (
    SELECT 1 FROM parcel_status_history psh
    WHERE psh.parcel_id = p.parcel_id AND psh.status = p.current_status
  );
