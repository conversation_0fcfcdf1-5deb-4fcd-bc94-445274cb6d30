import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { supabase } from "@/lib/supabase";

// GET /api/expenses
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const category = url.searchParams.get("category");
    const branch_id = url.searchParams.get("branch_id");
    const from_date = url.searchParams.get("from_date");
    const to_date = url.searchParams.get("to_date");
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    // Build query
    let query = supabase
      .from("expenses")
      .select(`
        *,
        branch:branches(name, code),
        submitter:users!submitted_by(name),
        approver:users!approved_by(name),
        rejecter:users!rejected_by(name)
      `);

    // Add filters if provided
    if (status) {
      query = query.eq("approval_status", status);
    }

    if (category) {
      query = query.eq("category", category);
    }

    if (branch_id) {
      query = query.eq("branch_id", branch_id);
    }

    if (from_date) {
      query = query.gte("submitted_at", from_date);
    }

    if (to_date) {
      query = query.lte("submitted_at", to_date);
    }

    // Add pagination
    query = query.order("submitted_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Execute query
    const { data: expenses, error, count } = await query;

    if (error) {
      console.error("Error fetching expenses:", error);
      return NextResponse.json({ error: "Failed to fetch expenses" }, {
        status: 500,
      });
    }

    return NextResponse.json({ expenses, count });
  } catch (error: any) {
    console.error("Error in GET /api/expenses:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/expenses
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.category || !body.amount || !body.branch_id) {
      return NextResponse.json(
        { error: "Missing required fields: category, amount, branch_id" },
        { status: 400 }
      );
    }

    // Get user ID from the users table
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("user_id")
      .eq("auth_id", session.user.id)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      return NextResponse.json({ error: "Failed to fetch user" }, { status: 500 });
    }

    const userId = userData?.user_id;

    // Create expense
    const { data: expense, error } = await supabase
      .from("expenses")
      .insert({
        ...body,
        submitted_by: userId,
        submitted_at: new Date().toISOString(),
        approval_status: "Pending"
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating expense:", error);
      return NextResponse.json({ error: "Failed to create expense" }, {
        status: 500,
      });
    }

    return NextResponse.json({ expense });
  } catch (error: any) {
    console.error("Error in POST /api/expenses:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
