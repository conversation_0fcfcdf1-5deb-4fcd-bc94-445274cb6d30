import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/vehicles
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const branch_id = url.searchParams.get("branch_id");

    // Build query
    let query = supabase
      .from("vehicles")
      .select("*");

    // Add filters if provided
    if (status) {
      query = query.eq("current_status", status);
    }

    if (branch_id) {
      query = query.eq("branch_id", branch_id);
    }

    // Execute query
    const { data: vehicles, error } = await query.order("registration_number");

    if (error) {
      console.error("Error fetching vehicles:", error);
      return NextResponse.json({ error: "Failed to fetch vehicles" }, {
        status: 500,
      });
    }

    return NextResponse.json({ vehicles });
  } catch (error: any) {
    console.error("Error in GET /api/vehicles:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

// POST /api/vehicles
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.registration_number) {
      return NextResponse.json(
        { error: "Missing required field: registration_number" },
        { status: 400 },
      );
    }

    // Create vehicle
    const { data: vehicle, error } = await supabase
      .from("vehicles")
      .insert({
        ...body,
        created_by: body.created_by || null,
        updated_by: body.updated_by || null,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating vehicle:", error);
      return NextResponse.json({ error: "Failed to create vehicle" }, {
        status: 500,
      });
    }

    return NextResponse.json({ vehicle });
  } catch (error: any) {
    console.error("Error in POST /api/vehicles:", error);
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
