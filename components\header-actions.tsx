"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON>ltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Bell, Search } from "lucide-react"
import { NotificationsDialog } from "@/components/notifications-dialog"

export function HeaderActions() {
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)

  return (
    <TooltipProvider>
      <div className="flex items-center space-x-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9 relative"
              onClick={() => setIsNotificationsOpen(true)}
            >
              <Bell className="h-4 w-4 text-primary" />
              <span className="sr-only">Notifications</span>
              <span className="absolute right-1 top-1 h-2 w-2 rounded-full bg-red-600" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Notifications</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-9 w-9"
              onClick={(e) => {
                e.preventDefault()
                const event = new KeyboardEvent("keydown", {
                  key: "j",
                  ctrlKey: true,
                })
                document.dispatchEvent(event)
              }}
            >
              <Search className="h-4 w-4 text-primary" />
              <span className="sr-only">Search</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Search (⌘J)</p>
          </TooltipContent>
        </Tooltip>

        <NotificationsDialog
          open={isNotificationsOpen}
          onOpenChange={setIsNotificationsOpen}
        />
      </div>
    </TooltipProvider>
  )
}