# WhatsApp Notification System

This document explains how the WhatsApp notification system works in the KPN Parcel Service application.

## Overview

The system automatically sends WhatsApp notifications to parcel senders when the status of their parcels changes. This keeps customers informed about their parcels throughout the delivery process.

## Architecture

The notification system consists of:

1. **Supabase Edge Function**: A serverless function that sends WhatsApp notifications
2. **Database Webhook**: Triggers the edge function when a parcel's status changes
3. **WhatsApp API Integration**: Uses WATI's API to send template messages
4. **Notification Logging**: Records all notification attempts in the database

## Notification Flow

1. When a parcel's status is updated in the database, a webhook is triggered
2. The webhook calls the `send-whatsapp-notification` edge function
3. The edge function:
   - Retrieves the parcel details from the database
   - Selects the appropriate WhatsApp template based on the new status
   - Formats the sender's phone number
   - Populates the template with parcel-specific data
   - Sends the notification via the WATI API
   - Logs the result in the `notification_logs` table

## Status-to-Template Mapping

| Parcel Status | WhatsApp Template | Description |
|---------------|-------------------|-------------|
| Booked | booking_confirmed | Initial confirmation when a parcel is booked |
| In Transit | in_transit_2 | Updates when a parcel is in transit between branches |
| To Be Received | arrived_at_hub | Notification when a parcel arrives at a hub |
| To Be Delivered | arrived_at_hub | Notification when a parcel is ready for delivery |
| Delivered | delivered | Final confirmation when a parcel is delivered |

## Template Variables

Each template uses specific variables that are populated with parcel data:

### booking_confirmed
- var1: Sender Name
- var2: LR Number
- var3: Booking Date
- var4: Pickup Address (Branch Name)

### in_transit_2
- var1: Sender Name
- var2: LR Number
- var3: Last Scan Location
- var4: Scan Date/Time
- var5: Expected Delivery Date

### arrived_at_hub
- var1: Sender Name
- var2: LR Number
- var3: Hub Name
- var4: Arrival Date/Time

### delivered
- var1: Sender Name
- var2: LR Number
- var3: Delivery Date/Time
- var4: Delivery Address

## Notification Logs

All notification attempts are logged in the `notification_logs` table with the following information:

- Parcel ID
- Notification type (template name)
- Recipient phone number
- Parcel status at the time of notification
- Success/failure status
- Error message (if any)
- API response data

## Troubleshooting

If notifications aren't being sent:

1. Check the Supabase Edge Function logs in the dashboard
2. Verify that the WATI_API_TOKEN is set correctly
3. Ensure the webhook is properly configured and enabled
4. Check that the sender's phone number is correctly formatted in the database
5. Review the `notification_logs` table for error messages

## Monitoring and Analytics

You can monitor notification performance using the following SQL queries:

```sql
-- Count of notifications by status
SELECT status, COUNT(*) 
FROM notification_logs 
GROUP BY status;

-- Success rate by notification type
SELECT notification_type, 
       COUNT(*) AS total,
       SUM(CASE WHEN success THEN 1 ELSE 0 END) AS successful,
       ROUND(100.0 * SUM(CASE WHEN success THEN 1 ELSE 0 END) / COUNT(*), 2) AS success_rate
FROM notification_logs 
GROUP BY notification_type;

-- Recent failures
SELECT * FROM notification_logs 
WHERE NOT success 
ORDER BY sent_at DESC 
LIMIT 10;
```

## Future Enhancements

Potential improvements to the notification system:

1. Add recipient notifications (in addition to sender notifications)
2. Implement notification preferences (opt-in/opt-out)
3. Add more notification types (e.g., delivery delays, payment reminders)
4. Create a notification dashboard for administrators
