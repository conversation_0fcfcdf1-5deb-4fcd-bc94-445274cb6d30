"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Search, Package, Truck, CheckCircle, Clock, AlertCircle } from "lucide-react"
import { ParcelDeliveryDialog } from "./parcel-delivery-dialog"

interface DeliveryEligibleParcel {
  parcel_id: number
  lr_number: string
  sender_name: string
  recipient_name: string
  recipient_phone?: string
  delivery_branch_id: number
  number_of_items: number
  total_received_items: number
  current_status: string
  booking_datetime: string
  destination_branch_name: string
  destination_branch_code: string
  all_items_received: boolean
  ready_for_delivery: boolean
}

export function ParcelManagementWithDelivery() {
  const { toast } = useToast()
  
  // State management
  const [parcels, setParcels] = useState<DeliveryEligibleParcel[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedParcel, setSelectedParcel] = useState<DeliveryEligibleParcel | null>(null)
  const [isDeliveryDialogOpen, setIsDeliveryDialogOpen] = useState(false)
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalParcels, setTotalParcels] = useState(0)
  const pageSize = 20

  // Load delivery-eligible parcels
  const loadParcels = async (page = 1, search = "") => {
    setIsLoading(true)
    
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      })
      
      if (search.trim()) {
        params.append('search', search.trim())
      }

      const response = await fetch(`/api/parcels/delivery-eligible?${params.toString()}`)
      const data = await response.json()

      if (response.ok) {
        setParcels(data.parcels || [])
        setTotalPages(data.pagination?.totalPages || 1)
        setTotalParcels(data.pagination?.total || 0)
        setCurrentPage(page)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to load parcels",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error loading parcels:', error)
      toast({
        title: "Error",
        description: "Failed to load parcels. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load parcels on component mount
  useEffect(() => {
    loadParcels()
  }, [])

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1)
    loadParcels(1, searchTerm)
  }

  // Handle search on Enter key
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // Handle delivery button click
  const handleDeliveryClick = (parcel: DeliveryEligibleParcel) => {
    setSelectedParcel(parcel)
    setIsDeliveryDialogOpen(true)
  }

  // Handle delivery completion
  const handleDeliveryComplete = () => {
    // Refresh the parcels list
    loadParcels(currentPage, searchTerm)
    setSelectedParcel(null)
  }

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      loadParcels(newPage, searchTerm)
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Booked': return 'bg-blue-100 text-blue-800'
      case 'Loaded': return 'bg-yellow-100 text-yellow-800'
      case 'Received': return 'bg-green-100 text-green-800'
      case 'Delivered': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Booked': return <Package className="h-3 w-3" />
      case 'Loaded': return <Truck className="h-3 w-3" />
      case 'Received': return <CheckCircle className="h-3 w-3" />
      case 'Delivered': return <CheckCircle className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Parcel Management</h2>
          <p className="text-muted-foreground">
            Manage parcels ready for delivery at your branch
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Parcels</CardTitle>
          <CardDescription>
            Search by LR number, sender name, or recipient name
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <div className="flex-1">
              <Label htmlFor="search" className="sr-only">Search</Label>
              <Input
                id="search"
                placeholder="Enter LR number, sender, or recipient name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={handleSearchKeyPress}
              />
            </div>
            <Button onClick={handleSearch} disabled={isLoading}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {totalParcels > 0 ? (
            <>Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalParcels)} of {totalParcels} parcels</>
          ) : (
            "No parcels found"
          )}
        </div>
        <Button 
          variant="outline" 
          onClick={() => loadParcels(currentPage, searchTerm)}
          disabled={isLoading}
        >
          Refresh
        </Button>
      </div>

      {/* Parcels List */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery-Ready Parcels</CardTitle>
          <CardDescription>
            Parcels that have been received and are ready for delivery
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading parcels...
            </div>
          ) : parcels.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No delivery-ready parcels found</p>
            </div>
          ) : (
            <ScrollArea className="h-[600px]">
              <div className="space-y-4">
                {parcels.map((parcel) => (
                  <Card key={parcel.parcel_id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-lg">{parcel.lr_number}</span>
                          <Badge className={`${getStatusColor(parcel.current_status)} flex items-center space-x-1`}>
                            {getStatusIcon(parcel.current_status)}
                            <span>{parcel.current_status}</span>
                          </Badge>
                          {parcel.all_items_received && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              All Items Received
                            </Badge>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <p><span className="font-medium">Sender:</span> {parcel.sender_name}</p>
                            <p><span className="font-medium">Recipient:</span> {parcel.recipient_name}</p>
                            {parcel.recipient_phone && (
                              <p><span className="font-medium">Phone:</span> {parcel.recipient_phone}</p>
                            )}
                          </div>
                          <div>
                            <p><span className="font-medium">Items:</span> {parcel.total_received_items} of {parcel.number_of_items}</p>
                            <p><span className="font-medium">Destination:</span> {parcel.destination_branch_name}</p>
                            <p><span className="font-medium">Booked:</span> {new Date(parcel.booking_datetime).toLocaleDateString()}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="ml-4">
                        {parcel.ready_for_delivery ? (
                          <Button 
                            onClick={() => handleDeliveryClick(parcel)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Deliver
                          </Button>
                        ) : (
                          <div className="text-center">
                            <AlertCircle className="h-6 w-6 mx-auto mb-1 text-yellow-500" />
                            <p className="text-xs text-muted-foreground">
                              {parcel.all_items_received ? "Not at destination" : "Incomplete items"}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1 || isLoading}
          >
            Previous
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNum)}
                  disabled={isLoading}
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>
          
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}

      {/* Delivery Dialog */}
      <ParcelDeliveryDialog
        isOpen={isDeliveryDialogOpen}
        onClose={() => setIsDeliveryDialogOpen(false)}
        parcel={selectedParcel}
        onDeliveryComplete={handleDeliveryComplete}
      />
    </div>
  )
}
