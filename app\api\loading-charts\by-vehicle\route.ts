import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/loading-charts/by-vehicle?registration_number=ABC123
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const registration_number = url.searchParams.get("registration_number");

    if (!registration_number) {
      return NextResponse.json(
        { error: "Missing required parameter: registration_number" },
        { status: 400 },
      );
    }

    // Get user's branch for validation
    const { data: { user } } = await routeHandlerClient.auth.getUser();
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id, role")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Find the vehicle first
    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select("vehicle_id, registration_number, branch_id")
      .eq("registration_number", registration_number.toUpperCase())
      .single();

    if (vehicleError || !vehicle) {
      return NextResponse.json(
        { error: "Vehicle not found" },
        { status: 404 },
      );
    }

    // Note: Vehicles are now accessible across all branches
    // No branch-based access control needed

    // Get loading charts for this vehicle that have items to receive
    const { data: loadingCharts, error: chartsError } = await supabase
      .from("loading_charts")
      .select(`
        chart_id,
        chart_number,
        vehicle_id,
        destination_branch_id,
        loading_type,
        status,
        created_at,
        vehicle:vehicles(registration_number, vehicle_type),
        destination_branch:branches!destination_branch_id(name, code)
      `)
      .eq("vehicle_id", vehicle.vehicle_id)
      .in("status", ["Created", "In Progress"])
      .order("created_at", { ascending: false });

    if (chartsError) {
      console.error("Error fetching loading charts:", chartsError);
      return NextResponse.json(
        { error: "Failed to fetch loading charts" },
        { status: 500 },
      );
    }

    if (!loadingCharts || loadingCharts.length === 0) {
      return NextResponse.json(
        { error: "No active loading charts found for this vehicle" },
        { status: 404 },
      );
    }

    // Check if any of these charts have items that can be received
    const chartIds = loadingCharts.map((chart) => chart.chart_id);

    const { data: pendingItems, error: itemsError } = await supabase
      .from("loading_chart_items")
      .select("chart_id")
      .in("chart_id", chartIds)
      .eq("status", "Pending");

    if (itemsError) {
      console.error("Error checking pending items:", itemsError);
      return NextResponse.json(
        { error: "Failed to check pending items" },
        { status: 500 },
      );
    }

    // Filter charts that have pending items
    const chartsWithPendingItems = loadingCharts.filter((chart) =>
      pendingItems?.some((item) => item.chart_id === chart.chart_id)
    );

    if (chartsWithPendingItems.length === 0) {
      return NextResponse.json(
        { error: "No parcels available to receive from this vehicle" },
        { status: 404 },
      );
    }

    return NextResponse.json({
      charts: chartsWithPendingItems,
      vehicle: {
        vehicle_id: vehicle.vehicle_id,
        registration_number: vehicle.registration_number,
      },
    });
  } catch (error: any) {
    console.error("Error in GET /api/loading-charts/by-vehicle:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
